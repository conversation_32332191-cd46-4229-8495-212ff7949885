import { Component } from "react";
import Sidebar from "../componets/Sidebar";
import Footer from "../componets/Footer";
import Header from "../componets/Header";
import { connect } from "react-redux";
import IdUpdate from "../componets/onBoard/IdUpdate";
import {decrementCounter,incrementCounter,step1,step2,step3,step4,step5,} from "../redux/actions/counterActions";
import { login, logOut } from "../redux/actions/loginActions";
import { setId } from "../redux/actions/counterActions";
import * as constants from '../constants/constants'

class UpdateIdPage extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      activeKey: "Indents",
    };
  }

  componentDidMount(){
    let role = localStorage.getItem("role")
      if(!(role === 'isNonTmlSPOC' || role === 'isNonTmlManager' || role === 'isTmlManager' || role === 'isSuperUser')){
          window.location.replace(constants.FRONTEND_URL + '/login');
      }
  }

  render() {
    const isLoggedIn = this.props.loginData.isLogin;
    const createdBy = this.props.loginData.userRole;
    if (!isLoggedIn) {
      window.location.replace(constants.FRONTEND_URL + '/login');
      return null 
    } 
    else {
      return (
        <IdUpdate />
      );
    }
  }
}

const mapStateToProps = (state) => ({
  counter: state.counter.value,
  Step1Taskid: state.counter.step1.taskIstanceIdStep1,
  Step1processId: state.counter.step1.processIstanceIdStep1,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
});

const mapDispatchToProps = {
  incrementCounter: incrementCounter,
  decrementCounter: decrementCounter,
  step1: step1,
  step2: step2,
  step3: step3,
  step4: step4,
  step5: step5,
  login:login,
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(UpdateIdPage);
