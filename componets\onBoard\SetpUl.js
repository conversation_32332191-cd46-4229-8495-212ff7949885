import React, { Component } from "react";
import Router from "next/router";
import { getAPIResponse } from "../../constants/Utils";
import * as config from "../../constants/config";
import { setId } from "../../redux/actions/counterActions";
import { connect } from "react-redux";

export class SetpUl extends Component {
  constructor(props) {
    super(props);
    this.state = {
      step: "",
      msg: "",
      requestCurrentStatus: "",
      requestType : "",
      requestFinalstatus : "",
      requestCreationType:""
    };
  }

  componentDidMount() {
    this.callCount();
  }

  callCount = () => {
    let id = this.props.requestId;
    if (id != "") {
      getAPIResponse(`${config.getDetailsByRequestId}/${id}`, "GET" , {})
      .then((response)=>{
        if(response.status === 500){
          console.log("getDetailsByRequestId() in requestApproval",response)
          return null
        }
        else{
          return response.json()
        }  
      })
      .then((data)=>{
        if(data !== null){
          if(data.status === 1){
            this.setState({
                  requestCurrentStatus: data.data.requestCurrentState.id,
                  requestType : data.data.requestCurrentState.requestTypeName,
                  requestFinalstatus:data.data.requestFinalState.id,
                  requestCreationType:data.data.requestType,
            });
          }
          else {
            console.log("callCount() in stepUI",data)
          }
        }
        else {
          console.log("callCount() in stepUI",data)
        }
      })
      .catch((error)=>{
        console.log("callCount() in stepUI",error)
      })
    }
  };

  render() {
    let step = this.state.step;
    if(this.state.requestCreationType == 'Create'){
      return (
        <div className="">
          <ul className="list-unstyled multi-steps">
            <li className={ this.state.requestCurrentStatus === 1 ? this.state.requestFinalstatus === 3 || this.state.requestFinalstatus === 4 ? "is-disable" :"is-active" : null}>
              <label>OTP Verification</label>
            </li>
            <li className={this.state.requestCurrentStatus === 2 ? this.state.requestFinalstatus === 3 || this.state.requestFinalstatus === 4 ? "is-disable" :"is-active" : null}>
              <label >{this.state.requestCurrentStatus === 2 ? this.state.requestFinalstatus === 4 ? 'NDA Form Rejected From User' : 'NDA Acceptance From User':'NDA Acceptance From User'}</label>
              </li>
            <li className={this.state.requestCurrentStatus === 3 ? this.state.requestFinalstatus === 3 || this.state.requestFinalstatus === 4 ? "is-disable" :"is-active" : null}>
              <label>{this.state.requestCurrentStatus === 3 ? this.state.requestFinalstatus === 4 ? 'NDA Form Rejected From Manager' : 'NDA Acceptance From Manager':'NDA Acceptance From Manager'}</label>
            </li>
            <li className={this.state.requestCurrentStatus === 6 ? "is-disable" :  this.state.requestCurrentStatus === 4 ? "is-active" : null}>
              <label>Domain ID Creation</label>
              </li>
          </ul>
        </div>
      );
    }
    else if (this.state.requestCreationType == 'Update') {
      return (
        <div className="">
          <ul className="list-unstyled multi-steps">
            <li className={this.state.requestFinalstatus === 3 ? "is-disable" : null}>
              <label>{this.state.requestFinalstatus === 3 ? "Request Cancelled":"Request Created"}</label>
            </li>
            <li className={this.state.requestCurrentStatus === 7 ? this.state.requestFinalstatus === 4 ? "is-disable" : "is-active":null}>
              <label>{this.state.requestCurrentStatus === 7 && this.state.requestFinalstatus === 4 ? "Request Reject By Manager"  : "Approval pending from Manager"}</label>
              </li>
            <li className={this.state.requestCurrentStatus === 8 ? "is-active" : this.state.requestCurrentStatus === 10 ? "is-disable" : null}>
              <label>{this.state.requestCurrentStatus === 8 ? "Update Domain ID" : this.state.requestCurrentStatus === 10 ? "Updations Failed" : "Update Domain ID"}</label>
            </li>
          </ul>
        </div>
      );
    }
    else {
      return null
    }
      
  }
}

const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  baseUrl: state.counter.baseUrl,
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(SetpUl);
