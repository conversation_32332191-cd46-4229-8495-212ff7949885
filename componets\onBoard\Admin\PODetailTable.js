import { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { Tabs, Tab, ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form } from 'react-bootstrap';
import { login, logOut } from "../../../redux/actions/loginActions";
import BootstrapTable from 'react-bootstrap-table-next';
import { ReactNotifications, Store } from "react-notifications-component";
import { MasterModalType, MasterType, RequestType, sizePerPage,admin_module,admin_action, admin_master_list,admin_master,admin_add_master,admin_update_master } from "../../../constants/constants";
import paginationFactory from 'react-bootstrap-table2-paginator';
import ToolkitProvider, { Search } from "react-bootstrap-table2-toolkit";
import { getAPIResponse } from '../../../constants/Utils';
import * as config from '../../../constants/config'
import AdminTable from './AdminTable';
import { Select,Switch } from 'antd'
import { FaRegEdit,FaArrowLeft,FaArrowRight,FaPlus  } from "react-icons/fa";
import { AiOutlineClose } from "react-icons/ai";
import { AiOutlineSearch } from "react-icons/ai";
import { successToast, errorToast } from "../../../toast"
import { PLEASE_ENTER_REQUIRED_FIELD, SOMETHING_WENT_WRONG,PLEASE_SELECT_VENDOR_COMPANY, PLEASE_SELECT_REGION } from '../../../constants/message';
const { SearchBar } = Search;


class PODetailTable extends Component {

  static getInitialProps({ store }) { }
  constructor(props) {
    super(props);
    this.state = {
      activeKey: "Indents",
      defaultHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "vendorCode", text: "Vendor ID", sort: true },
        { dataField: "vendorName", text: "Vendor Name", sort: true },
        { dataField: "isGroupNDASigned", text: "Group NDA", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      vendorHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "vendorCode", text: "Vendor ID", sort: true },
        { dataField: "vendorName", text: "Vendor Name", sort: true },
        {dataField: "isGroupNDASigned", text: "Group NDA", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      designationHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "designationCode", text: "Designation ID", sort: true },
        { dataField: "designationName", text: "Designation Name", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      skillHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "skillCode", text: "Skill ID", sort: true },
        { dataField: "skillName", text: "Skill Name", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      towerHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "towerCode", text: "Tower ID", sort: true },
        { dataField: "towerName", text: "Tower Name", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      projectHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "vendor", text: "Vendor", sort: true },
        { dataField: "projectCode", text: "Project ID", sort: true },
        { dataField: "projectName", text: "Project Name", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      tmlRegionHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "regionCode", text: "Region ID", sort: true },
        { dataField: "regionName", text: "Region Name", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      tmlOfficeHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "region", text: "Region Name", sort: true },
        { dataField: "officeCode", text: "Office ID", sort: true },
        { dataField: "officeName", text: "Office Name", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      departmentHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "departmentCode", text: "Department ID", sort: true },
        { dataField: "departmentName", text: "Department Name", sort: true },
        { dataField: "empType", text: "Department Type", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      poHeaders: [   
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "po_id", text: "PO-NO.", sort: true }, 
        { dataField: "po_start_date", text: "Start Date", sort: true }, 
        { dataField: "po_end_date", text: "End Date", sort: true },  
        { dataField: "po_resource_count", text: "Resource Count", sort: true },
        { dataField: "po_comp_code", text: "Company Code", sort: true },       
        { dataField: "po_status", text: "Status", sort: true },      
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],


      currentPageData: [   
        {
          srno: 1,
          id: "PO1234",
          startDate: "2023-08-01",
          endDate: "2023-08-10",
          count: 25,
          status: "Active",
          createdBy: "Admin",
          updatedBy: "Manager",
        },
        {
          srno: 2,
          id: "PO5678",
          startDate: "2023-08-05",
          endDate: "2023-08-15",
          count: 40,
          status: "Inactive",
          createdBy: "User1",
          updatedBy: "User2",
        }
      ],

      totalRecords: 2,
      prev: null,
      next: null,
      pages: 1,
      currentPage: 0,
      currentPageData: [],
      sizePerPage: sizePerPage,
      totalSize: 0,
      searchText: '',
      selectOptions: MasterType,
      requestType: "Vendor",
      selectedValue: "vendor",
      vendorOption: [],
      designationList: [],
      project: [],
      tmlLocationData: [],
      tmlRegionData: [],
      skillData: [],
      towerData: "",
      vendor: null,
      prev: null,
      next: null,
      pages: 1,
      saveDisabled: true,
      isSearching: false,
      totalRecords: 0,
      showModal: false,
      selectedModalValue: null,
      poId: "",
        buCompanyCode: null,
        startDate: "",
        endDate: "",
        createdBy: "",
        updatedBy: "",
        status: "",
        count: "",
        isFieldsDisabled: true, 
  buCompanyCodeList: [],
    selectModalOptions: MasterModalType,
      isMasterEnable: true,
      masterModalName:'',
      masterModalCode:'',
      masterModalVendor:'',
      masterModalType:'add',
      vendorMasterList:[],
      selectedVendor:{},
      regionMasterList:[],
      selectedRegion:{},
      isMasterSelectionDisabled:false,
      invalidAddButton:[],
      prevMasterData:{},
      isGroupNDASigned:false,
      masterNameErrorMessage:"",
      masterCodeErrorMessage:"",
      projectErrorMessage:"",
      hideSelect:true,
      isLoading:false,
    //   saveDisabled:false,
      isEnable:['vendor','region','office'],
      departmentType:[
        {
          value: 'IT',
          label: 'IT'
        },
        {
          value: 'NONIT',
          label: 'Non IT'
        }
      ],
      selectedDepartment:'IT'
    }
    this.updateMaster.bind(this); 
  }

  componentDidMount() {
    this.getMasterList()
    // this.getMasterList({"master": "vendor","action": admin_master_list,"module": admin_master })
    // this.getMasterList({"master": "tmlRegion","action": admin_master_list,"module": admin_master })
  }

  setDropDownList=(state,defaultSelect,masterData) =>{
    let masterList = []
    if (masterData.length > 0){
      masterData.forEach((element) => {
        if (element['isActive']) {
          if (state == 'vendorMasterList'){
            masterList.push({
              value: element.vendorCode,
              label: element.vendorName
            })
          }
          else{
            masterList.push({
              value: element.regionCode,
              label: element.regionName
            })
          }
        }
      })
      this.setState({ [state]: masterList, [defaultSelect]: masterList[0].value })
    }
  }
  getMasterList = () => {
    this.setState({
      isLoading : true
    })
    
    getAPIResponse(config.PoMasterList, "GET", {})
      .then((response) => {
        if (!response.ok) {
          console.log("getMasterList() in Request", response)
          return null
        }
        else {
          return response.json()
        }
      }).then((data) => {
        if (data !== null && data.status === 1) {
          let masterData = data.data.results
          console.log(masterData)
          masterData.forEach((element,index) => {
          element['srno'] = (this.state.pages - 1) * 10 + index + 1
            if(element['po_status'] == 9){
                element['po_status'] = 'Active'
            }  
            element["Action"] = (
            <FaRegEdit onClick={() => {this.updateMaster(element, this.state.selectedValue, element.status);}}style={{ display: "flex", alignItems: "center", cursor: "pointer" }}size={"1.5rem"}fill="#6392c5"/>);});


          this.setState({
            currentPageData: masterData,
            prev: data.data.previous != null,
            next: data.data.next != null,
            totalRecords:data.data.count
          })
          console.log("-------------------------------------------------------->>>>>>>>>>>>>>>>>>>>>>>>>>",this.state.currentPageData)
          
        }
        else {
          this.setState({
            currentPageData: [],
            prev: null,
            next: null,
            totalRecords:0
          })
          console.log("getMasterList() in Request", response)
        }
      })
      .catch((error) => {
        console.log("getMasterList() in Request", error)
      })
      this.setState({
        isLoading : false
      })
  }

  handleInputChange = (event) => {
    this.setState({
      searchText: event.target.value,
      pages:1
    },()=>{
      if(this.state.searchText.length >= 3){
        this.getMasterList()
      }
      else if(this.state.searchText.length <= 0){
        this.getMasterList()
      }
    });
    
  };

  clearInput = () => {
    this.setState({ searchText: '', pages: 1 });
    this.getMasterList()
  };



updateMaster = (value,master,isEnable) => {
    if (master === 'designations') master = 'designation'    
    if (master === 'tmlOffice') master = 'office'
    if (master === 'tmlRegion') master = 'region'
    let masterName = master + 'Name'
    let masterCode = master + 'Code'

    this.setState({
      isMasterEnable: isEnable,
      masterModalCode:value[masterCode],
      masterModalName:value[masterName],
      isMasterSelectionDisabled:true,
      showModal:true,
      masterModalType:'update',
      poId:value.po_id || "",
      buCompanyCode: value.po_comp_code
      ? { value: value.po_comp_code, label: value.po_comp_code }
      : null,
      startDate: value.po_start_date || "",
      endDate: value.po_end_date || "",
      status: value.po_status || "",
      count: value.count || "",
      saveDisabled: false,
      selectedModalValue:{ label: master.charAt(0).toUpperCase() + master.slice(1), value: master },
      isGroupNDASigned:value?.isGroupNDASigned === "Yes" ? true : false,
    })

    let masterPrevActive = isEnable
    let masterPrevName = value[masterName]
    let masterPrevCode = value[masterCode]

    this.setState({prevMasterData:
      {
        "isActive": masterPrevActive,
      [masterName]: masterPrevName,
      [masterCode]: masterPrevCode
    }
    },()=>{
      if (master === 'vendor') {
        this.setState({
          prevMasterData:{
            ...this.state.prevMasterData,
            "vendor":this.state.selectedVendor?.value,
            "isGroupNDASigned": value?.isGroupNDASigned === "Yes" ? true : false,
          }
        })
      }
      if (master === 'office') {
        this.setState({
          selectedRegion: value['region'],
          prevMasterData:{
            ...this.state.prevMasterData,
            "region_id":this.state.selectedRegion?.value
          }
        })
      }
      if (master === 'department') {
        this.setState({
          selectedDepartment: value['empType'],
          prevMasterData:{
            ...this.state.prevMasterData,
            "empType":this.state.selectedDepartment
          }
        })
      }
      if (master === 'project') {
        this.setState({
          selectedVendor: value['vendor'],
        })
      }
    })
    
  }

  addMaster = (master) => {
    this.getBuCompanyCodeList()
    this.setState({
      showModal:true,
      masterModalType:'add',
      selectedModalValue:{ label: master.charAt(0).toUpperCase() + master.slice(1), value: master },
      poId: "",
        buCompanyCode: null,
        startDate: "",
        endDate: "",
        createdBy: "",
        updatedBy: "",
        status: "",
        count: ""
    })
  }

  updateState = (key, value) => {
    this.setState({ [key]: value });
  };

  // Handle input change
  handleModalInput = (e) => {
    const { name, value } = e.target;
    this.updateState(name, value.trimStart());
  };

  handleModalClosure(){
    this.setState({
      isMasterEnable: true,
      masterModalCode:'',
      masterModalName:'',
      isMasterSelectionDisabled:false,
      showModal:false,
      masterModalType:'add',
      selectedModalValue:{ label: 'Vendor', value: 'vendor' },
      isGroupNDASigned:false,
      masterCodeErrorMessage:"",
      masterNameErrorMessage:"",
      prevMasterData:{}
    })
  }

  isInvalid = (obj) => obj == undefined || obj == null || obj == 'NA' || obj == '-'

  CheckEmptyString = (str = "") => !this.isInvalid(str) && str != "" && str.trim() !== "" ? true : false

  handleAddUpdateMaster(master){
    // const regex = /^[a-zA-Z0-9]+$/;
    const regex = /^[A-Za-z0-9\s\-.,_\/\\&]+$/;
    this.setState({
      isLoading : true,
      saveDisabled:true
    })
    if (!this.CheckEmptyString(this.state.masterModalName) || !this.CheckEmptyString(this.state.masterModalCode)){
      errorToast(PLEASE_ENTER_REQUIRED_FIELD)
    }
    else if(master.toLowerCase() == 'project' && !this.CheckEmptyString(this.state.selectedVendor)){
      errorToast(PLEASE_SELECT_VENDOR_COMPANY)
    }
    else if(master.toLowerCase() == 'tmlOffice' && !this.CheckEmptyString(this.state.selectedRegion)){
      errorToast(PLEASE_SELECT_REGION)
    }
    else{
      let isValidName = regex.test(this.state.masterModalName)
      let isValidCode = regex.test(this.state.masterModalCode)
      if(!isValidName || !isValidCode){
        if(!isValidName) this.setState({masterNameErrorMessage:"Please enter valid master name"}) 
        if(!isValidCode) this.setState({masterCodeErrorMessage:"Please enter valid master code"}) 
        this.setState({
          isLoading : false,
          saveDisabled:false
        })
      }
      else{
        if (master === 'designations') master = 'designation'
        if (master === 'tmlOffice') master = 'office'
        if (master === 'tmlRegion') master = 'region'
        let masterName = master + 'Name'
        let masterCode = master + 'Code'
        let body = {
          "updateMaster":{
            "isActive": master === 'vendor' && this.state.masterModalType === 'add' ? true : this.state.isMasterEnable ,
            [masterName]: this.state.masterModalName,
            [masterCode]: this.state.masterModalCode.toUpperCase(),
            "vendor":this.state.selectedVendor,
            "region":this.state.selectedRegion,
            "empType":this.state.selectedDepartment,
            isGroupNDASigned:this.state.isGroupNDASigned
          },
          "previousMaster":this.state.prevMasterData,
          "operation": this.state.masterModalType,
          "action": this.state.masterModalType === 'add' ? admin_add_master : admin_update_master,
          "module": admin_master 
        }
        if(master !== "project"){
          delete body.updateMaster.vendor
          delete body.previousMaster.vendor
        }
        if(master !== "vendor"){
          delete body.updateMaster.isGroupNDASigned
          delete body.previousMaster.isGroupNDASigned
        }
        if(master !== "tmlOffice"){
          delete body.updateMaster.region_id
          delete body.previousMaster.region_id
        }
        if(master !== "department"){
          delete body.updateMaster.empType
          delete body.previousMaster.empType
        }
        let apiUrl = config.addUpdateVendor
        switch(this.state.selectedValue){
          case "designations":
            apiUrl = config.addUpdateDesignation
            break;
          case "skill":
            apiUrl = config.addUpdateSkills
            break;
          case "project":
            apiUrl = config.addUpdateProject
            break;
          case "tower":
            apiUrl = config.addUpdateTower
            break;
          case "tmlOffice":
            apiUrl = config.addUpdatetmlOffice
            break;
          case "tmlRegion":
            apiUrl = config.addUpdatetmlRegion
            break;
          case "department":
            apiUrl = config.addUpdatedepartment
                break;
          default:
            apiUrl = config.addUpdateVendor
            break;
        }
        getAPIResponse(apiUrl, "POST", body)
        .then((response) => {
          if (response.status===500) {
            console.log("handleAddUpdateMaster() in Request", response)
            return null
          }
          else {
            return response.json()
          }
        }).then((data) => {
          if (data.status === 1) {
            this.setState({
              showModal:false,
              isLoading : false,
              saveDisabled:false
            },()=>{this.getMasterList()})
            successToast(data.message)
            this.handleModalClosure()
          }
          else {
            errorToast(data.message)
          }
        })
        .catch((error) => {
          console.log("handleAddUpdateMaster() in error", error)
          errorToast(SOMETHING_WENT_WRONG)
        })
        
      }
    }
    this.setState({
      isLoading : false,
      saveDisabled:false
    })
  }

  goNext(row) {
    this.props.setId({ requestId: row.id });
    console.log("Eye clicked: ", row);
    Router.push({
      pathname: "/UpdateIdPage",
    });
  }

  linkFollow = (cell,row) => {
    return (
      <div>
        <a onClick={() => { this.goNext(row); }} style={{ cursor: "pointer", marginRight: "2px" }} className="btn-info btn-xs" target="_blank"><i className="fa fa-edit"></i>
        </a>
      </div>
    )
  }

  setHeader = () =>{
    switch(this.state.selectedValue){
      case "designations":
        this.setState({ defaultHeaders: this.state.designationHeaders })
        break;
      case "tmlRegion":
        this.setState({ defaultHeaders: this.state.tmlRegionHeaders })
        break;
      case "tmlOffice":
        this.setState({ defaultHeaders: this.state.tmlOfficeHeaders })
        this.getMasterList({"master": "tmlRegion","action": admin_master_list,"module": admin_master })
        break;
      case "skill":
        this.setState({ defaultHeaders: this.state.skillHeaders })
        break;
      case "project":
        this.setState({ defaultHeaders: this.state.projectHeaders })
        this.getMasterList({"master": "vendor","action": admin_master_list,"module": admin_master })
        break;
      case "tower":
        this.setState({ defaultHeaders: this.state.towerHeaders })
        break;
      case "department":
        this.setState({ defaultHeaders: this.state.departmentHeaders })
          break;
      default:
        this.setState({defaultHeaders:this.state.vendorHeaders})
        break;
    }
    this.getMasterList()
  }

getBuCompanyCodeList = () => {
    getAPIResponse(config.getBuCompanyCodeList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getBuCompanyCodeList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj) => {
            this.setState(prevState => ({
              buCompanyCodeList : [...prevState.buCompanyCodeList , {value : obj.compCode , label : obj.compName}]
            }))
          })
      }
      else {
        console.log("getBuCompanyCodeList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getBuCompanyCodeList() in Request",error)
    })
  }

handleSearch = () => {
    if (!this.state.poId || this.state.poId.trim() === "") {
    errorToast("Please enter PO ID");
    return;
  }
  if (!this.state.buCompanyCode) {
    errorToast("Please select BU Code!");
    return;
  }
let queryParams = new URLSearchParams({
    "po_id": this.state.poId,
    "company_code": this.state.buCompanyCode
  })
  this.setState({ isSearching: true, saveDisabled: true });
  let api_url = `${config.searchPO}?${queryParams.toString()}`
  getAPIResponse(api_url, "GET", {})
    .then((response) => {
      if(!response.ok){
        console.log("getBuCompanyCodeList() in Request",response)
        errorToast(response.message)
        return null
      }
      else{
        return response.json()
      }

    })
    .then((data) => {
      if(data != null){
        if (data.status === 0) {
          errorToast(data.message)
          return;
        }
        this.setState({
        startDate: data.data.po_start_date,
        endDate: data.data.po_end_date,
        status: data.status,
        saveDisabled: false 
      });
      successToast("PO details fetched successfully")
      }
      else{
        console.log(data.message)   
        errorToast(data.message)
      }
    })
    .catch((err) => {
        errorToast("Something went wrong while searching")
        console.log("Search API Error", err)
    });
};


// handleSearch = () => {
//   if (!this.state.poId || this.state.poId.trim() === "") {
//     errorToast("Please enter PO ID");
//     return;
//   }
//   if (!this.state.buCompanyCode) {
//     errorToast("Please select BU Code!");
//     return;
//   }

//   this.setState({ isSearching: true, saveDisabled: true });

//   const url = `https://partneronboard-dashboard-uat.tatamotors.com/api/po/po-details?po_id=${this.state.poId}&company_code=${this.state.buCompanyCode}`;

//   fetch(url, { method: "GET" })
//     .then((response) => {
//       if (!response.ok) {
//         errorToast("Failed to fetch PO details");
//         return null;
//       }
//       console.log("------------------===================----------------------",response.json())
//       return response.json();
//     })
//     .then((data) => {
//       if (data) {
//         if (data.status === 0) {
//           toast.error(data.message);
//           return;
//         }
//         console.log("--------------------------------->>>>>>>>>")

//         this.setState({
//           startDate: data.data.po_start_date,
//           endDate: data.data.po_end_date,
//           createdBy: data.data.po_created_by,
//           updatedBy: data.data.po_updated_by,
//           status: data.data.po_status,
//           saveDisabled: false,
//         });

//         successToast("PO details fetched successfully");
//       }
//     })
//     .catch((err) => {
//       errorToast("Something went wrong while searching");
//       console.log("Search API Error", err);
//     })
//     .finally(() => {
//       this.setState({ isSearching: false });
//     });
// };



// handleSearch = () => {
//   this.setState({ isSearching: true, saveDisabled: true });
//   const data = {
//     status: 1,
//     message: "Data retrieved successfully",
//     data: {
//       po_id: 9700001869,
//       po_start_date: "2025-03-01",
//       po_end_date: "2025-04-06",
//       po_comp_code: "100",
//       po_sap_vendor_code: "I64931",
//       po_vendor_name: "iASYS TECHNOLOGY SOLUTIONS PVT LTD",
//       po_resource_count: "19",
//       po_status: "9",
//       po_created_at: "2025-09-03T07:55:08.280971Z",
//       po_created_by: "",
//       po_updated_at: "2025-09-03T11:41:54.648649Z",
//       po_updated_by: ""
//     }
//   };

//   if (data.status === 1) {
//     this.setState({
//       startDate: data.data.po_start_date,
//       endDate: data.data.po_end_date,
//       createdBy: data.data.po_created_by,
//       updatedBy: data.data.po_updated_by,
//       status: data.data.po_status,
//       saveDisabled: false,
//     });

//     successToast("PO details fetched successfully (dummy)");
//   } else {
//     errorToast(data.message);
//   }

//   this.setState({ isSearching: false });
// };




handleSubmit = () => {
    if (!this.state.count || this.state.count.toString().trim() === "") {
        errorToast("Please enter count before Submitting");
        return;
    }
  const bodyData = {
    po_id: this.state.poId,
    po_resource_count: this.state.count,
    
  };

  getAPIResponse(config.submitMaster, "PATCH", bodyData)
    .then((response) => {
      if (!response.ok) throw new Error("Submit failed");
      return response.json();

    })
    .then((data) => {
      console.log("Submitted Successfully", data);
      this.setState({ showModal: false });
      successToast(data.message)
      this.getMasterList()

    })
    .catch((err) => console.log("Submit Error", err));
};

handleUpdate = () => {
    if (!this.state.count || this.state.count.toString().trim() === "") {
        errorToast("Please enter count before Submitting");
        return;
    }
  const bodyData = {
    po_id: this.state.poId,
    po_resource_count: this.state.count,
  };

  getAPIResponse(config.submitMaster, "PATCH", bodyData)
    .then((response) => {
      if (!response.ok) throw new Error("Submit failed");
      return response.json();
    })
    .then((data) => {
        if(data !== null && data.status === 1)
            console.log("Submitted Successfully", data);
            successToast(data.message)
            this.setState({ showModal: false });
    })
    .catch((err) => console.log("Submit Error", err));
    
};



  render() {
    console.log("this.state:",this.state)
    return (
      <Fragment>
        <ReactNotifications />
        <div className="border content-wrapper justify-space-between">
          <div className="text-right text-space-between">
              <Select
                options={this.state.selectOptions}
                value={this.state.selectedValue}
                onChange={(e) => { this.setState({ requestType: e, selectedValue: e, pages: 1 }, () => { this.setHeader() }) }}
                isSearchable
                name="requestType"
                placeholder="Select Master Table"
                className="select-option"
                style={{height:"42.5px", visibility: this.state.hideSelect ? "hidden" : "visible"}}
                
              />
              <div className='add-search-vendor'>
                <div className="search-bar">
                  <input 
                    type="text" 
                    placeholder={"Search by PO ID" }
                    value={this.state.searchText} 
                    onChange={(event)=>{this.handleInputChange(event)}} 
                  />
                  {this.state.searchText && (
                    <span className="clear-iconV2" onClick={this.clearInput}>x</span>
                  )}
                </div>
              
                {!this.state.invalidAddButton.includes(this.state.selectedValue)?
                  <div className='button-paginate' onClick={()=>{this.addMaster(this.state.selectedValue)}}>
                  <FaPlus style= {{display:'inline-block', alignItems:'center'}} size={'0.8rem'}/> 
                    {" Add"}
                </div>
                :null}
              </div>
              
            </div>

            <AdminTable 
          headers={this.state.poHeaders}     
          currentPageData={this.state.currentPageData} 
        />
            {/* <AdminTable headers={this.state.defaultHeaders} currentPageData={this.state.currentPageData} /> */}
          <div className='fixed-width'>
            <div className="paginator-space">
              <div className='flex-d' >
                <div className='page-label'>
                  {`Total Record: ${this.state.totalRecords}`}
                </div>
              </div>
              <div className='pagination'>
              <div className={this.state.prev === true  ? 'button-paginate' : 'button-paginate  disabled'} onClick={this.state.prev === true ? () => { this.setState({ pages: this.state.pages - 1, isLoading: true }, () => { this.getMasterList() })} : null}>
                <FaArrowLeft style= {{display:'inline-block', alignItems:'center'}} size={'0.8rem'}/> 
                  {" Previous"}
              </div>
                <div className={'page-label'}>{this.state.pages}</div>
                <div className={this.state.next === true  ? 'button-paginate' : 'button-paginate disabled'} onClick={this.state.next === true ? () => { this.setState({ pages: this.state.pages + 1, isLoading: true }, () => { this.getMasterList() })} : null}>
                  {"Next "}
                  <FaArrowRight style= {{display:'inline-block', alignItems:'center'}} size={'0.8rem'}/> 
              </div>
              </div>
            </div>
          </div>
        </div>

        {/* Add Update Modal */}
{/* ---------------------sujal */}
    {this.state.sujal && (
    <div>
        {<div className="popup-center">
  <div className="popup-container">
    <div style={{ width: "100%" }}>
      <div className="close-popup" onClick={() => { this.handleModalClosure() }}>
        <AiOutlineClose />
      </div>
      <h3 style={{ textAlign: "center", fontWeight: "bolder" }}>
        {this.state.masterModalType === 'add' ? "Add PO Details" : "Update PO Details"}
      </h3>
    </div>

    {/* PO Id */}
    <div className="popup-form">
      <div className="popup-input">
        <label className="popup-lable">PO Id<span style={{color:'red', fontWeight:'900'}}>*</span></label>
        <div className="input-box">
          <input
            name="poId"
            placeholder="Enter PO Id"
            value={this.state.poId}
            onChange={(e) => this.setState({ poId: e.target.value })}
          />
        </div>
      </div>
    </div>

    
    <div className="popup-form">
      <div className="popup-input">
        <label className="popup-lable">BU Company Code<span style={{color:'red', fontWeight:'900'}}>*</span></label>
        <div className="input-box" style={{ display: "flex", gap: "8px" }}>
          <Select
            options={this.state.buCompanyCodeList}
            value={this.state.buCompanyCode}
            onChange={(e) => this.setState({ buCompanyCode: e })}
            isSearchable
            placeholder="Select BU Code"
            style={{ width: "100%" }}
          />
          {this.state.masterModalType === "add" ? (<button className="search-btn" onClick={this.handleSearch}><AiOutlineSearch size={18} /></button>) : null}
        </div>
      </div>
    </div>

    
    <div className="display-flex-column" style={{ width: "100%" }}>
      <div className="popup-form">
        <div className="popup-input">
          <label className="popup-lable">Start Date</label>
          <div className="input-box">
            <input value={this.state.startDate} disabled />
          </div>
        </div>
      </div>

      <div className="popup-form">
        <div className="popup-input">
          <label className="popup-lable">End Date</label>
          <div className="input-box">
            <input value={this.state.endDate} disabled />
          </div>
        </div>
      </div>

      {/* <div className="popup-form">
        <div className="popup-input">
          <label className="popup-lable">Created By</label>
          <div className="input-box">
            <input value={this.state.createdBy} disabled />
          </div>
        </div>
      </div>

      <div className="popup-form">
        <div className="popup-input">
          <label className="popup-lable">Updated By</label>
          <div className="input-box">
            <input value={this.state.updatedBy} disabled />
          </div>
        </div>
      </div> */}

      <div className="popup-form">
        <div className="popup-input">
          <label className="popup-lable">Status</label>
          <div className="input-box">
            <input value={this.state.status} disabled />
          </div>
        </div>
      </div>

      
      <div className="popup-form">
        <div className="popup-input">
          <label className="popup-lable">Count<span style={{color:'red', fontWeight:'900'}}>*</span></label>
          <div className="input-box">
            <input
              type="number"
              name="count"
              placeholder="Enter Count"
              value={this.state.count}
              onChange={(e) => this.setState({ count: e.target.value })}
            />
          </div>
        </div>
      </div>
    </div>

    
    <div className="popup-footer" style={{ display: "flex", justifyContent: "flex-end", marginTop: "20px" }}>
      <button className="cancel-btn" onClick={() => this.handleModalClosure()}>Cancel</button>
      <button className="submit-btn" onClick={this.handleSubmit}>Submit</button>
    </div>
  </div>
</div>
}
        {this.renderAddUpdateModal()}
    </div>
    )}



        {this.state.showModal?
          <>
            <div className="popup-wrapper"></div>
            <div className="popup-center">
              <div className="popup-container">
                <div style={{ width: "100%" }}>
                  <div className="close-popup" onClick={()=>{this.handleModalClosure()}}>
                    <AiOutlineClose />
                  </div>
                  <h3 style={{ textAlign: "center", fontWeight: "bolder" }}>{this.state.masterModalType == 'add'? "Add PO Details" : "Update PO Details"}</h3>

                </div>
            {/* <div style={{ display: "flex", gap: "20px", alignItems: "flex-end" }}>
                <div className="popup-form" style={{ flex: 1 }}>
                    <div className="popup-input">
                    <label className="popup-lable">PO ID</label>
                        <div className="input-box">
                            <input
                            name="poId"
                            placeholder="Enter PO Id"
                            value={this.state.poId}
                            onChange={(e) => this.setState({ poId: e.target.value })}
                            />
                        </div>
                    </div>
                </div>
                <div style={{ display: "flex", flex: 1, gap: "0px" }}>
                    <div className="popup-form" style={{ flex: 1 }}>
                        <div className="popup-input">
                            <label className="popup-lable">BU Code</label>
                            <div className="input-box" >
                                <Select
                                    options={this.state.buCompanyCodeList}
                                    value={this.state.buCompanyCode}
                                    onChange={(e) => this.setState({ buCompanyCode: e })}
                                    isSearchable
                                    placeholder="Select BU Code"
                                    style={{ width: "100%" }}
                                />
                                {this.state.masterModalType === "add" ? (
  <button className="search-btn" onClick={this.handleSearch}>
    <AiOutlineSearch size={18} />
  </button>
) : null}
                            </div>
                            
                        </div>
                    </div>

                    <div className="popup-form search-box">
                        <div className="popup-input">
                            <label className="popup-lable">&nbsp;</label>
                                
                        </div>
                    </div>
                </div>
            </div> */}



            <div className='display-flex-column' style={{width:"100%"}}>
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">PO ID<span style={{color:'red', fontWeight:'900'}}>*</span>  
                      </label>
                      <div className="input-box">
                        <input
                            name="poId"
                            placeholder="Enter PO Id"
                            value={this.state.poId}
                            onChange={(e) => this.setState({ poId: e.target.value })}
                            />
                      </div>
                    </div>
                  </div>
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">BU Code<span style={{color:'red', fontWeight:'900'}}>*</span>  
                      </label>
                      <div className="input-box">
                        <Select
                                    options={this.state.buCompanyCodeList}
                                    value={this.state.buCompanyCode}
                                    onChange={(e) => this.setState({ buCompanyCode: e })}
                                    isSearchable
                                    placeholder="Select BU Code"
                                    style={{ width: "100%" }}
                        />
                            {this.state.masterModalType === "add" ? (
                            <button className="search-btn" onClick={this.handleSearch}>
                                <AiOutlineSearch size={18} />
                            </button>
                            ) : null}        
                      </div>
                      {this.state.masterCodeErrorMessage ? <span className="error-message">{this.state.masterCodeErrorMessage}</span> : null}
                    </div>
                  </div>
            </div>







            <div className='display-flex-column' style={{width:"100%"}}>
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">Start Date<span style={{color:'red', fontWeight:'900'}}>*</span>  
                      </label>
                      <div className="input-box">

                        <input value={this.state.startDate} disabled  name="startdate"
                            placeholder="Enter Start Date" />
                      </div>
                    </div>
                  </div>
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">End Date<span style={{color:'red', fontWeight:'900'}}>*</span>  
                      </label>
                      <div className="input-box">
                        <input value={this.state.endDate} disabled   name="enddate"
                            placeholder="Enter End Date"/>
                      </div>
                      {this.state.masterCodeErrorMessage ? <span className="error-message">{this.state.masterCodeErrorMessage}</span> : null}
                    </div>
                  </div>
            </div>
            {/* <div className='display-flex-column' style={{width:"100%"}}>
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">Created By<span style={{color:'red', fontWeight:'900'}}>*</span>  
                      </label>
                      <div className="input-box">
                        <input value={this.state.createdBy} disabled name="createdby"
                            placeholder="Enter Created By"/>
                      </div>
                    </div>
                  </div>
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">Updated By<span style={{color:'red', fontWeight:'900'}}>*</span>  
                      </label>
                      <div className="input-box">
                        <input value={this.state.updatedBy} disabled name="updateby"
                            placeholder="Enter Updated By"/>
                      </div>
                      {this.state.masterCodeErrorMessage ? <span className="error-message">{this.state.masterCodeErrorMessage}</span> : null}
                    </div>
                  </div>
            </div> */}



            <div className='display-flex-column' style={{width:"100%"}}>
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">Status<span style={{color:'red', fontWeight:'900'}}>*</span>  
                      </label>
                      <div className="input-box">
                        <input value={this.state.status} disabled name="status"
                            placeholder="Enter Status"/>
                      </div>
                    </div>
                  </div>
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">Count<span style={{color:'red', fontWeight:'900'}}>*</span>  
                      </label>
                      <div className="input-box">
                        <input
                            type="number"
                            name="count"
                            placeholder="Enter Count"
                            value={this.state.count}
                            onChange={(e) => this.setState({ count: e.target.value })}
                            disabled={this.state.saveDisabled}

                        />
                      </div>
                      {this.state.masterCodeErrorMessage ? <span className="error-message">{this.state.masterCodeErrorMessage}</span> : null}
                    </div>
                  </div>
            </div>
                <div className="save">
                  <button className="save-button" disabled={this.state.saveDisabled} onClick={() => {this.state.masterModalType === 'add' ? this.handleSubmit() : this.handleUpdate();}}>{this.state.masterModalType == 'add'? "Submit" : "Update"}</button>
                </div>
              </div>
            </div>
          </>
        :null
        }
        {
          this.state.isLoading ?
            <div className="loader">
              <div></div>
            </div> : null
        }
      </Fragment>
    )
  }
}


const mapStateToProps = state => ({
  counter: state.counter.value,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login
});

const mapDispatchToProps = {
  login: login
};

export default connect(mapStateToProps, mapDispatchToProps)(PODetailTable);
