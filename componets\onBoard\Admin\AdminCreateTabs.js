import React, { Component } from "react";
import {Tabs,Tab,ListGroup,Badge,Table,Alert,FormControl,InputGroup,fieldset,Modal,Col,Card,Container,Row,Button,Form,} from "react-bootstrap";
import { connect } from "react-redux";
import { setId} from "../../../redux/actions/counterActions";
import AssignedRequest from "../AssignedRequest";
import FullfilledRequest from "../FullfilledRequest";
import RejectedRequest from "../RejectedRequest";
import CancelledRequest from "../CancelledRequest";
import InviteTable from "../InviteTable";
import ApprovedRequest from "../ApprovedRequest";

class AllTabs extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      activeKeyCreate: "invite",
      activeKeyUpdate:"assigned",
      activeKey:"invite"
    };
  }

  handleShow = (e) => {
    this.setState({
      activeKey: e,
    });

  };
  
  renderTabContent() {
    const { activeKey } = this.state;
    console.log(activeKey)
    switch (activeKey) {
      case 'invite':
        return <InviteTable baseUrl={this.props.baseUrl} loginData={this.props.loginData} activekey={this.state.activeKey} />;
      case 'assigned':
        return <AssignedRequest baseUrl={this.props.baseUrl} loginData={this.props.loginData} activekey={this.state.activeKey} />;
      case 'approved':
        return <ApprovedRequest baseUrl={this.props.baseUrl} loginData={this.props.loginData} activekey={this.state.activeKey} />;
      case 'fullfilled':
        return <FullfilledRequest baseUrl={this.props.baseUrl} loginData={this.props.loginData} activekey={this.state.activeKey}/>
      case 'reject':
        return <RejectedRequest baseUrl={this.props.baseUrl} loginData={this.props.loginData} activekey={this.state.activeKey}/>
      case 'cancel':
        return <CancelledRequest baseUrl={this.props.baseUrl} loginData={this.props.loginData} activekey={this.state.activeKey}/>
      default:
        return null;
    }
  }
  render() {
    return (
        <div>
          <div className="table-container">
            <Row>
              <Col md="12">
                <Tabs
                  id="controlled-tab-example"
                  defaultActiveKey={"invite"}
                  onSelect={this.handleShow}
                  className="mb-3"
                  fill
                > 
                  <Tab
                    className="tab-color"
                    eventKey="invite"
                    title="Invite"
                    tabClassName={this.state.activeKey == "Indents"? "tabActive" : "tabInactive"}  
                  />
                    {/* <InviteTable baseUrl={this.props.baseUrl} loginData={this.props.loginData} activekey={this.state.activeKey}/>
                  </Tab>  */}

                  <Tab
                    // className="tab-color"
                    eventKey="assigned"
                    title="Assigned Request"
                    tabClassName={this.props.requestType === 'Update' || this.state.activeKey == "assigned"? "tabActive" : "tabInactive"}
                  />
                    {/* <AssignedRequest baseUrl={this.props.baseUrl} loginData={this.props.loginData} activekey={this.state.activeKey}/>
                  </Tab> */}

                  <Tab
                    // className="tab-color"
                    eventKey="approved"
                    title="Approved Request"
                    tabClassName={this.state.activeKey == "approved"? "tabActive" : "tabInactive"}
                  />
                  {/* <ApprovedRequest baseUrl={this.props.baseUrl} loginData={this.props.loginData} activekey={this.state.activeKey}/>
                  </Tab> */}

                  <Tab
                    // className="tab-color"
                    eventKey="fullfilled"
                    title="Fullfilled Request"
                    tabClassName={this.state.activeKey == "fullfilled"? "tabActive" : "tabInactive"}
                  />
                  {/* <FullfilledRequest baseUrl={this.props.baseUrl} loginData={this.props.loginData} activekey={this.state.activeKey}/>
                  </Tab> */}

                  <Tab
                    // className="tab-color"
                    eventKey="reject"
                    title="Rejected Request"
                    tabClassName={this.state.activeKey == "reject"? "tabActive" : "tabInactive"}  
                  />
                  {/* <RejectedRequest baseUrl={this.props.baseUrl} loginData={this.props.loginData} activekey={this.state.activeKey}/>
                  </Tab> */}

                  <Tab
                    // className="tab-color"
                    eventKey="cancel"
                    title="Cancelled Request"
                    tabClassName={this.state.activeKey == "cancel"? "tabActive" : "tabInactive"}  
                  />
                  {/* <CancelledRequest baseUrl={this.props.baseUrl} loginData={this.props.loginData} activekey={this.state.activeKey}/>
                  </Tab> */}
                  
                </Tabs>
                {this.renderTabContent()}
              </Col>
            </Row> 
          </div>
        </div>
      );
    } 
  }

const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
  requestType: state.counter.requestType
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(AllTabs);
