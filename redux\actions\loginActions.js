
//Action Types
export const LOGIN = "LOGIN";
export const LOGOUT = "LOGOUT";
export const REHYDRATE = "REHYDRATE"


//Action Creator
export const login = (data) => ({
    type: LOGIN,
    payload: {
        userName: data.userName,
        userEmail: data.userEmail,
        profile: data.profile,
        profileImg: data.profileImg,
        empId: data.empId,
        userRole: data.userRole,
        isLogin: data.isLogin,
        personName: data.personName,
        personMobile: data.personMobile,
        personEmail: data.personEmail,
        vendor: data.vendor,
        link:data.link,
        vendorName:data.vendorName,
        isTmlEmployee:data.isTmlEmployee,
        otp:data.otp,
        spoc:data.spoc,
        tmlmanager:data.tmlmanager,
        vendorCompanyName:data.vendorCompanyName,
        message:data.message,
        isSPOC:data.isSPOC,
        isNonTmlManager:data.isNonTmlManager,
        isSuperUser:data.isSuperUser,
        isSidebarShown:data.isSidebarShown
    }
});
export const logOut = () => ({
    type: LOGOUT,

});


