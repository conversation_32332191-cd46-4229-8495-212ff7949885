.box1 {
    position: relative;
    border-radius: 3px;
    background: #fff;
    border-top: 3px solid #d2d6de;
    margin-bottom: 20px;
    width: 100%;
    border-top-color: #00c0ef;
    box-shadow: 0;
}
.box-title1 {
    display: inline-block;
    font-size: 18px;
    margin: 0;
    line-height: 1;
}
.box-header.with-border {
    border-bottom: 1px solid #f4f4f4;
}
.box-header {
    color: #444;
    display: block;
    padding: 10px;
    position: relative;
}
.box-body {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    padding: 0px;
}
label:not(.form-check-label):not(.custom-file-label) {
    font-weight: 500 !important;
    font-size: 14px !important;
}
.form-group {
    margin-bottom: 0rem !important;
}
.form-check-label {
    font-size: 13px !important;
}
.content-header {
    padding: 7px 0.5rem !important;
}
.myalert {
    font-size: 13px !important;
    padding: 0px !important;
}
.products-list {
    list-style: none;
    margin: 0;
    padding: 0;
}
.product-list-in-box > .item {
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 0;
    border-bottom: 2px solid #f4f4f4;
}
.products-list > .item {
    border-radius: 3px;
    padding: 10px 0;
    background: #fff;
}
.products-list .product-img {
    float: left;
}
.products-list .product-info {
    margin-left: 60px;
}
.products-list .product-title {
    font-weight: 400;
    font-size: 14px;
}
.products-list .product-description {
    display: block;
    color: #999;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 12px;
}
.box.box-solid {
    border-top: 0;
}
.no-padding {
    padding: 0 !important;
}
.nav {
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}
.box .nav-stacked > li {
    border-bottom: 1px solid #f4f4f4;
    margin: 0;
}
.bg-light-blue, .label-primary, .modal-primary .modal-body {
    background-color: #3c8dbc !important;
}
.nav > li {
    position: relative;
    display: block;
}
.nav > li > a {
    position: relative;
    display: block;
    padding: 10px 15px;
}
.nav-pills > li.active > a {
    font-weight: 600;
}
.nav-stacked > li {
    float: none;
}
.box1 .nav-stacked > li {
    border-bottom: 1px solid #f4f4f4;
    margin: 0;
}
.nav-stacked > li.active > a, .nav-stacked > li.active > a:hover {
    background: transparent;
    color: #444;
    border-top: 0;
    border-left-color: #3c8dbc;
}
.fa2 {
    font-size: 15px !important;
    margin-right: 8px !important;
}
.bg-info {
    background-color: #0dcaf0 !important;
}
.rowBorder {
    border-right: 3px solid #d2d6de !important;
}
.tabActive {
    border-top: 3px solid #3c8dbc !important;
}
.tabInactive {
    color: #495057;
}
.load {
    position: absolute;
    z-index: 1;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: 50px;
    height: 50px;
    margin: auto;
}
.loader {
    border: 16px solid #f3f3f3;
    border-radius: 50%;
    border-top: 16px solid #3498db;
    width: 120px;
    height: 120px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
    margin: auto;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
}
@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
   }
    100% {
        -webkit-transform: rotate(360deg);
   }
}
@keyframes spin {
    0% {
        transform: rotate(0deg);
   }
    100% {
        transform: rotate(360deg);
   }
}
