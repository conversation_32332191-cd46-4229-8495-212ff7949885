import dashboard from "../public/img/dashboard.svg" 
import create from '../public/img/create.svg'
import assign from '../public/img/assign.svg'
import master from '../public/img/master.svg'
import report from '../public/img/report.svg'
import bulk_extension from '../public/img/bulk-extension.svg'
import bulk_enable from '../public/img/bulk-enable.svg'

export const navigations = {

    isTmlManager : [
        {
            src: dashboard,
            width:25,
            height:25,
            tabName : "My Request",
            link : "/Dashboard",
            isDropdown:false

        },
        {
            src:create,
            width:25,
            height:25,
            tabName : "New Request",
            link:"",
            isDropdown:true
        },
        {
            src:assign,
            width:25,
            height:25,
            tabName : "Approvals",
            link:"/ApprovedRequest",
            isDropdown:false
        },
        {
            src:report,
            width:25,
            height:25,
            tabName : "SPOC Reports",
            link:"/SpocReports",
            isDropdown:false
        },
        {
            src:report,
            width:25,
            height:25,
            tabName : "Domain ID Extension",
            link:"/DomainIDExtension",
            isDropdown:false
        },
        // {
        //     src:bulk_extension,
        //     width:25,
        //     height:25,
        //     tabName : "Bulk Extension",
        //     link:"/BulkExtension",
        //     isDropdown:false
        // }
    ],
    isSpoc : [
        {
            src: dashboard,
            width:25,
            height:25,
            tabName : "My Request",
            link : "/Dashboard",
            isDropdown:false

        },
        {
            src:create,
            width:25,
            height:25,
            tabName : "New Request",
            link:"",
            isDropdown:true
        },
        {
            src:report,
            width:25,
            height:25,
            tabName : "SPOC Reports",
            link:"/SpocReports",
            isDropdown:false
        },
        {
            src:report,
            width:25,
            height:25,
            tabName : "Domain ID Extension",
            link:"/DomainIDExtension",
            isDropdown:false
        },
        {
            src: master,
            width:25,
            height:25,
            tabName : "PO Detail",
            link : "/PODetail",
            isDropdown:false

        },
    ],
    isNonTmlManager : [
        {
            src:assign,
            width:25,
            height:25,
            tabName : "Approvals",
            link:"/ApprovedRequest",
            isDropdown:false
        },
        {
            src:report,
            width:25,
            height:25,
            tabName : "Domain ID Extension",
            link:"/DomainIDExtension",
            isDropdown:false
        },
    ],
    isNonTmlSPOC : [
        {
            src: dashboard,
            width:25,
            height:25,
            tabName : "My Request",
            link : "/Dashboard",
            isDropdown:false

        },
        {
            src:create,
            width:25,
            height:25,
            tabName : "New Request",
            link:"",
            isDropdown:true
        },
        {
            src:assign,
            width:25,
            height:25,
            tabName : "Approvals",
            link:"/ApprovedRequest",
            isDropdown:false
        },
        {
            src:report,
            width:25,
            height:25,
            tabName : "SPOC Reports",
            link:"/SpocReports",
            isDropdown:false
        },
        {
            src:report,
            width:25,
            height:25,
            tabName : "Domain ID Extension",
            link:"/DomainIDExtension",
            isDropdown:false
        },
    ],
    isSuperUser : [
        // {
        //     src: dashboard,
        //     width:25,
        //     height:25,
        //     tabName : "Dashboard",
        //     link : "/Admin/Dashboard",
        //     isDropdown:false

        // },
        {
            src: dashboard,
            width:25,
            height:25,
            tabName : "Requests",
            link : "/Admin/RequestDashboard",
            isDropdown:false

        },
        {
            src:assign,
            width:25,
            height:25,
            tabName : "Approvals",
            link:"/Admin/ApprovedRequest",
            isDropdown:false
        },
        {
            src: master,
            width:25,
            height:25,
            tabName : "Masters",
            link : "/Admin/Master",
            isDropdown:false

        },
        {
            src:create,
            width:25,
            height:25,
            tabName : "Access Management",
            link:"/Admin/AccessManagement",
            isDropdown:false
        },
        {
            src:report,
            width:25,
            height:25,
            tabName : "Employee Reports",
            link:"/Admin/EmployeeReports",
            isDropdown:false
        },
        {
            src:bulk_extension,
            width:25,
            height:25,
            tabName : "Bulk Update",
            link:"/Admin/BulkUpdate",
            isDropdown:false
        },
        // {
        //     src:bulk_enable,
        //     width:25,
        //     height:25,
        //     tabName : "Bulk Enable",
        //     link:"/Admin/BulkEnable",
        //     isDropdown:false
        // }
    ],

}

export const requestDropDown = [
    {
        href:"/Onboard",
        name:"Create"
    },
    {
        href:"/UpdateRequest",
        name:"Update"
    },
    {
        href:"/DisableRequest",
        name:"Disable"
    },
    {
        href:"/SyncEmployees",
        name:"Sync Vendor Employee"
    }
]

export function routerAndRoleV2(isTmlManager , isSPOC , isNonTmlManager, isSuperUser){
    let defaultRoute = "" 
    let role = ""
    let name = ""

    if(isSuperUser === "True"){
        defaultRoute = '/Admin/RequestDashboard'
        role = 'isSuperUser'
        name = 'Admin'
    }
    else{
        if(isTmlManager === "True" && isSPOC === "True" && isNonTmlManager === "False"){
            defaultRoute = '/Dashboard'
            role = 'isTmlManager'
            name = 'TML Manager'
          }
      
        if(isTmlManager === 'False' && isSPOC === 'True' && isNonTmlManager === 'False'){
            defaultRoute = '/Dashboard' 
            role = 'isSpoc'
            name = 'SPOC'
        }
    
        if(isTmlManager === 'False' && isSPOC === 'False' && isNonTmlManager === 'True'){
            defaultRoute = '/ApprovedRequest'
            role = 'isNonTmlManager'
            name = "Non TML Manager"
        }
    
        if(isTmlManager === 'False' && isSPOC === 'True' && isNonTmlManager === 'True'){
            defaultRoute = '/Dashboard'
            role = 'isNonTmlSPOC'
            name = 'Non TML SPOC'
        }
    }
    let isRoleSetInLocalStrorage =localStorage.getItem('role')
    if (!isRoleSetInLocalStrorage)
        localStorage.setItem("role",role)
    return {defaultRoute , role, name}
  
} 

export const pageAccessWithRole = {
    isTmlManager     : ["/ApprovedRequest","/Dashboard","/SyncEmployees","/DisableRequest","/UpdateRequest","/Onboard"],
    isSpoc           : ["/Dashboard","/SyncEmployees","/DisableRequest","/UpdateRequest","/Onboard"],
    isNonTmlManager  : ["/ApprovedRequest"],
    isNonTmlSPOC     : ["/ApprovedRequest","/SyncEmployees","/DisableRequest","/UpdateRequest","/Onboard"],
    isSuperUser      : ["/Dashboard","/Master","/Spoc","/RequestDashboard","/ApprovedRequest"]
}

export function routerAndRole(isTmlManager , isSPOC , isNonTmlManager, isSuperUser){
    let defaultRoute = "" 
    let role = ""
    let name = ""

    if(isSuperUser === true){
        defaultRoute = '/Admin/RequestDashboard'
        role = 'isSuperUser'
        name = 'Admin'
    }
    else{
        if(isTmlManager === true && isSPOC === true && isNonTmlManager === false){
            defaultRoute = '/Dashboard'
            role = 'isTmlManager'
            name = 'TML Manager'
          }
      
        if(isTmlManager === false && isSPOC === true && isNonTmlManager === false){
            defaultRoute = '/Dashboard' 
            role = 'isSpoc'
            name = 'SPOC'
        }
    
        if(isTmlManager === false && isSPOC === false && isNonTmlManager === true){
            defaultRoute = '/ApprovedRequest'
            role = 'isNonTmlManager'
            name = "Non TML Manager"
        }
    
        if(isTmlManager === false && isSPOC === true && isNonTmlManager === true){
            defaultRoute = '/Dashboard'
            role = 'isNonTmlSPOC'
            name = 'Non TML SPOC'
        }
    }
    let isRoleSetInLocalStrorage =localStorage.getItem('role')
    if (!isRoleSetInLocalStrorage)
        localStorage.setItem("role",role)
    return {defaultRoute , role, name}
  
}
