import { Component } from "react";
import Sidebar from "../componets/Sidebar";
import Footer from "../componets/Footer";
import Header from "../componets/Header";
import Link from "next/link";
import { encode } from "base-64";
import Router from "next/router";
import { connect } from "react-redux";
import { setId } from "../redux/actions/counterActions";
//import { decrementCounter, incrementCounter, step1 } from '../redux/actions/counterActions';
import { Tabs, Tab, ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form, } from "react-bootstrap";
import Request from "../componets/onBoard/Request";
class RequestUpdate extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
  }

  render() {
    return (
      <Request baseUrl={this.props.baseUrl} />
    );
  }
}

const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  baseUrl: state.counter.baseUrl,
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(RequestUpdate);
