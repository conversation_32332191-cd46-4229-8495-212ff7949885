import { Component } from "react";
import Sidebar from "../componets/Sidebar";
import Footer from "../componets/Footer";
import Header from "../componets/Header";
import { connect } from "react-redux";
import Login from "./login";
import { login, logOut } from "../redux/actions/loginActions";
import { setRequestType } from "../redux/actions/counterActions";
import CreateDisableRequest from "../componets/onBoard/CreateDisableRequest";

class DisableRequest extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      activeKey: "Indents",
    };
  }

  componentDidMount(){
    let role = localStorage.getItem("role")
        if(!(role === 'isNonTmlSPOC' || role === 'isTmlManager' || role === "isSpoc")){
            window.location.replace(constants.FRONTEND_URL + '/login');
        }
  }

  handleShow = (e) => {
    this.setState({
      activeKey: e,
    });
  };

  render() {
    const isLoggedIn = this.props.loginData.isLogin;
    if (!isLoggedIn) {
        window.location.replace(constants.FRONTEND_URL + '/login');
        return null 
    } 
    else {
      return (
        <CreateDisableRequest/>
      );
    }
  }
}

const mapStateToProps = (state) => ({
  counter: state.counter.value,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
});

const mapDispatchToProps = {
  login:login,
  setRequestType:setRequestType
};
export default connect(mapStateToProps, mapDispatchToProps)(DisableRequest);
