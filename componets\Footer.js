import React from "react";
// import {connect} from 'react-redux';
// import {decrementCounter, incrementCounter} from '../redux/actions/counterActions';

class Footer extends React.Component {
  static getInitialProps({ store }) {}

  constructor(props) {
    super(props);
  }

  render() {
    return (
      <footer className="main-footer footer-align">
        <strong style={{paddingRight:'5px'}}>Copyright &copy; 2024 <a href="">Tata Motors</a>.</strong>All  Rights  Reserved.
        <div className="float-right d-none d-sm-inline-block">
        </div>
      </footer>
    );
  }
}

export default Footer;
