import React, { Fragment } from "react";
import { Component } from "react";
import { toast } from "react-toastify";
import Router from "next/router";
import BootstrapTable from "react-bootstrap-table-next";
import paginationFactory from "react-bootstrap-table2-paginator";
import ToolkitProvider, { Search } from "react-bootstrap-table2-toolkit";
const { SearchBar } = Search;
import { connect } from "react-redux";
import { setId } from "../../redux/actions/counterActions";
import { getAPIResponse } from '../../constants/Utils'
import * as config from '../../constants/config'
// import * as constants from '../../constants/constants'
import {sizePerPage,admin_spoc_report,admin_spoc_report_list, emptyDataMessage, admin_spoc_report_mail} from '../../constants/constants'
import moment from "moment";
import { PAGINATIONS, SEARCH, SOMETHING_WENT_WRONG,PLEASE_ENTER_REQUIRED_FIELD,MINDEFAULTVALUE,INTERNAL_SERVER_ERROR } from "../../constants/message";
import { successToast, errorToast } from "../../toast"
import { Button, DatePicker, Select, Switch } from "antd";
import { AiOutlineClose } from "react-icons/ai";
import dayjs from "dayjs";
import { FaEnvelope,FaEye, FaEyeSlash } from "react-icons/fa";
import { Tooltip } from 'antd';

const { RangePicker } = DatePicker

class SpocReportsTable extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      response: "",
      updateObj: "",
      headers: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "employeeId", text: "Employee ID", sort: true },
        { dataField: "fullName", text: "Person Name", sort: true },
        { dataField: "email", text: "Personal Vendor Partner Email ID", sort: true },
        { dataField: "projectName", text: "Project", sort: true },
        { dataField: "tmlManagerEmail", text: "TML/TTL Manager", sort: true },
        { dataField: "validFrom", text: "Account Start", sort: true },
        { dataField: "validTill", text: "Account Expiry", sort: true },
        { dataField: "daysRemainig", text: "Active Days remaining", sort: true },
        { dataField: "Active", text: "Active status", sort: true ,
          formatter: (cell, row) => (
          <Switch
            checked={cell}
            onChange={(checked) => this.handleSwitchToggle(checked, row.id)}
          />
        )}
      ],
      dataArray: [],
      totalcomplete: "",
      currentPage: 0,
      currentPageData: [],
      sizePerPage: sizePerPage, 
      totalSize: 0,
      searchText: '',
      isSendDisable:false,
      showModal:false,
      spocEmail:this.props.loginData.userEmail,
      dataRange:3,
      fromDate:dayjs(),
      toDate:dayjs().add(3, 'month'),
      showDefaultDateRange: true,
      minDate:dayjs().subtract(3,'month'),
      maxDate:dayjs().add(6,'month'),
      userRole: localStorage.getItem("role"),
      vendorOption:[{"value": "all","label": "All Vendors"}],
      ActiveOption:[
        {"value": "all","label": "All Status"},
        { "value": true, "label": "Active" },
        { "value": false, "label": "In-active " }
      ],
      activeStatus:'all',
      vendor:'all',
      vendorErrorMessage:"",
      isLoading:false,
      totalRecords:0,
      isDataMasked:true,
    };

  }

  componentDidMount() {
    this.getSpocReportsTable()
    console.log("this.props.loginData.userRole",this.props,localStorage.getItem("role"))
    if((this.state.userRole === 'isSuperUser')||(this.state.userRole === 'isTmlManager'))
      this.getVendorList()
  }

  componentDidUpdate(prevProps){
    if(prevProps.requestType !== this.props.requestType){
      this.getSpocReportsTable()
    }
  }

  setDateRange(range) {
    console.log("range:", range)
    if (range !== null) {
      const startDate = moment(range[0].$d).format('YYYY-MM-DD')
      let endDate = moment(range[1].$d).format('YYYY-MM-DD')
      const maxDate = dayjs(startDate).add(this.state.dataRange,'month')
      if (dayjs(endDate).isAfter(maxDate)){
          endDate = moment(this.state.maxDate.$d).format('YYYY-MM-DD')
        }
      this.setState({
        fromDate: startDate,
        toDate: endDate,
      }, () => {
        this.getSpocReportsTable()
      })
    }
  }

  onCalendarChange = (dates) => {
    if (dates !== null && dates.length > 0) {
      const startDate = moment(dates[0].$d).format('YYYY-MM-DD')
      let endDate = moment(dates[1].$d).format('YYYY-MM-DD')
      const maxDate = dayjs(startDate).add(this.state.dataRange,'month')

      if (dayjs(endDate).isAfter(maxDate)){
        endDate = moment(maxDate.$d).format('YYYY-MM-DD')
      }
      this.setState({
        fromDate: startDate,
        toDate: endDate,
        showDefaultDateRange: false,
        minDate:dayjs(startDate),
        maxDate:maxDate,
      })
    }
  };
  
  handleOpenChange = (open) => {
    this.setState({
      showDefaultDateRange: open,
    },() => {
      if (!open && moment(this.state.maxDate.$d).format('YYYY-MM-DD') == this.state.toDate) this.getSpocReportsTable()
    })
  }

  getSpocReportsTable = () => {
    this.setState({
      isLoading : true
    })
    let body = {
      "pagination":true,
      "page_number":1,
      // "vendor":this.props.loginData.vendorName.toUpperCase(),
      // "from_date":dayjs(this.state.fromDate).format("YYYY-MM-DD"),
      // "to_date":dayjs(this.state.toDate).format("YYYY-MM-DD"),
      "active": this.state.activeStatus
    }
    let api_url = config.spocEmpDetails
    if((this.state.userRole === 'isSuperUser')||(this.state.userRole === 'isTmlManager')){
      if (this.state.vendor !== 'all') body['vendor'] = this.state.vendor
      if (this.state.userRole === 'isSuperUser'){
        body['action'] = admin_spoc_report_list
        body['module'] = admin_spoc_report
        api_url = config.adminSpocEmpDetails
      }
    }
    else{
      body['vendor'] = this.props.loginData.vendorName.toUpperCase()
    }

    getAPIResponse(api_url, "POST", body)
    .then((response)=>{
      if(response.status === 500){
        console.log("getSpocReportsTable() in spocEmpDetails",response)
        return null
      }
      else{
        return response.json()
      }  
    })
      .then((data)=>{
        if (data !== null && data.status === 1){
          data.data.results.map((value, index) => ({...value, isActive:<Switch checked={value.isActive}/>}))
          this.setState({
            totalSize: data.data.count,
            currentPage: 1,
            currentPageData: data.data.results.map((value, index) => ({...value, 
              maskedEmail:value.email.replace(/^(.)(.*)(.@.*)$/, (_, first, middle, last) => 
                first + "*".repeat(middle.length) + last
              ),
              unmaskedEmail:value.email,
              email: value.email.replace(/^(.)(.*)(.@.*)$/, (_, first, middle, last) => 
                first + "*".repeat(middle.length) + last
              ),
            srno: index + 1,validFrom:moment(value.validFrom).format('DD-MM-YYYY'),validTill:moment(value.validTill).format('DD-MM-YYYY'), Active:<Switch checked={value.isActive}/>}))
            // currentPageData: data.data.results.map((value, index) => (console.log("value",value)))
          })
        }
    })
    .catch((error)=>{
      errorToast(SOMETHING_WENT_WRONG)
      console.log("getSpocReportsTable() in spocEmpDetails",error)
    })
    this.setState({
      isLoading : false
    })
  }

  getPaginatedSpocReportsTables(page, searchText) {
    this.setState({
      isLoading : true
    })
    let body = {
      "pagination":true,
      "page_number":page,
      "search_text":searchText,
      // "from_date":dayjs(this.state.fromDate).format("YYYY-MM-DD"),
      // "to_date":dayjs(this.state.toDate).format("YYYY-MM-DD"),
      "active": this.state.activeStatus
    }
    let api_url = config.spocEmpDetails
    if((this.state.userRole === 'isSuperUser')||(this.state.userRole === 'isTmlManager')){
      if (this.state.vendor !== 'all') body['vendor'] = this.state.vendor
      if (this.state.userRole === 'isSuperUser'){
        body['action'] = admin_spoc_report_list
        body['module'] = admin_spoc_report
        api_url = config.adminSpocEmpDetails
      }
    }
    else{
      body['vendor'] = this.props.loginData.vendorName.toUpperCase()
    }
    getAPIResponse(api_url, "POST", body).then((response)=>{
      if (response.status === 500){
        console.log("getPaginatedSpocReportsTables (FulfilledRequest.js)", response)
        return null
      } else {
        return response.json()
      }  
    }).then((response) => {
      if (response !== null && response.data === null && this.state.searchText.length > 0) {
        this.setState({
          totalSize: 0,
          currentPage: 0,
          currentPageData: []
        })
      }
      if (response !== null && response.status === 1) {
        response.data.results.map((value, index) => (console.log("values pages",value.isActive)))
        this.setState({
          totalSize: response.data.count,
          currentPage: page,
          currentPageData: response.data.results.map((value, index) => ({...value, srno: (page - 1) * this.state.sizePerPage + index + 1,validFrom:moment(value.validFrom).format('DD-MM-YYYY'),validTill:moment(value.validTill).format('DD-MM-YYYY'), Active:<Switch  checked={value.isActive}/>})),
        })
      }
    }).catch((error) => {
      errorToast(SOMETHING_WENT_WRONG)
      console.log("getPaginatedSpocReportsTables (FulfilledRequest.js)", error)
    })
    this.setState({
      isLoading : false
    })
  }

  handleSendMail = () =>{
    this.setState({
      isSendDisable:true
    })
    // if (this.state.fromDate == "" || this.state.toDate == ""){
    //   errorToast(PLEASE_ENTER_REQUIRED_FIELD)
    // }
    // else{
      let body = {
        "email":this.state.spocEmail,
        // "from_date":this.state.fromDate,
        // "to_date":this.state.toDate,
        "active": this.state.activeStatus

      }
      if((this.state.userRole === 'isSuperUser')||(this.state.userRole === 'isTmlManager')){
        if (this.state.vendor !== 'all') body['vendor'] = this.state.vendor
        if (this.state.userRole === 'isSuperUser'){
          body['action'] = admin_spoc_report_mail
          body['module'] = admin_spoc_report
        }
      }
      else{
        body['vendor'] = this.props.loginData.vendorName.toUpperCase()
      }
      getAPIResponse(config.sendSpocEmpDetails, "POST", body).then((response) => {
        if (response.status === 500){
          console.log("getPaginatedSpocReportsTables (FulfilledRequest.js)", response)
          return null
        } else {
          return response.json()
        }  
      }).then((response) => {
        console.log("data:",response)
        if (response !== null && response.status === 1){
          console.log("data:",response.data)
          successToast(response.message)
        }else{
          errorToast(response.message)
        }
      }).catch((error) => {
        errorToast(SOMETHING_WENT_WRONG)
        console.log("handleSendMail (handleSendMail.js)", error)
      })
    // }
    this.setState({
      isSendDisable:false
    })
    
  }

  getVendorList = () => {
    getAPIResponse(config.getVendorList , "GET" , {})
    .then((response) => {
      if(response.status === 500){
        errorToast(INTERNAL_SERVER_ERROR)
        console.log("getVendorList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null && data.status === 1){
        let vendorData = data.data.map((data)=>({value:data.vendorCode,label:data.vendorName.toUpperCase()}))
        vendorData.splice(0, 0, {"value": "all","label": "All Vendors"});

        this.setState({
          vendorOption : vendorData 
        })
      }
      else {
        console.log("getVendorList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getVendorList() in Request",error)
    })
  }

  onHandleVendorDropdown = (e) => {
    this.setState({
        vendor:e,
        vendorErrorMessage:""
    },()=>this.getSpocReportsTable())
  }

  handleSwitchToggle = (checked, id) => {
  // Example: update the state or call an API
  console.log('Toggled ID:', id, 'to', checked);
  // Optionally update the state:
  const updatedData = this.state.currentPageData.map(item =>
    item.id === id ? { ...item, isActive: checked, Active: <Switch checked={checked} onChange={(checked) => this.handleSwitchToggle(checked, id)} /> } : item
  );
  this.setState({ currentPageData: updatedData });
};

  onHandleActiveDropdown = (e) => {
    this.setState({
        activeStatus:e,
        vendorErrorMessage:""
    },()=>this.getSpocReportsTable())
  }

  handleMasking = ()=>{
    this.setState(prevState=>({...prevState,isDataMasked:!prevState.isDataMasked,currentPageData:prevState.currentPageData.map(obj=>({...obj,email:!prevState.isDataMasked ? obj.maskedEmail:obj.unmaskedEmail }))}))
  }

  render() {
    const tableColumns=this.state.headers
    return (
      <Fragment>
          <ToolkitProvider
            keyField="id"
            data={this.state.currentPageData}
            // columns={this.state.headers}
            columns={tableColumns}
            // formatExtraData = {this.state.isRetryDisable}
            search
          >
            {(props) => (
              <div>
                
                <div className="validate-spoc-email">
                  <div className="align-center"> 
                    <div className='button-mail' onClick={()=>{this.handleSendMail()}}>
                      <FaEnvelope style= {{display:'inline-block', alignItems:'center'}} size={'1rem'}/> 
                        {"Send to Mail"}
                    </div>
                    <div className='page-total' >
                      {`Total Record: ${this.state.totalSize}`}
                    </div>
                    {/* <div className='page-total' onClick={this.handleMasking}>
                      {this.state.isDataMasked ? <FaEye/>:<FaEyeSlash/>}
                    </div> */}
                    <div className={this.state.currentPageData.length !== 0? 'page-total': 'page-total'}onClick={this.handleMasking}>
                        <Tooltip title={this.state.isDataMasked ? 'Show' : 'Hide'} color="#FFF" overlayInnerStyle={{ color: '#000000' }}>
                          <div>
                            {this.state.isDataMasked ? <FaEye /> : <FaEyeSlash />}
                          </div>
                        </Tooltip>
                      </div>
                  </div>
                  {/* <Button disabled={this.state.isSendDisable} className="primary-button" onClick={()=>{this.handleSendMail()}}>Send to Mail</Button> */}
                  <div className="text-right">
                  { (this.state.userRole === 'isSuperUser')||(this.state.userRole === 'isTmlManager') ?
                    <Select className="input-box-select" value={this.state.vendor} placeholder="Please Select Vendor" options={this.state.vendorOption} onChange={(e) => { this.onHandleVendorDropdown(e) }} disabled={this.state.isValidUser} />
                    : null
                  }  
                    {
                    // <RangePicker
                      // className="input-box-select"
                      // defaultValue={[dayjs(this.state.fromDate),dayjs(this.state.toDate)]}
                      // format="DD-MM-YYYY"
                      // onChange={(range) => this.setDateRange(range)}
                      // maxDate={!this.state.showDefaultDateRange ? this.state.maxDate : dayjs(this.state.toDate).add(this.state.dataRange,'month')}
                      // minDate={!this.state.showDefaultDateRange ? this.state.minDate: dayjs(MINDEFAULTVALUE)}
                      // onCalendarChange={(date)=> this.onCalendarChange(date)}
                      // onOpenChange={(open,value)=>{this.handleOpenChange(open,value)}}
                      // value={[dayjs(this.state.fromDate),dayjs(this.state.toDate)]}
                      // onClick={(date)=> this.onCalendarChange(date)}
                    // />
                    }
                    <Select className="input-box-select" value={this.state.activeStatus} placeholder="Please Select Vendor" options={this.state.ActiveOption} onChange={(e) => { this.onHandleActiveDropdown(e) }} />
                    <SearchBar
                      {...props.searchProps}
                      className="custome-search-field"
                      placeholder="Search"
                    />
                  </div>
                  
                </div>

                <BootstrapTable
                  pagination={paginationFactory({page: this.state.currentPage, sizePerPage: this.state.sizePerPage, totalSize: this.state.totalSize, hideSizePerPage: true})}
                  wrapperClasses="table-responsive"
                  striped
                  {...props.baseProps}
                  remote
                  columns={tableColumns}
                  onTableChange={(type, { page, searchText }) => {
                    console.log("Props:::",props, type)
                    if (type === SEARCH) {
                      this.setState({searchText: searchText})
                      this.getPaginatedSpocReportsTables(1, searchText)
                    }
                    if (type === PAGINATIONS) {
                      this.getPaginatedSpocReportsTables(page, this.state.searchText)
                    }       
                  }}
                  noDataIndication={emptyDataMessage()}
                />
              </div>
            )}
          </ToolkitProvider>
          {
            this.state.isLoading ?
              <div className="loader">
                <div></div>
              </div> : null
          }
      </Fragment>
    );
  }
}
const mapStateToProps = (state) => ({
  loginData: state.loginInfo.login
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(SpocReportsTable);
