import React, { Component } from "react";
import Link from "next/link";
import { connect } from "react-redux";
import logo from '../public/img/tata_logo.svg'
import Image from "next/image";
import { withRouter } from "next/router";
import { MdOutlineArrowDropDown } from "react-icons/md";
import { AiOutlineClose } from "react-icons/ai";
import { navigations, requestDropDown , routerAndRole} from "../constants/navigations";
import { login } from "../redux/actions/loginActions";
import * as constants from '../constants/constants'
import { FaBookOpen } from "react-icons/fa";

class Sidebar extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      activeKey: "Indents",
      dropdownShow : false,
      navigations:[]
    };

    this.path = ["/Onboard" , "/UpdateRequest"]
  }

  componentDidMount(){
    const isNonTmlManager = this.props.loginData.isNonTmlManager;
    const isSPOC = this.props.loginData.isSPOC
    const isTmlManager = this.props.loginData.isTmlEmployee
    const isAdmin = this.props.loginData.isSuperUser
    try {
      let route = routerAndRole(isTmlManager, isSPOC, isNonTmlManager,isAdmin)
      if(route.role){
        this.setState({
          navigations:navigations[route.role]
        })
      }
      else{
        window.location.replace(constants.FRONTEND_URL + '/login');
      }
      
    }
    catch(e){
      window.location.replace(constants.FRONTEND_URL + '/login');
    }

  }

  handleDropdown = () => {
    this.setState({
      dropdownShow:!this.state.dropdownShow
    })
  }

  hideSidebar = () => {
    this.props.login({
      userName: this.props.userInfo.userName,
      userEmail: this.props.userInfo.userEmail,
      profileImg: "",
      empId: "",
      isLogin: true,
      vendorName:this.props.userInfo.vendorName,
      isTmlEmployee:this.props.userInfo.isTmlEmployee,
      isSPOC:this.props.userInfo.isSPOC,
      isNonTmlManager:this.props.userInfo.isNonTmlManager,
      vendorCompanyName:this.props.userInfo.vendorCompanyName,
      isSidebarShown:false
    })
  }

  render() {
    const { router } = this.props
    const pathname = router.asPath;
    // let navigation = []
    
    // screen list
    // let navigation = []
    
    

    return (

      <aside className={this.props.isSidebarShown ? "main-sidebar show-sidebar": "main-sidebar"}>
        {this.props.isSidebarShown ? 
              <span className="close-button" onClick={this.hideSidebar}>
                <AiOutlineClose/>
              </span> : false}
        <Link href="/Dashboard">
          <span className="brand-link">
            <span className="text-center font-weight-light display-center">
              <Image 
              src={logo} 
              alt="logo-images"
              width={100}
              height={100}
              />
            </span>
          </span>
        </Link>
        
        <div className="scroll-bhehaviour">
          <div className="sidebar-flex-manaul">
            <div className="mt-2 sidebar-menu">
              <ul
                className="nav"
              >
                {this.state.navigations.map((value, index) => {
                  return (
                    <>
                      <li className={pathname === value.link ? "nav-item nav-active" : "nav-item"}>
                        <Link href={value.link}>
                          <div className="nav-link flex-d" onClick={value.isDropdown ? this.handleDropdown : null}>
                            <div className="flex">
                              <Image
                                src={value.src}
                                alt="logo-images"
                                width={value.width}
                                height={value.height}
                                className="nav-icon"
                              />
                              <div className="tab-name">{value.tabName}</div>
                            </div>
                            {value.isDropdown ?
                              <div>
                                <MdOutlineArrowDropDown className="dropdown-icon" />
                              </div> : null
                            }
                          </div>
                          <div>
                          </div>
                        </Link>
                      </li>
                      {
                        this.state.dropdownShow && value.isDropdown ?
                          <div className="dropdown-data">
                            <ul>
                              {
                                requestDropDown.map((value, index) => {
                                  return (
                                    <Link href={value.href} key={index}>
                                      <div className="content">
                                        <span className="dash">-</span>
                                        <li className={pathname === value.href ? "dropdown-active " : null}>{value.name}</li>
                                      </div>
                                    </Link>
                                  )
                                })
                              }
                            </ul>
                          </div>
                          : null
                      }
                    </>
                  )
                })}
              </ul>
            </div>
          </div>
          <div className="user-manaul-container">
            <FaBookOpen style={{ color: "white" }} />
            <Link style={{ color: "white" }} href={"/documents/Partner-Onboarding-Help-and-Support.pdf"} target="_blank">User Manual</Link>
          </div>
        </div>
      </aside>
    );
  }
}
const mapStateToProps = (state) => ({
  counter: state.counter.value,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
  isSidebarShown : state.loginInfo.login.isSidebarShown,
  userInfo:state.loginInfo.login
});

const mapDispatchToProps = {
  login:login
};
export default withRouter(connect(mapStateToProps, mapDispatchToProps)(Sidebar));
