import React, { Component } from "react";
import { connect } from "react-redux";
import { setId, managerBoxCount } from "../../redux/actions/counterActions";
import Accordion from "react-bootstrap/Accordion";
import { getAPIResponse } from "../../constants/Utils";
import * as config from '../../constants/config'
import * as constants from '../../constants/constants'
import approval from '../../public/img/employee_pending.svg'
import Image from "next/image";
import approval_done from '../../public/img/approval-done.svg'

class ManagerBox extends Component {
  static getInitialProps({ store }) {}

  constructor(props) {
    super(props);
    this.state = {
      activeKey: "Indents",
      countData: {},
    };
    //this.handleSubmit.bind(this);
  }

  componentDidMount() {
    this.getManagerDashboardCount();
  }

  getManagerDashboardCount = () =>{
    let api_url = config.getManagerDashboardCount
    if (localStorage.getItem("role") == 'isSuperUser'){
      let queryParams = new URLSearchParams({
        "action": constants.admin_approval_dashboard_count,
        "module": constants.admin_approval_dashboard
      })
      api_url = `${config.getAdminApprovalDashboardCount}?${queryParams.toString()}`
    }
    console.log("getManagerDashboardCount api_url:",api_url,this.props?.loginData)
    getAPIResponse(api_url , "GET" , {})
    .then((response)=>{
      if(!response.ok){
        console.log("getManagerDashboardCount() in ManagerBox",response)
        return null
      }
      else {
        return response.json()
      }
    })
    .then((data)=>{
      if(data !== null){
        if(data.status === 1){
          this.props.managerBoxCount({
            countData: data.data,
          });
        }
        else {
          console.log("getManagerDashboardCount() in ManagerBox",data)
        }
      }
      else {
        console.log("getManagerDashboardCount() in ManagerBox",data)
      }
    })
    .catch((error)=>{
      console.error("getManagerDashboardCount() in ManagerBox",error)
    })
  }

  render() {
    return (
      <div>
        <div className="row">
          <div className="col-md-4 col-sm-6 col-12">
            <div className="info-box">
              <Image 
              src={approval}
              alt='pending'
              width={40}
              height={50}
              className="margin-to-image"
              />
              <div className="info-box-content">
                <span className="info-box-text"> Total Approvals Pending </span>
                <span className="info-box-number">
                  {this.props.countData.approval_pending}
                </span>
              </div>
            </div>
          </div>

          <div className="col-md-4 col-sm-6 col-12">
            <div className="info-box">
              <Image 
              src={approval_done}
              alt='pending'
              width={40}
              height={50}
              className="margin-to-image"
              />
              <div className="info-box-content">
                <span className="info-box-text">Total Approvals Accepted</span>
                <span className="info-box-number">
                  {this.props.countData.approval_accepted}
                </span>
              </div>
            </div>
          </div>
          </div>
        </div>
    );
  }
}
const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  countData: state.counter.countDataManager,
});

const mapDispatchToProps = {
  setId: setId,
  managerBoxCount: managerBoxCount,
};
export default connect(mapStateToProps, mapDispatchToProps)(ManagerBox);
