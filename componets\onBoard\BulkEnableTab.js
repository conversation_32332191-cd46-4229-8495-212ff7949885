import { Component } from 'react'
import { connect } from 'react-redux'
import AsyncSelect from "react-select/async"
import { getAPIResponse } from '../../constants/Utils';
import * as config from '../../constants/config';
import { <PERSON><PERSON>, Card, DatePicker, Divider, List, Modal, Tooltip, Upload } from 'antd';
import { VscClearAll } from 'react-icons/vsc';
import dayjs from 'dayjs';
import { Form } from 'react-bootstrap';
import AdminTable from './Admin/AdminTable';
import { FaArrowLeft, FaArrowRight, FaExclamationCircle } from 'react-icons/fa';
import { MdOutlineDownload, MdOutlineUploadFile } from 'react-icons/md';
import Title from 'antd/es/typography/Title';
import fileDownload from 'js-file-download';
import { errorToast, successToast } from '../../toast';
import Papa from 'papaparse';
import { INTERNAL_SERVER_ERROR, SOMETHING_WENT_WRONG } from '../../constants/message';
import dynamic from 'next/dynamic';

const Table = dynamic(() => import('antd').then(mod => mod.Table), { ssr: false });

const itemsPerPage = 10;

export class BulkEnable extends Component {
    constructor(props) {
        super(props);
        this.state = {
            employeeData: [],
            smartSearhText: "",
            employeeObjects: {},
            selectedEmployee: "",
            extendValidityTo: "",
            defaultHeaders: [
                { dataField: "id", text: "Sr. No.", sort: false },
                { dataField: "fullName", text: "Person Name", sort: false },
                { dataField: "domainId", text: "Domain ID", sort: false },
                { dataField: "email", text: "Email ID", sort: false },
                { dataField: "extendValidityTo", text: "Extend Validity To", sort: false },
                { dataField: "isactive", text: "Active", sort: false },
                { dataField: "action", text: "Action", sort: false }
            ],
            employeeFound: [
                { title: 'Person Name', dataIndex: 'fullName', key: 'fullName' },
                { title: 'Domain ID', dataIndex: 'domainId', key: 'domainId' },
                { title: 'Email ID', dataIndex: 'email', key: 'email' },
            ],
            employeeNotFound: [
                { title: 'Email ID', dataIndex: 'email', key: 'email' },
                { title: 'Status', dataIndex: 'status', key: 'status' },
            ],
            currentPage: 1,
            isModelOpen: false,
        }
    }

    onClear = () => {
        this.setState({
            smartSearhText: "",
            employeeObjects: {},
            selectedEmployee: "",
            extendValidityTo: "",
        })
    }

    getTotalPages = () => {
        const number = Math.ceil(this.state.employeeData.length / itemsPerPage);
        return number === 0 ? 1 : number;
    };

    getCurrentItems = () => {
        const employeeData = [...this.state.employeeData].map((value, index) => {
            return { id: index + 1, ...value }
        });
        const { currentPage } = this.state;
        const response = employeeData.sort((a, b) => b.id - a.id).map((value, index) => {
            return { ...value, id: index + 1 }
        });
        const startIndex = (currentPage - 1) * itemsPerPage;
        return response.slice(startIndex, startIndex + itemsPerPage);
    }

    handlePageChange(pageNumber) {
        this.setState({ currentPage: pageNumber });
    }

    isDisable = () => {
        return (this.state.selectedEmployee === "" || this.state.selectedEmployee == {}) ||
            (this.state.extendValidityTo === "" || this.state.extendValidityTo == null) ? true : false
    }

    onInputChangeForSmartSearch = (value) => {
        this.setState({
            smartSearhText: value,
        })
    }

    handleEmployeeSearch = (event) => {
        this.setState({
            selectedEmployee: {
                value: event.value, label: this.state.employeeObjects[event.value][0],
                domainId: this.state.employeeObjects[event.value][3],
                email: this.state.employeeObjects[event.value][4]
            }
        })
    }

    customLabel = (value, index) => {
        const employeeName = value.request
        this.setState(prevState => ({
            employeeObjects: {
                ...prevState.employeeObjects,
                [employeeName]: [value.fullName, value.vendor.vendorName, value.vendor.vendorCode, value.domainId, value.email],
            }
        }));

        return (
            <div key={`employee-details-${index}`} className="search-conatiner">
                <b><span key="fullName">{value.fullName}</span></b>
                <br key="br1" />
                <span key="employeeId" className="smartsearch">{value.employeeId}</span>
                <span key="separator2" className="smartsearch"> |  </span>
                <span key="vendor" className="smartsearch">{value.vendor.vendorName}</span>
                <br key="br2" />
                <span key="email" className="smartsearch">{value.email}</span>
            </div>
        )
    }

    smartSearchForEmployee = () => {
        if (this.state.smartSearhText !== "") {
            let searchParams = new URLSearchParams({
                "search_text": this.state.smartSearhText,
                "only_disable_accounts": true
            })
            return getAPIResponse(`${config.getVendorEmployeeDeatils}?${searchParams.toString()}`, 'GET', {})
                .then((response) => {
                    if (response.status === 500) {
                        errorToast(INTERNAL_SERVER_ERROR)
                        return null
                    }
                    else {
                        return response.json()
                    }
                }).then((data) => {
                    if (data !== null && data.status == 1) {
                        let options = []
                        data.data.map((value, index) => {
                            const isExist = this.state.employeeData.find(emp => emp.email === value.email);
                            if (!isExist) {
                                options.push({ value: value.request, label: this.customLabel(value, index), ...value })
                            }
                        })
                        return options
                    }
                })
                .catch((error) => {
                    errorToast(SOMETHING_WENT_WRONG)
                })
        }
    }

    handleDate = (value) => {
        this.setState({
            extendValidityTo: value
        })
    }

    handledAddEmployee = () => {
        const extendValidityTo = dayjs(this.state.extendValidityTo).format("YYYY-MM-DD")
        const dateError = this.validateExtendValidityDate(extendValidityTo, false);
        if (dateError) {
            errorToast(dateError);
            return false;
        }
        const employeeData = [...this.state.employeeData];
        employeeData.push({
            fullName: this.state.selectedEmployee.label,
            email: this.state.selectedEmployee.email,
            domainId: this.state.selectedEmployee.domainId,
            extendValidityTo: extendValidityTo,
            isactive: true
        });
        this.setState({ employeeData }, () => {
            this.onClear();
        })
    }

    handledRemoveEmployee = (email) => {
        const employeeData = [...this.state.employeeData];
        const afterRemoveData = employeeData.filter((emp) => emp.email != email);
        this.setState({ employeeData: afterRemoveData })
    }

    handledToggleActiveEmployee = (email) => {
        const employeeData = [...this.state.employeeData];
        employeeData.map(data => {
            if (data.email === email) {
                data.isactive = !data.isactive
            }
        })
        this.setState({ employeeData })
    }

    handledSubmitRequest = () => {
        if (this.state.employeeData.length > 0) {
            const requestBody = {
                employees: this.state.employeeData,
                action: "Bulk Enable - Employees ValidityTo & Status",
                module: "Bulk Enable",
                // createdBy:this.props.loginData.empId,
            }
            const url = config.adminBulkEmployeeValidityAndStatusEnable
            getAPIResponse(url, 'POST', requestBody)
                .then((response) => {
                    if (response.status === 500) {
                        errorToast(INTERNAL_SERVER_ERROR)
                        console.log("handledSubmitRequest() in bulkExtensionTab",error)
                        return null
                    }
                    else {
                        return response.json()
                    }
                }).then((response) => {
                    if(response != null){
                        if (response.status == 1) {
                            if (response.data.is_request_in_process) {
                                successToast(response.message)
                            }
                            if (response.data.request_already_exist.length > 0) {
                                this.showCsvDataModal([],[],[],response.data.request_already_exist)
                            }
                            this.setState({
                                employeeData: [],
                                smartSearhText: "",
                                employeeObjects: {},
                                selectedEmployee: "",
                                extendValidityTo: "",
                                currentPage: 1
                            })
                        }else{
                            errorToast(response.message == null ? SOMETHING_WENT_WRONG : response.message)
                            console.log("handledSubmitRequest() in bulkExtensionTab",error)
                        }
                    }
                })
                .catch((error)=>{
                    console.log("handledSubmitRequest() in bulkExtensionTab",error)
                })
        }
    }

    normalizeDate = (dateString) =>{
        const regexDDMMYYYY = /^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-(\d{4})$/;
        const regexYYYYMMDD = /^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/;
        
        let match;
    
        // Check for dd-mm-yyyy format
        if ((match = dateString.match(regexDDMMYYYY))) {
        const [day, month, year] = match.slice(1);
        return `${year}-${month}-${day}`
        }
    
        // Check for yyyy-mm-dd format
        if ((match = dateString.match(regexYYYYMMDD))) {
        const [year, month, day] = match.slice(1);
        return `${year}-${month}-${day}`
        }

        return null;
    }
    validateDate = (date) => {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(date)) return false;
        const parsedDate = new Date(date);
        return !isNaN(parsedDate.getTime());
    };

    groupErrorsByRow = (errors) => {
        return errors.reduce((acc, error) => {
            if (!acc[error.row]) {
                acc[error.row] = [];
            }
            acc[error.row].push({ field: error.field, value: error.value });
            return acc;
        }, {});
    };

    isDuplicate(value, set) {
        if (set.has(value)) {
            return true;
        } else {
            set.add(value);
            return false;
        }
    }

    showValidationErrors = (invalidEntries) => {
        Modal.error({
            title: `CSV Validation Errors (${invalidEntries.length})`,
            width: 800,
            style: { top: 20 },
            content: (
                <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
                    <List
                        size="small"
                        dataSource={invalidEntries}
                        renderItem={({ row, errors }) => (
                            <List.Item>
                                <List.Item.Meta
                                    avatar={<FaExclamationCircle style={{ color: 'red' }} />}
                                    title={`Row ${row}`}
                                    description={
                                        <div style={{ color: 'black' }}>
                                            {errors.join(' | ')}
                                        </div>
                                    }
                                />
                            </List.Item>
                        )}
                    />
                </div>
            ),
            footer: (
                <div className='d-flex' style={{ justifyContent: 'flex-end' }}>
                    <Button type='primary' onClick={() => Modal.destroyAll()} className='button-mail' > Close </Button>
                </div>
            ),
        });
    };

    validateExtendValidityDate(extendValidityTo, includeDate = true) {
        const today = dayjs();
        const sixMonthsFromNow = today.add(6, 'month').add(1, 'day');
        const extendValidityDate = dayjs(extendValidityTo);

        // Construct date string for error messages if includeDate is true
        const dateString = includeDate ? `: "${extendValidityTo}"` : '';

        // Check if the date is after today and within 6 months + 1 day
        if (!extendValidityDate.isAfter(today)) {
            return `Extend Validity To date must be in the future${dateString}`;
        } else if (extendValidityDate.isAfter(sixMonthsFromNow)) {
            return `Extend Validity To date must be within the next 6 months${dateString}`;
        }
        return null; // No error
    }

    handleCardClick = (action, file = null) => {
        if (action === "download") {
            const csvContent = 'Email,Extend Validity(yyyy-mm-dd) To\n';
            fileDownload(csvContent, `BulkExtensionTemplate.csv`, 'text/csv');
        }

        if (action === "upload") {
            Papa.parse(file, {
                complete: (result) => {
                    const data = result.data;

                    if (data.length === 0 || (data.length === 1 && data[0].length === 1 && !data[0][0].trim())) {
                        errorToast('The CSV file is empty.');
                        return;
                    }

                    const headers = data[0].map(header => header.trim());
                    const rows = data.slice(1);
                    if (rows.length === 0) {
                        errorToast('The CSV file is empty.');
                        return;
                    }
                    const domainSet = new Set();
                    const rowErrors = {};

                    const jsonData = rows.map((row, index) => {
                        const rowIndex = index + 2;
                        const email = row[headers.indexOf('Email')];
                        const extendValidityTo = this.normalizeDate(row[headers.indexOf('Extend Validity To')]);

                        if (!rowErrors[rowIndex]) {
                            rowErrors[rowIndex] = [];
                        }

                        if (!this.validateDate(extendValidityTo)) {
                            rowErrors[rowIndex].push(`Invalid Extend Validity To: "${extendValidityTo}"`);
                        } else {
                            const dateError = this.validateExtendValidityDate(extendValidityTo);
                            if (dateError) {
                                rowErrors[rowIndex].push(dateError);
                            }
                        }

                        if (this.isDuplicate(email, domainSet)) {
                            rowErrors[rowIndex].push(`Duplicate Email: "${email}"`);
                        }
                        return { email, extendValidityTo };
                    });

                    const filterErrors = Object.entries(rowErrors).filter(([rows, errors]) => errors.length > 0);
                    const invalidEntries = filterErrors.map(([row, errors]) => ({
                        row: parseInt(row),
                        errors
                    }));

                    if (invalidEntries.length > 0) {
                        this.showValidationErrors(invalidEntries);
                    } else {
                        this.getEmployeeData(jsonData);
                    }
                },
                header: false,
                skipEmptyLines: true
            });
        }
    };

    getEmployeeData = (data) => {
        const email = data.map(x => x.email);
        return getAPIResponse(config.getNDAEmployeesByDomainIds, 'POST', { email })
            .then((response) => {
                if (response.status === 500) {
                    errorToast(INTERNAL_SERVER_ERROR)
                    return null
                } else {
                    return response.json()
                }
            }).then((response) => {
                if (response !== null && response.status == 1) {
                    const not_found = response.data.domain_ids_not_found.map(data => {
                        return {
                            'email': data,
                            'status': 'Not Found'
                        }
                    })
                    const found = response.data.nda_employee_details.map(data => ({
                        fullName: data.DisplayName,
                        email: data.userPrincipalName,
                        domainId: data.samAccountName
                    }))
                    this.showCsvDataModal(data, found, not_found)
                }
            })
    }

    showCsvDataModal = (data = [], dataFound = [], dataNotFound = [], requestAlreadyExist = []) => {
        if (requestAlreadyExist.length === 0) {
            successToast('CSV file imported successfully!');
        }
        console.log(dataNotFound, "dataNotFound");
        console.log(dataFound, "dataFound");
        console.log(data, "data");
        this.setState({ isModelOpen: false })
        const modal = requestAlreadyExist.length > 0 ? Modal.error : Modal.info
        modal({
            title: requestAlreadyExist.length > 0 ? 'Update Request Found' : 'CSV Import Data',
            width: 800,
            centered: true,
            content: (
                <>
                    {
                        requestAlreadyExist.length === 0 ?
                            (
                                <>
                                    <Divider className='my-1' />
                                    <Title level={5}>{`Employee Found (${dataFound.length})`}</Title>
                                    <Table
                                        columns={this.state.employeeFound}
                                        dataSource={dataFound}
                                        rowKey={(record, index) => index}
                                        pagination={{
                                            pageSize: 10,
                                        }}
                                        scroll={{
                                            y: 240,
                                        }}
                                    />

                                    <Title level={5}>{`Employee data not found for the following domain IDs (${dataNotFound.length})`}</Title>
                                    <Table
                                        columns={this.state.employeeNotFound}
                                        dataSource={dataNotFound}
                                        rowKey={(record, index) => index}
                                        pagination={{
                                            pageSize: 5,
                                        }}
                                        scroll={{
                                            y: 240,
                                        }}
                                    />
                                </>
                            ) : (
                                <>
                                    <Title level={5}>{`A request to update the employee's data is already in progress (${requestAlreadyExist.length})`}</Title>
                                    <Table
                                        columns={this.state.employeeFound}
                                        dataSource={requestAlreadyExist}
                                        rowKey={(record, index) => index}
                                        pagination={{
                                            pageSize: 5,
                                        }}
                                        scroll={{
                                            y: 240,
                                        }}
                                    />
                                </>
                            )
                    }
                </>
            ),
            ...(requestAlreadyExist.length === 0 ? {
                footer: (
                    <div className='d-flex mt-3' style={{ justifyContent: 'flex-end' }}>
                        <Button type='primary' onClick={() => {
                            this.setEmployeeData(data, dataFound);
                            Modal.destroyAll();
                        }} className='button-mail' > Import </Button>
                    </div>
                )
            } : { footer: null }),
            maskClosable: true,
            closable: true
        });
    };

    setEmployeeData = (mapping, data) => {
        console.log(mapping, data, "mapping, data");
        
        const result = data.map((value, index) => {
            const domain = mapping.find(x => x.email === value.email);
            console.log(domain, "domain");
            
            return {
                ...value,
                extendValidityTo: domain.extendValidityTo,
                isactive: true
            }
        })

        this.setState({
            employeeData: [...this.state.employeeData, ...result],
        })
    }

    onInputChange2 = (value) => {
        console.log("Input Value:", value);
        this.setState({ smartSearhText: value });
        return value; // Returning value prevents unnecessary re-renders in AsyncSelect
      };
    
      smartSearch = async (inputValue) => {
        if (!inputValue) return [];
    
        this.setState({ loader: true });
    
        let searchParams = new URLSearchParams({
          "search_text":inputValue,
          "include_disable_accounts": false
        })
    
        try {
          const response = await getAPIResponse(`${config.getVendorEmployeeDeatils}?${searchParams.toString()}` , 'GET' , {})
          if (response.status === 500) {
            errorToast(SOMETHING_WENT_WRONG);
            console.error("smartSearchForEmployee() Error:", response);
            return [];
          }
    
          const data = await response.json();
    
          if (data && data.status === 1) {
            let options = data.data.map((value, index) => ({
              value: value.request,
              label: this.customLabel(value, index),
            }));
            console.log("Fetched Options:", options);
            return options;
          } else {
            console.log("No valid data:", data);
            return [];
          }
        } catch (error) {
          console.error("API Call Error:", error);
          errorToast(SOMETHING_WENT_WRONG);
          return [];
        } finally {
          this.setState({ loader: false });
        }
      }    

    render() {
        const isDisable = this.isDisable();
        const { currentPage } = this.state;
        const totalPages = this.getTotalPages();
        const currentItems = this.getCurrentItems();
        return (
            <>
                <div id='bulk_extension_header'>
                    <div className='row'>
                        <div className='col-3 pl-0'>
                            <Form.Label>Employee Detail</Form.Label><sup style={{ color: 'red' }}>*</sup>
                            <AsyncSelect
                                placeholder={"Search By Employee Name"}
                                name='bulkExtensionSelect'
                                instanceId={'bulkExtensionSelect'}
                                className='myclass'
                                // loadOptions={this.smartSearchForEmployee}
                                onChange={(e) => { this.handleEmployeeSearch(e) }}
                                // onInputChange={(e) => { this.onInputChangeForSmartSearch(e) }}
                                onInputChange={this.onInputChange2}
                                loadOptions={this.smartSearch}
                                value={this.state.selectedEmployee}
                                noOptionsMessage={() => !this.state.smartSearhText ? "Enter Employee Name" : "No Data Found"}
                                classNames='w-80'
                                styles={{
                                    menu: (provided) => ({
                                        ...provided,
                                        zIndex: 3
                                    })
                                }}
                            />
                        </div>
                        <div className='col-3'>
                            <Form.Label>Extend Validity To</Form.Label><sup style={{ color: 'red' }}>*</sup>
                            <DatePicker
                                style={{ fontFamily: 'sans-serif' }}
                                className="form-control"
                                name="bulkExtensionDate"
                                placeholderText="Extend Valid To"
                                minDate={dayjs().add(1, 'day')}
                                maxDate={dayjs().add(6, 'month').add(1, 'day')}
                                dateFormat="yyyy-MM-dd"
                                value={this.state.extendValidityTo}
                                onChange={(e) => { this.handleDate(e) }}
                            />
                        </div>
                        <div className='validate'>
                            <div className='change-cursor' onClick={this.onClear}>
                                <Tooltip title="Clear" color="#FFF" overlayInnerStyle={{ color: "#000000" }}>
                                    <div className="clear-button w-20"><VscClearAll className="clear-icon" /></div>
                                </Tooltip>
                            </div>

                            <div className={`ml-2 button-mail ${isDisable ? 'disabled' : ''}`} onClick={() => { isDisable ? null : this.handledAddEmployee() }}>{"Add Employee"}</div>
                        </div>
                        <div className='mt-auto ml-auto'>
                            <div className={`mr-2 button-mail ${this.state.employeeData.length > 0 ? 'disabled' : ''}`} onClick={() => this.state.employeeData.length > 0 ? null : this.setState({ isModelOpen: !this.state.isModelOpen })}>{"Upload CSV"}</div>
                        </div>
                    </div>
                </div>
                <div id='bulk_extension_body' className="overflow-auto mt-4" style={{ height: '71.5vh' }}>
                    <AdminTable
                        headers={this.state.defaultHeaders}
                        currentPageData={currentItems}
                        noDataMessage={"No Data. Please Add an Employee."}
                        handledRemoveEmployee={this.handledRemoveEmployee}
                        handledToggleActiveEmployee={this.handledToggleActiveEmployee}
                        isActiveDisable={true}
                    />
                    <div className='pagination justify-content-between'>
                        <div className='row m-0 p-0'>
                            <div className={`ml-0 ${currentPage !== 1 ? 'button-paginate' : 'button-paginate disabled'}`} onClick={currentPage !== 1 ? () => this.handlePageChange(currentPage - 1) : null}>
                                <FaArrowLeft style={{ display: 'inline-block', alignItems: 'center' }} size={'0.8rem'} />
                                {" Previous"}
                            </div>
                            <div className={'page-label'}>{this.state.currentPage}</div>
                            <div className={currentPage !== totalPages ? 'button-paginate' : 'button-paginate disabled'} onClick={currentPage !== totalPages ? () => this.handlePageChange(currentPage + 1) : null}>
                                {"Next "}
                                <FaArrowRight style={{ display: 'inline-block', alignItems: 'center' }} size={'0.8rem'} />
                            </div>
                        </div>
                        <div className='ml-0 d-flex'>
                            <div className={`${this.state.employeeData.length > 0 ? 'button-paginate' : 'button-paginate disabled'}`} onClick={this.state.employeeData.length > 0 ? () => this.setState({ employeeData: [] }) : null}>
                                {"Clean Table"}
                            </div>
                            <div className={`${this.state.employeeData.length > 0 ? 'button-paginate' : 'button-paginate disabled'}`} onClick={this.state.employeeData.length > 0 ? () => this.handledSubmitRequest() : null}>
                                {"Submit"}
                            </div>
                        </div>
                    </div>
                </div>
                <Modal
                    title="Bulk Enable - Add Employee using CSV"
                    open={this.state.isModelOpen}
                    footer={[]}
                    centered
                    onCancel={() => this.setState({ isModelOpen: !this.state.isModelOpen })}
                >
                    <p>Manage your data by downloading a template or uploading a file.</p>

                    <div className='d-flex justify-content-around'>
                        <div id="download" className='w-50 h-50'>
                            <Card
                                className='d-flex flex-column align-items-center justify-content-center card-spacing clickable-card'
                                style={{ textAlign: 'center', height: '200px' }}
                                onClick={() => this.handleCardClick('download')}
                            >
                                <MdOutlineDownload color='#0c3057' size={'2.5rem'} />
                                <Title level={5}>Download Template</Title>
                                <p>Get a CSV template to start importing your data.</p>
                            </Card>
                        </div>
                        <div id='upload' className='w-50 h-50'>
                            <Upload
                                accept='.csv'
                                showUploadList={false}
                                beforeUpload={(file) => {
                                    this.onClear();
                                    this.handleCardClick('upload', file);
                                    return false;
                                }}
                            >
                                <Card
                                    className='d-flex flex-column align-items-center justify-content-center card-spacing clickable-card'
                                    style={{ textAlign: 'center', height: '200px' }}
                                >
                                    <MdOutlineUploadFile color='#0c3057' size={'3rem'} />
                                    <Title level={4}>Upload File</Title>
                                    <p>Upload your CSV file to import data.</p>
                                </Card>
                            </Upload>
                        </div>
                    </div>
                </Modal>
            </>
        )
    }
}

const mapStateToProps = (state) => ({})

const mapDispatchToProps = {}

export default connect(mapStateToProps, mapDispatchToProps)(BulkEnable)