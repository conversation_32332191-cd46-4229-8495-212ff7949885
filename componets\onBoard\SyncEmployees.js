import { Component } from "react";
import { connect } from "react-redux";
import {decrementCounter,incrementCounter,step1} from "../../redux/actions/counterActions";
import Router from "next/router";
import { DatePicker } from "antd";
import React from "react";
import '../../styles/Home.module.css'
import { FormControl, InputGroup,Col, Row, Button, Form, Spinner} from "react-bootstrap";
import Select from "react-select";
import { getAPIResponse } from '../../constants/Utils'
import * as config from '../../constants/config'
import '../../styles/Home.module.css'
import { login, logOut } from "../../redux/actions/loginActions";
import makeAnimated from 'react-select/animated';
import { withRouter } from "next/router";
import { VscClearAll } from "react-icons/vsc";
import { Gender, UpdateEmailRegex } from "../../constants/constants";
import { Expiry<PERSON><PERSON><PERSON><PERSON><PERSON>e, INTERNAL_SERVER_ERROR, MANAGER_DETAILS_NOT_FOUND, MANAGER_VENDOR_CANNOT_SAME, PLEASE_ENTER_REQUIRED_FIELD, PLEASE_ENTER_VALID_EAMIL_ID, YEARS_OF_EXPERIENCE_ERROR,ERROR_MESSAGE_FOR_VALID_FROM,PLEASE_ENTER_SAMACCOUNT_NAME,PLEASE_ENTER_MANAGER,PLEASE_VALIDATE_DOMAIN_ID, SOMETHING_WENT_WRONG, FOR_IT_EMPLOYEES_MANAGR_SHOULD_BE_TML_MANAGER, PLEASE_SELECT_EMPLOYEE_TYPE,PLEASE_ENTER_VALID_MOBILE_NO} from "../../constants/message";
import dayjs from "dayjs";
import { successToast, errorToast } from "../../toast"
import { EmployeeType } from "../../constants/constants";
import { FaSearch } from "react-icons/fa";
import { Tooltip } from 'antd';
import { FaCheck } from "react-icons/fa";
import BouncingDotsLoader from "../BouncingLoader";
import { FaEye, FaEyeSlash,FaInfoCircle } from "react-icons/fa";


const animatedComponents = makeAnimated();
class CreateUpdateRequest extends Component {
  static getInitialProps({ store }) {}

  constructor(props) {
    super(props);
    this.state = {
      personEmail: '',
      personMobile: '',
      vendor: null,
      middleName: '',
      firstName: "",
      lastName: "",
      locationOption: [],
      manager: "",
      emailfag: true,
      positionPerson: "",
      PersNo: "",
      location: null,
      tmlManagerEmail: "",
      CompleteName: "",
      designation: "",
      validityFrom: "",
      validityTo:"",
      validityToNew: "",
      validityFromNew: "",
      response: [],
      employeeId: "",
      department: "",
      domainChecked: true,
      city: "",
      gender: Gender,
      genderType: null,
      vendorManager: "",
      vendorManagerEmail: "",
      project: [],
      projectName: "",
      designationList : [],
      tmlManagerPerno : "",
      vendorOption: [],
      tmlManagerCompanyCode :"",
      skillData : [], 
      skill : [],
      yearOfExperience : "",
      target : "",
      tmlLocation:"",
      buCompanyCode:"",
      buCompanyCodeList:[],
      swipeLocation:"",
      swipeLocationOptions:[],
      tmlRegion:"",
      tmlLocationData:[],
      tmlRegionData :[],
      isLoading:false,
      sameManagerField:"",
      tmlManagerTitle:"",
      tmlManagerData:[],
      email:false,
      internet:false,
      managerLoder : false ,
      managerErrorMessage : "",
      towerName:"",
      towerData:"",
      isTowerShown:false,
      employeeUpdate : "",
      isEnable:true,
      errorMessageForValidation : "",
      isErrorShown:false,
      personEmailMessage:"",
      experienceErrorMessage:"",
      vendorEmailMessage:"",
      emailCheck:false,
      internetCheck:false,
      isTmlManagerChange:false,
      samAccountName:"",
      employeeType:null,
      isEmailDisable:true,
      isManagerErrorShown:false,
      validateDomainID:false,
      validManagerID:false,
      subFunction2Text:"",
      pincode:"",
      ouLevel2ShortText:"",
      locationDesc:"",
      functionText:"",
      costCenter:"",
      companyDesc:"",
      subFunction2Text:"",
      source:"",
      loaderDomainID:false,
      loaderManagerID:false,
      sAMAccountName_manager:"",
      manager_last_name:"",
      manager_first_name:"",
      employeeTypeErrorShown:false,
      employeeTypeErrorMessage:"",
      sioCode:"",
      departmentData : [], 
      edataDepartment:[],
      departmentType: "",
      isDepartmentDisable: true,
      departmentTempData:[],
      showMaskedContent:false,
      l2ManagerEmail:"",
      l2ManagerName:"",
      l2FieldsEditable: false,
      isL2Editable: false,
      l2ManagerEmployeeId:"",
    };

  }

  componentDidMount() {
    
    this.getVendorList()
    this.getDesignationList()
    this.getProjectList(this.props.vendor)
    this.getTmlLocation()
    this.getSwipeLocationList()
    this.getBuCompanyCodeList()
    this.getTmlRegion()
    this.getSkill()
    this.getTower()
    this.getDepartment()
  }

  getVendorList = () => {
    getAPIResponse(config.getVendorList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getVendorList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
        data.data.map((value)=>{
          this.setState(prevState => ({
            vendorOption : [...prevState.vendorOption,{value:value.vendorCode,label:value.vendorName}]
          }))
        })
      }
      else {
        console.log("getVendorList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getVendorList() in Request",error)
    })

  }

  getVendorName = () => {
    let vendorname = this.props.vendor
    this.state.vendorOption.map((obj , index) => {
      if(vendorname === obj.vendorCode){
        this.setState({
          vendor:obj.vendorName
        })
      }
    })
  }

  getDesignationList = () =>{
    getAPIResponse(config.getDesignationList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getDesignationList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              designationList : [...prevState.designationList , {value : obj.designationCode , label : obj.designationName}]
            }))
          })
      }
      else {
        console.log("getDesignationList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getDesignationList() in Request",error)
    })
  }

  getProjectList = (vendor) => {
    if(vendor !== null){
    let queryParams = new URLSearchParams({
      'vendor': vendor
    })
    this.setState({
      project:[]
    })
      getAPIResponse(`${config.getProjectList}?${queryParams.toString()}` , "GET" , {})
      .then((response) => {
        if(!response.ok){
          console.log("getProjectList() in Request",response)
          return null
        }
        else{
          return response.json()
        }
      }).then((data) => {
        if(data !== null){
            data.data.map((obj, index) => {
              this.setState(prevState => ({
                project : [...prevState.project , {value : obj.projectCode , label : obj.projectName}]
              }))
            })
        }
        else {
          console.log("getProjectList() in Request",response)
        }
      })
      .catch((error) => {
        console.log("getProjectList() in Request",error)
      })
    }
  }

  getTmlLocation = () => {
    getAPIResponse(config.getTmlOffice , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getTmlLocation() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              tmlLocationData : [...prevState.tmlLocationData , {value : obj.officeCode , label : obj.officeName}]
            }))
          })
      }
      else {
        console.log("getTmlLocation() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getTmlLocation() in Request",error)
    })
  }

  getSwipeLocationList = () => {
    getAPIResponse(config.getSwipeLocationList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getSwipeLocationList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
        const swipeLocationOptions = data.data.map((obj) => ({value : obj.locationCode , label : obj.locationName}))
        this.setState(prevState => ({
          ...prevState,
          swipeLocationOptions
        }),() => {this.getSwipeLocationName()})
      }
      else {
        console.log("getSwipeLocationList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getSwipeLocationList() in Request",error)
    })
  }

  getSwipeLocationName = () => {
    let LocationName = this.props.swipeLocation
    this.state.swipeLocationOptions.map((obj , index) => {
      if(LocationName === obj.value){
        this.setState({
          vendor:obj
        })
      }
    })
  }

  getBuCompanyCodeList = () => {
    getAPIResponse(config.getBuCompanyCodeList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getBuCompanyCodeList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj) => {
            this.setState(prevState => ({
              buCompanyCodeList : [...prevState.buCompanyCodeList , {value : obj.compCode , label : obj.compName}]
            }))
          })
      }
      else {
        console.log("getBuCompanyCodeList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getBuCompanyCodeList() in Request",error)
    })
  }

  getTmlRegion = () => {
    getAPIResponse(config.getTmlRegion , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getTmlRegion() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              tmlRegionData : [...prevState.tmlRegionData , {value : obj.regionCode , label : obj.regionName}]
            }))
          })
      }
      else {
        console.log("getTmlRegion() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getTmlRegion() in Request",error)
    })
  }

  getSkill = () => {
    getAPIResponse(config.getSkillList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getSkill() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              skillData : [...prevState.skillData , {value : obj.skillCode , label : obj.skillName}]
            }))
          })
      }
      else {
        console.log("getSkill() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getSkill() in Request",error)
    })
  }

  getTower = () => {
    getAPIResponse(config.getTowerList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getSkill() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
        data.data.map((obj)=>{
          this.setState(prevState => ({
            towerData : [...prevState.towerData,{value:obj.towerCode , label:obj.towerName}]
          }),()=>{this.getTowerName()})
        })  
      }
      else {
        console.log("getTower() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getTower() in Request",error)
    })
  }

  getTowerName = () => {
    let towerName = this.state.towerName
    this.state.towerData.map((obj , index) => {
      if(towerName === obj.value){
        this.setState({
          towerName:obj
        })
      }
    })
  }

  getDepartment = () => {
    getAPIResponse(config.getDepartmentList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getDepartment() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              departmentData : [...prevState.departmentData , {value : obj.departmentCode , label : obj.departmentName, type: obj.empType}]
            }))
          })
      }
      else {
        console.log("getDepartment() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getDepartment() in Request",error)
    })
  }
  resetForm = () => {
    this.setState({
      CompleteName: "",
      employeeId : "",
      firstName : "",
      lastName : "",
      middleName:'',
      personMobile : '',
      personEmail : '',
      designation : "",
      gender : "",
      projectName : "",
      vendorManager : "",
      vendorManagerEmail : "",
      tmlManagerEmail : "",
      location:"",
      department: "",
      genderType:"",
      city:"",
      validityFrom:null,
      validityTo:null,
      manager:"",
      skill: [],
      yearOfExperience:"",
      tmlLocation:"",
      swipeLocation:"",
      buCompanyCode:"",
      tmlRegion:"",
      domainChecked:false,
      emailCheck:false,
      internetCheck:false,
      samAccountName:"",
      isEnable:true,
      validManagerID:false,
      validateDomainID:false
    });
  }

  onChangeHandler = (event) => {
    let name = event.target.name; 
    let value = event.target.value;
    if(name === "manager"){
        this.setState({
          sameManagerField :false ,
          CompleteName:"",
          city:"",
          department:"",
          [name]: value  ,
          managerErrorMessage:"",
          isTmlManagerChange:true,
          isManagerErrorShown:false
        })
    }
    else if(name === "personMobile"){
        let pattern = /^[0-9\b]+$/;
        if(pattern.test(value) || value == ''){
            this.setState({
                personMobile:value,
            })
        }
    }
    else {
        this.setState({ 
            [name]: value,
        });
    }
    
  };

  handleDropdownData = (selectedOption, name) => {
    if(name == 'skill'){
      if (selectedOption.length == 0) {
        this.setState({
          skill: []
        })
      } 
      else {
        let skillArray = selectedOption.map((value , index) =>{
          if (selectedOption.length < this.state.skill.length) {
            this.setState({
              skill: selectedOption
            })
          } else {
            this.setState({
              skill : [...this.state.skill,value]
            })
          }
        })
      }
      this.getSkillList()
    }
    else if(name == 'vendor'){
      if(selectedOption.value === 'TCS'){
        this.setState({
          isTowerShown : true,
          [name] : selectedOption,
          towerData:[],
          projectName:""
        },()=>{
          this.getProjectList(selectedOption.value) , 
          this.getTower()
        })
      }
      else {
        this.setState({
          isTowerShown : false,
          projectName:"",
          [name] : selectedOption
        },()=>{this.getProjectList(selectedOption.value)});
      }
    }
    else {
      this.setState({ [name] : selectedOption});
    }
  };

 objectsAreEqual = (obj1, obj2) =>{
    const keys1 = Object.keys(obj1)
    const keys2 = Object.keys(obj2)
  
    if (keys1.length !== keys2.length) {
      return false;
    }
  
    for (let key of keys1) {
      if (obj1[key] !== obj2[key]) {
        return false;
      }
    }

    return true;
  }
  

  handleDate = (value, name) => {
    this.setState({ [name]:value});
  };

  handleCheckBox = (event) => {
    const {value, name, checked } = event.target
    this.setState ({
        [value] : checked
    })
  }

  validateForm = (CompleteName,employeeId,firstName,lastName,personEmail,personMobile,tmlLocation,buCompanyCode,swipeLocation,designation,validityTo,city,department,gender,vendor,projectName,vendorManager,vendorManagerEmail,tmlManagerEmail,skills,yearOfExperience,tmlRegion,edataDepartment) => {
      if ( employeeId == "" || firstName == "" || lastName == "" || personEmail == "" || tmlLocation == "" || buCompanyCode == "" || swipeLocation == "" || designation == "" || validityTo == "" || gender == "" || vendor == null || projectName == "" || vendorManager == "" || vendorManagerEmail == "" || tmlManagerEmail == null || skills == [].length || yearOfExperience == "" || tmlRegion == "" || edataDepartment == "") {
          errorToast(PLEASE_ENTER_REQUIRED_FIELD)
          return false;
      }
      else if(personMobile && personMobile.length !== 10){
        errorToast(PLEASE_ENTER_VALID_MOBILE_NO)
          return false;
      }
      else if (this.state.employeeType?.value === "IT" && (city == null || department == null)){
        errorToast(PLEASE_ENTER_REQUIRED_FIELD);
          return false
      }
      else if (this.state.vendor.value == 'TCS' && this.state.towerName == ""){
          errorToast(PLEASE_ENTER_REQUIRED_FIELD);
          return false
      } 
      else if (this.state.source === "AD" && this.state.employeeType?.value === "IT"){
        errorToast(FOR_IT_EMPLOYEES_MANAGR_SHOULD_BE_TML_MANAGER)
        return false
      }
      else if(this.state.vendorManagerEmail.toLocaleLowerCase() === this.state.manager.toLocaleLowerCase()){
        errorToast(MANAGER_VENDOR_CANNOT_SAME)
        return false
      }
      else if (this.state.manager == "") {
        errorToast(MANAGER_DETAILS_NOT_FOUND)
        return false;
      }
      else if (this.state.isTmlManagerChange && (CompleteName == "" || CompleteName == null) ) {
        errorToast(MANAGER_DETAILS_NOT_FOUND)
        return false;
      }
      return true
  };

  getSkillList = () => {
    let skillList = this.state.skill.map((value , index) => value.value)
    return skillList
  }

  capitalizeEachLetter = (vendorName) => {
    return vendorName
    .split(' ')
    .map(word => word.split('').map((letter, index) => index === 0 ? letter.toUpperCase() : letter).join(''))
    .join(' ');
  }

  getSkillDropdownData = (list) => {
    let skillList = list.map(value =>({
      value : value.skillCode,
      label : value.skillName
    }))
    return skillList
  }

  syncEmployees = (event) => {
    // if (!this.state.buCompanyCode || !this.state.buCompanyCode.value) {
    //     errorToast("Please select BU Company Code");
    //     return;
    // }
    let flag = this.validateForm(this.state.CompleteName,this.state.employeeId,this.state.firstName,this.state.lastName,this.state.personEmail,this.state.personMobile,this.state.tmlLocation,this.state.buCompanyCode,this.state.swipeLocation,this.state.designation,this.state.validityTo,this.state.city,this.state.department,this.state.genderType,this.state.vendor,this.state.projectName,this.state.vendorManager,this.state.vendorManagerEmail,this.state.manager,this.state.skill,this.state.yearOfExperience,this.state.tmlRegion,this.state.edataDepartment);
    if (flag) {
      this.setState({
        isLoading : true,
      })
      let apiFormatedData = {
        employeeId : this.state.employeeId,
        firstName : (this.state.firstName.charAt(0).toUpperCase() + this.state.firstName.toLowerCase().slice(1)).trim(),
        middleName : this.state.middleName?.trim().charAt(0).toUpperCase() + this.state.middleName?.trim().toLowerCase().slice(1),
        lastName : (this.state.lastName.charAt(0).toUpperCase() + this.state.lastName.toLowerCase().slice(1)).trim(),
        mobile : this.state.personMobile,
        email : this.state.personEmail.toLowerCase().trim(),
        designation : this.state.designation.value,
        gender : this.state.genderType.value,
        vendor : this.state.vendor.value,
        project : this.state.projectName.value,
        vendorManagerName: this.capitalizeEachLetter(this.state.vendorManager.trim()),
        vendorManagerEmail: this.state.vendorManagerEmail.toLowerCase().trim(),
        tmlManagerEmployeeId:this.state.tmlManagerPerno,
        tmlManagerCompCode:this.state.tmlManagerCompanyCode,
        city:this.state.city,
        department:this.state.department,
        tmlManagerTitle:this.state.tmlManagerTitle,
        tmlManagerName:this.state.CompleteName,
        companyDesc:this.state.companyDesc,
        costCenter:this.state.costCenter,
        functionText:this.state.functionText,
        locationDesc:this.state.locationDesc,
        ouLevel2ShortText:this.state.ouLevel2ShortText,
        pincode:this.state.pincode,
        subFunction2Text:this.state.subFunction2Text,
        createdBy:this.props.loginData.empId,
        validTill:dayjs(this.state.validityTo).format("YYYY-MM-DD"),
        validFrom:dayjs(this.state.validityFrom).format("YYYY-MM-DD"),
        skills : this.getSkillList(),
        tmlRegion:this.state.tmlRegion.value,
        tmlOffice:this.state.tmlLocation.value,
        swipeLocation:this.state.swipeLocation.value,
        buCompCode:this.state.buCompanyCode.value,
        yearsOfExperience:this.state.yearOfExperience,
        isEmail:this.state.emailCheck,
        isInternet:this.state.internetCheck,
        requestType:'Sync',
        tower:this.state.towerName?.value,
        employeeType:this.state.employeeType?.value,
        source:this.state.source,
        tmlManagerEmail:this.state.manager,
        samAccountName:this.state.samAccountName,
        sAMAccountName_manager:this.state.sAMAccountName_manager,
        manager_last_name:this.state.manager_last_name,
        manager_first_name:this.state.manager_first_name,
        edataDepartment:this.state.edataDepartment?.label,
        edata:{tower:this.state.towerName?.value,sioCode:this.state.sioCode,department:this.state.edataDepartment?.label},
        l2ManagerName: this.state.l2ManagerName,
        l2ManagerEmail: this.state.l2ManagerEmail,
        l2ManagerEmployeeId: this.state.l2ManagerEmployeeId,

      };
      if(!this.state.towerName){
        delete apiFormatedData.edata.tower
      }
      getAPIResponse(config.syncEmployeeDetails , "POST", apiFormatedData)
      .then((response)=>{
        if(response.status === 500){
          this.setState({
            isLoading:false,
          })
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("handleSubmit() in request",response)
          return null
          }
          else {
           return response.json()
          }
      })
      .then((data)=>{
        if(data !== null && data.status === 1){
            this.setState({
              isLoading : false,
              isTowerShown:false
            })
            successToast(data.message)
            this.goNext()
        }
        else {
            this.setState({
              isLoading:false,
            })
            errorToast(data.message)
          }
      })
      .catch((error)=>{
        this.setState({
          isLoading:false,
        })
        console.log("handleSubmit() in request",error)
      })
      //  https://stackoverflow.com/questions/********/basic-authentication-with-fetch
    }
  };

  validateDomainId = () => {
    if(this.state.samAccountName == "" || this.state.samAccountName == null) {
        this.setState({
            isErrorShown:true,
            errorMessageForValidation : PLEASE_ENTER_SAMACCOUNT_NAME
        })
    }
    else {
       this.setState({
          loaderDomainID : true,
          errorMessageForValidation:"",
          isErrorShown:false
       })
        let body = {
            "samAccountName" : this.state.samAccountName
        }
        getAPIResponse(config.validateDomainID , "POST" , body)
        .then((response)=>{
            if(response.status === 500) {
                errorToast(INTERNAL_SERVER_ERROR)
            }
            else {
                return response.json()
            }
        })
        .then((data)=> {
            if(data !== null && data.status === 1) {
                this.setState({
                    firstName:data.data.firstName,
                    middleName:data.data.middleName,
                    lastName:data.data.lastName,
                    personMobile:data.data.mobile,
                    personEmail:data.data.email,
                    genderType:data.data.gender,
                    yearOfExperience:data.data.yearsOfExperience,
                    skill:data.data.skills,
                    designation:data.data.designation,
                    employeeId:data.data.employeeId,
                    manager:data.data.tmlManager,
                    vendorManager:data.data.vendorManagerName,
                    vendorManagerEmail:data.data.vendorManagerEmail,
                    projectName:data.data.project,
                    // towerName:data,
                    validityTo:dayjs(data.data.validTill),
                    validityFrom:dayjs(data.data.validFrom),
                    emailCheck:data.data.isEmail,
                    isEmailDisable:data.data.isEmail,
                    tmlLocation:data.data.tmlOffice,
                    swipeLocation:data.data.swipeLocation,
                    buCompanyCode:data.data.buDetails,
                    tmlRegion:data.data.tmlRegion,
                    validateDomainID:true,
                    loaderDomainID : false,
                    
                })
            }
            else {
                this.setState ({
                    isErrorShown : true,
                    errorMessageForValidation : data.message,
                    loaderDomainID : false
                })
            }
        })
        .catch((error)=> {
          this.setState({
            loaderDomainID : false
          })
          errorToast(SOMETHING_WENT_WRONG)
          console.log("validateDomainId() in SyncEmployees",error)
        })
    }   
  }





  


    managerDetails = () => {
    if (this.state.manager == "" || this.state.manager == null){
      this.setState({
        isManagerErrorShown:true,
        managerErrorMessage:PLEASE_ENTER_MANAGER
      })
    }
    else if(this.state.employeeType == "" || this.state.employeeType == null){
      this.setState({
        employeeTypeErrorMessage:PLEASE_SELECT_EMPLOYEE_TYPE,
        employeeTypeErrorShown:true
      })
    }
    else if (!this.state.validateDomainID){
      this.setState({
        isErrorShown:true,
        errorMessageForValidation : PLEASE_VALIDATE_DOMAIN_ID
      })
    }
    else {
      this.setState({
        isManagerErrorShown:false,
        managerErrorMessage:"",
        loaderManagerID : true
      })
      let body = {
        manager : this.state.manager,
        employeeType : this.state.employeeType?.value
      }
      getAPIResponse(config.validateManagerDetails , "POST" , body)
      .then((response)=>{
        this.setState({
          managerLoder : false
        })
        if(response.status === 500){
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("managerDetails() in Request",response)
          return null
        }
        else {
          return response.json()
        }
      })
      .then((data) => {
        if(data !== null && data.status === 1){
          const source = data.data.source
          const l2 = data.data.l2Manager
          let l2Editable = false;
            if(source === "SAP" || source === "Sharepoint"){
              if (l2?.error){
                errorToast(l2.error);
                l2Editable = true;
                l2.name = "";
                l2.email = "";
              }
              else if (!l2?.name || !l2?.email){
                if (!l2?.name) {
                errorToast("L2 Manager Name not found in response.");
              }
              if (!l2?.email) {
                errorToast("L2 Manager Email not found in response.");
              }
      
              }
            } 
            if (source === "AD") {
              alert("Enter the Manager Name and Email")
              l2Editable = true;
              // l2.fullName = "";
              // l2.email = "";
            }
              else {
              l2Editable = false; 
            }
            this.setState({
              city:data.data.citytown,
              department:data.data.department,
              tmlManagerTitle:data.data.title,
              CompleteName:data.data.fullName,
              tmlManagerPerno:data.data.employeeId,
              tmlManagerCompanyCode:data.data.compCode,
              companyDesc:data.data.companyDesc,
              costCenter:data.data.costCenter,
              functionText:data.data.functionText,
              locationDesc:data.data.locationDesc,
              ouLevel2ShortText:data.data.ouLevel2ShortText,
              pincode:data.data.pincode,
              subFunction2Text:data.data.subFunction2Text,
              isEnable:false,
              isDepartmentDisable:false,
              validManagerID:true,
              source:data.data.source,
              manager_first_name : data.data.manager_first_name,
              manager_last_name:data.data.manager_last_name,
              sAMAccountName_manager:data.data.sAMAccountName_manager,
              loaderManagerID:false,
              l2ManagerName: l2.name,
              l2ManagerEmail: l2.email,
              l2ManagerEmployeeId:l2.employeeID,
              l2FieldsEditable: l2Editable,
              // isTmlManagerChange:false
            },()=>this.handleBuCode())
        }
        else {
          this.setState({
            loaderManagerID:false,
            isManagerErrorShown:true,
            managerErrorMessage:data.message
          })
          console.log("managerDetails() in Request",data)
        }
      })
      .catch((error) => {
        this.setState({
          loaderManagerID:false
        })
        errorToast(SOMETHING_WENT_WRONG)
        console.log("managerDetails() in Request",error)
      })
    }
      
  }


  handleBuCode = ()=>{
    if(this.state.source === 'SharePoint' || this.state.source === 'SAP'){
      const buCompanyCode = this.state.buCompanyCodeList.find(obj=>String(obj.value)===String(this.state.tmlManagerCompanyCode));
      this.setState(prevState=>({...prevState,buCompanyCode,isBuDisabled:true}))
    }
    if(this.state.source === 'AD'){
      const buCompanyCode = this.state.buCompanyCodeList.find(obj=>String(obj.value)===String(this.state.tmlManagerCompanyCode));
      this.setState(prevState=>({...prevState,buCompanyCode,isBuDisabled:true}))
      // this.setState(prevState=>({...prevState,buCompanyCode:"",isBuDisabled:false}))
    }
}



fetchL2ManagerDetails = async () => {
  const email = this.state.l2ManagerEmail;
  if (this.state.l2ManagerEmail == "") {
    errorToast("Please Enter Manager Email")
  }
  else if (this.state.employeeType === "") {
    errorToast("Please Select Employee Type")
    }
  else{
    let body = {
        "manager" : this.state.l2ManagerEmail.trim(),
        "employeeType" : this.state.employeeType?.value  
      }
      getAPIResponse(config.getManagerDetails , "POST", body)
      .then((response)=>{
        console.log("Raw Response:", response);
        this.setState({
          managerLoder : false
        })
        if(response.status === 500){
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("managerDetails() in Request",response)
          return null
        }
        else {
          return response.json()
        }
      })
      .then((data) => {
        if (data.data.source === "AD") {
                errorToast("L2 Manager details not found. Please enter manually.");
                return;
              }
        if(data !== null && data.status === 1){
          this.setState({
          l2ManagerName: data.data.fullName || "",
          l2ManagerEmployeeId: data.data.employeeId || "",
          tmlManagerCompanyCode: data.data.compCode || "",
        },()=>this.handleBuCode())
        }
        else {
          errorToast(data.message); 
          console.log("managerDetails() in Request",data)
        }
      })
      .catch((error) => {
        console.log("managerDetails() in Request",error)
      })
    }
  }

  onChangeEmailHandler = (value , errorValue , error , name) => {
    this.setState({
        [error] : "" , 
        [name] : value
    })
  }

  handleEmployeeType = (value) => {
    if(this.state.employeeTypeErrorShown){
      this.setState({
        employeeTypeErrorMessage:"",
        employeeTypeErrorShown:false,
      })
    }
    this.setState({
      employeeType : value,
      isManagerErrorShown:false,
      managerErrorMessage:""
    })
    if (value.value == 'IT'){
      let department = this.state.departmentData.find(obj => obj.value === 'IT');
      let departmenttemp = this.state.departmentData.filter(obj => obj.type !== 'NONIT');

      this.setState({
        // isDepartmentDisable:true,
        // edataDepartment: department,
        edataDepartment: department,
        departmentTempData: departmenttemp,
      })
    }
    else{
      let department = this.state.departmentData.filter(obj => obj.type !== 'IT');
      let selectedDepartment = this.state.departmentData.find(obj => obj.value === "NONIT");
      this.setState({
        // isDepartmentDisable:true,
        departmentTempData: department,
        edataDepartment: selectedDepartment,
        // edataDepartment: selectedDepartment,
      })
    }
  }

  inputValidation = (value , name) => {
    const regex = /[^A-Za-z0-9\s]+$/;
    const regex_employeeID = /[^A-Za-z0-9]+$/;
    
    if(name === "employeeId"){
      if(!regex_employeeID.test(value)){
        this.setState({
          [name] : value
        })
      }
    }
    else if (!regex.test(value)){
      this.setState({
        [name] : value
      })
    }
  }

  alphabetValidations = (value, name) => {
    const regex = /[^A-Za-z\s]+$/;
    if(!regex.test(value)){
      this.setState({
        [name] : value
      })
    }
  }


  emailValidations = (value , error , name) => {
    const regex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/i;
    // if(value !== '' && !regex.test(value)){
    if(value !== '' && !UpdateEmailRegex.test(value)){
      this.setState({
        [error] : PLEASE_ENTER_VALID_EAMIL_ID,
        [name] : null
      })
    } 
  }

  numberValidation = (value , name , error) => {
    if(this.state.experienceErrorMessage !== "") this.setState({experienceErrorMessage : ""})
    const pattern = /^[0-9\b]+$/;
    if(pattern.test(value)){
      if(parseInt(value) > 60){
        this.setState ({ 
            [name] : null , 
            [error]  : YEARS_OF_EXPERIENCE_ERROR 
        })
      }
      else {
        this.setState({
          [name] : value,
        })
      }
    }
    else if(value == ""){
      this.setState({
        [name] : value
      })
    } 
  }

  onChangeMobileNumber = ({target:{value}}) =>{
    let pattern = /^[0-9\b]+$/;
    if(pattern.test(value) || value == ''){
      this.setState({
        personMobile:value
      })
    }
  }

  domainIdValidation = (value , filedName) => {
    const regex = /[^A-Za-z0-9.]+$/;
    if((value !== "" || value !== null) && !regex.test(value)){
        this.setState({
            [filedName] : value,
            isErrorShown:false,
            errorMessageForValidation : "",
            validateDomainID:false
        })
    }
  }

  clearSearch = () => {
    this.setState({
        samAccountName : "",
        errorMessageForValidation:"",
        isErrorShown:false,
        managerErrorMessage:"",
        isManagerErrorShown:false,
        validateDomainID:false,
        isEnable:true,
        validManagerID:false,
        isEnable:true,
        employeeType:""
    },() => {this.resetForm()})
  }

  goNext = () => {
    Router.push({
      pathname: "/Dashboard",
    });
  };

  maskEmail = (email) => {
    if(!email) return null;
    if (!email || !email.includes("@")) return "****@****.***";
  
    const [name, domain] = email.split("@");
    const maskedName = name[0] + "*".repeat(Math.max(0, name.length - 2)) + name.slice(-1);
    
    return maskedName + "@" + domain;
  };

  maskMobileNumber = (number) => {
    if(!number) return null;
    if (number.length < 4) return "****"; 
    return number.replace(/\d(?=\d{4})/g, "*");
  };

  render() {
    return (
      <>
      <div>
        <div className="row">
          <div className="col-12">
            <Form autoComplete="off">
              <div className="card card-primary card-outline">
                <div className="card-header with-border">
                  <h2 className="card-title">Sync Vendor Employee Details</h2>
                </div>
                <div className="card-body">

                  {/* ------------------------------ Validation part ---------------------------   */}
                    <div className="validate-group">
                      <div className="display-flex">
                            <Col md="3" className="flex-1">
                                <Form.Group controlId="employeeDetails">
                                <Form.Label>Domain ID </Form.Label><sup style={{color:'red'}}>*</sup>
                                    <InputGroup>
                                        <Form.Control 
                                            value={this.state.samAccountName}
                                            type="text"
                                            name="samAccountName"
                                            placeholder="Enter Domain ID"
                                            onChange={(e) => {this.domainIdValidation(e.target.value , "samAccountName")}}
                                            disabled={this.state.validManagerID && this.state.validateDomainID}
                                        />
                                    </InputGroup>
                                </Form.Group>
                                <div>
                                  { this.state.isErrorShown ? <span className="validation-error">{this.state.errorMessageForValidation}</span> : null}
                                </div>
                            </Col>
                            
                            <div className={this.state.isErrorShown  || this.state.isManagerErrorShown || this.state.employeeTypeErrorShown? "error-validate": "validate"} onClick={this.validateDomainId} disabled={true}>
                              <Tooltip title="Validate" color="#FFF" overlayInnerStyle={{color:"#000000"}}>
                                {this.state.validateDomainID ? 
                                 <button className="clear-button" disabled={this.state.validateDomainID}><FaCheck className="clear-icon"/></button> 
                                 : this.state.loaderDomainID ? 
                                  <button className="clear-button">
                                    <div className="loader-small"></div>
                                  </button>
                                 :
                                 <div className="clear-button"><FaSearch className="clear-icon"/></div>
                                }
                              </Tooltip>
                            </div>
                      </div>
                      
                      <Col md="3">
                        <Form.Group controlId="employeeType">
                          <Form.Label> Employee Type  </Form.Label><sup style={{color:'red'}}>*</sup>
                          <Select
                            instanceId={"employeeType"}
                            options={EmployeeType}
                            value={this.state.employeeType}
                            onChange={(e)=>{this.handleEmployeeType(e)}}
                            isSearchable
                            isFocused={false}
                            placeholder="Select Employee Type"
                            className="myclass"
                            noOptionsMessage={({ inputValue }) =>
                              "No results found"
                            }
                            isDisabled={this.state.validManagerID}
                          />
                        </Form.Group>
                        { this.state.employeeTypeErrorShown ? <span className="validation-error">{this.state.employeeTypeErrorMessage}</span> : null }
                    </Col>

                      <div className="display-flex">
                        <Col md="3" className="flex-1">
                            <Form.Group controlId="manager">
                              <Form.Label>Manager's Email ID </Form.Label><sup style={{color:'red'}}>*</sup>
                                  <InputGroup>
                                      <Form.Control 
                                          value={this.state.manager}
                                          type="text"
                                          name="manager"
                                          placeholder="Enter Manager's Email ID"
                                          onChange={this.onChangeHandler}
                                          disabled={this.state.validManagerID && this.state.validateDomainID}
                                      />
                                  </InputGroup>
                              </Form.Group>
                              { this.state.isManagerErrorShown ? <span className="validation-error">{this.state.managerErrorMessage}</span> : null }
                        </Col>  
                        <div className={this.state.isErrorShown || this.state.isManagerErrorShown || this.state.employeeTypeErrorShown? "error-validate": "validate"} onClick={this.managerDetails}>
                          <Tooltip title="Validate" color="#FFF" overlayInnerStyle={{color:"#000000"}}>
                              {
                                this.state.validManagerID ?
                                <button className="clear-button" disabled={this.state.validManagerID}><FaCheck className="clear-icon"/></button> :
                                this.state.loaderManagerID ? 
                                <button className="clear-button" disabled={this.state.loaderManagerID}>
                                    <div className="loader-small"></div>
                                </button> 
                                :
                                <div className="clear-button"><FaSearch className="clear-icon"/></div>
                              }
                          </Tooltip>
                        </div>
                      </div>

                      <div className="clear-validate-group" style={{marginLeft:"1rem"}}>
                       
                            <div className={this.state.isErrorShown  || this.state.isManagerErrorShown || this.state.employeeTypeErrorShown? "error-validate":"validate"} onClick={this.clearSearch}>
                            <Tooltip title="Clear" color="#FFF" overlayInnerStyle={{color:"#000000"}}>
                                <div className="clear-button"><VscClearAll className="clear-icon"/></div>
                            </Tooltip>
                            </div>

                          
                        <div className="validate" onClick={()=>{this.setState(prevState=>({...prevState,showMaskedContent:!prevState.showMaskedContent}))}}>
                        <Tooltip title={this.state.showMaskedContent ? 'Hide':'Show'} color="#FFF" overlayInnerStyle={{color:"#000000"}}>
                                <div className="clear-button">{this.state.showMaskedContent ? <FaEyeSlash className="clear-icon"/>:<FaEye className="clear-icon"/>}</div> 
                              </Tooltip> 
                        </div>
                        
                       
                      </div> 
                         
                    </div> 
                  {/* ------------------------------ Person Details ----------------------------- */}
                  <div className="form-section">
                    <span>Personal Details</span>
                  </div>

                  <Row className="form-grid">
                    <Col md="3">
                      <Form.Group controlId="Firstname">
                        <Form.Label> First Name  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <InputGroup>
                          <Form.Control
                            value={this.state.firstName}
                            type="text"
                            name="firstName"
                            onChange={(e)=>{this.alphabetValidations(e.target.value , "firstName")}}
                            placeholder=" Enter First Name"
                            disabled={this.state.isEnable}
                            maxLength="20"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="middleName">
                        <Form.Label> Middle Name </Form.Label>
                        <InputGroup>
                          <Form.Control
                            value={this.state.middleName}
                            type="text"
                            name="middleName"
                            onChange={(e)=>{this.alphabetValidations(e.target.value , "middleName")}}
                            placeholder=" Enter Middle Name"
                            disabled={this.state.isEnable}
                            maxLength="20"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="lastName">
                        <Form.Label> Last Name  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <InputGroup>
                          <Form.Control
                            value={this.state.lastName}
                            type="text"
                            // required
                            name="lastName"
                            onChange={(e)=>{this.alphabetValidations(e.target.value , "lastName")}}
                            placeholder=" Enter Last Name"
                            disabled={this.state.isEnable}
                            maxLength="20"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>

                    <Col md="3" className="detail-view-flex">
                      <Form.Group controlId="Contact">
                        <Form.Label> Mobile No. </Form.Label>
                          <Form.Control
                            pattern="[1-9]{1}[0-9]{9}"
                            type="text"
                            name="personMobile"
                            readOnly={this.state.isEnable} // Makes the field read-only if isEnable is true
                            onChange={this.onChangeMobileNumber}
                            value={this.state.showMaskedContent ? this.state.personMobile : this.maskMobileNumber(this.state.personMobile)}
                            placeholder="Enter Contact Number"
                            maxLength="10"
                            disabled={this.state.isEnable} // Disables the field if isEnable is true
                          />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="Email">
                        <div className="d-flex align-items-center justify-content-between">
                        <div>
                        <Form.Label> Vendor Partner Email ID </Form.Label><sup style={{color:'red'}}>*</sup>
                        </div>
                        <Tooltip placement="top" title={`Enter the official email ID provided by the vendor for onboarding.`} arrow >
                        <FaInfoCircle/>
                        </Tooltip>
                        </div>
                        <Form.Control
                          type="email"
                          name="personEmail"
                          value={this.state.showMaskedContent ? this.state.personEmail : this.maskEmail(this.state.personEmail)}
                          onChange={(e) => {this.onChangeEmailHandler(e.target.value , this.state.personEmailMessage , "personEmailMessage" , 'personEmail')}}
                          placeholder="Enter Vendor Partner Email"
                          onBlur={()=>{this.emailValidations(this.state.personEmail , "personEmailMessage" , "personEmail")}}
                          disabled={this.state.isEnable}
                        />
                        { this.state.personEmailMessage !== "" && this.state.personEmailMessage ? 
                              <span style={{color:"red" , fontSize:"12px"}}>{this.state.personEmailMessage}</span> : null
                        }
                      </Form.Group>
                    </Col>
                    
                    <Col md="3">
                        <Form.Group controlId="Gender">
                          <Form.Label> Gender </Form.Label><sup style={{color:'red'}}>*</sup>
                          <Select
                            options={Gender}
                            value={this.state.genderType}
                            onChange={(e) => {this.handleDropdownData(e, "genderType")}}
                            isSearchable
                            name="genderType"
                            placeholder="Select Gender"
                            className="myclass"
                            noOptionsMessage={({ inputValue }) =>
                              "No results found"
                            }
                            isDisabled={this.state.isEnable}
                          />
                        </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="yearOfExperience">
                        <Form.Label> Years of Experience </Form.Label><sup style={{color:'red'}}>*</sup>
                        <div style={{position:'relative'}}>
                        <FormControl
                            name="yearOfExperience"
                            type="text"
                            placeholder="Enter Years of Experience"
                            className="input-field-apperence"
                            maxLength="2"
                            value={this.state.yearOfExperience}
                            onChange={(e) => {this.numberValidation(e.target.value, "yearOfExperience" , "experienceErrorMessage")}}
                            disabled={this.state.isEnable}
                          />
                           { this.state.experienceErrorMessage !== "" && this.state.experienceErrorMessage ? 
                              <span style={{color:"red" , fontSize:"12px"}}>{this.state.experienceErrorMessage}</span> : null
                          }
                        </div>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="skills">
                        <Form.Label> Skills </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.skillData}
                          value={this.state.skill}
                          onChange={(e) => {this.handleDropdownData(e, "skill")}}
                          isSearchable
                          name="skill"
                          placeholder="Select Skills"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isMulti
                          isDisabled={this.state.isEnable}
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="designation">
                        <Form.Label> Designation  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"designation"}
                          options={this.state.designationList}
                          value={this.state.designation}
                          onChange={(e) => {this.handleDropdownData(e, "designation");}}
                          isSearchable
                          isFocused={false}
                          placeholder="Select Designation"
                          isOptionDisabled={(option) => option.label == "a"}
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="employeeId">
                        <Form.Label>Employee ID</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                          type="text"
                          name="employeeId"
                          onChange={(e)=>{this.inputValidation(e.target.value , "employeeId")}}
                          placeholder="Enter Employee ID"
                          disabled={this.state.isEnable}
                          value={this.state.employeeId}
                          maxLength="20"
                        />
                      </Form.Group>
                    </Col>
                    <Col md="3">
                      <Form.Group controlId="department">
                        <Form.Label>Department</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.departmentTempData.length > 0 ? this.state.departmentTempData :this.state.departmentData}
                          value={this.state.edataDepartment}
                          onChange={(e) => {this.handleDropdownData(e, "edataDepartment")}}
                          isSearchable
                          name="department"
                          placeholder="Select Department"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // isDisabled={this.state.isDepartmentDisable}
                          // isMulti
                          // components={animatedComponents}
                          // required
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  {/* --------------------------------- TML company Details ------------------------------ */}
                  <div className="form-section">
                    <span>Reporting Manager and Company Details</span>
                  </div>

                  <Row className="form-grid">
                    <Col md="3">
                      <Form.Group controlId="city To">
                        <Form.Label> City  </Form.Label>{this.state.employeeType?.value === 'NONIT' ? null : <sup style={{color:'red'}}>*</sup> }
                        <Form.Control
                        type="text"
                        name="city"
                        value={this.state.city}
                        onChange={(e)=>{this.alphabetValidations(e.target.value , "city")}}
                        placeholder=" Enter City"
                        disabled={this.state.isEnable || this.state.employeeType?.value === "IT"}
                      />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="Department No">
                        <Form.Label> Department  </Form.Label>{this.state.employeeType?.value === 'NONIT' ? null : <sup style={{color:'red'}}>*</sup> }
                        <Form.Control
                        type="text"
                        name="department"
                        value={this.state.department}
                        placeholder=" Enter Department"
                        onChange={(e)=>{this.alphabetValidations(e.target.value , "department")}}
                        disabled={this.state.isEnable || this.state.employeeType?.value === "IT"}
                      />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="tmlRegion">
                        <Form.Label> TML Region  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"tmlRegion"}
                          options={this.state.tmlRegionData}
                          value={this.state.tmlRegion}
                          onChange={(e) => {this.handleDropdownData(e, "tmlRegion");}}
                          isSearchable
                          isFocused={false}
                          name="tmlRegion"
                          placeholder="Select TML Region"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="tmlLocation">
                        <Form.Label> TML Location  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"tmlLocation"}
                          options={this.state.tmlLocationData}
                          value={this.state.tmlLocation}
                          onChange={(e) => {this.handleDropdownData(e, "tmlLocation");}}
                          isSearchable
                          isFocused={false}
                          name="tmlLocation"
                          placeholder="Select TML Location"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="buCompanyCode">
                        <Form.Label> BU Company Code </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"buCompanyCode"}
                          options={this.state.buCompanyCodeList}
                          value={this.state.buCompanyCode}
                          // isDisabled={this.state.isEnable}
                          isDisabled={true}
                          onChange={(e) => {this.handleDropdownData(e, "buCompanyCode");}}
                          isSearchable
                          isFocused={false}
                          name="buCompanyCode"
                          placeholder="Select BU Company Code"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="swipeLocation">
                        <Form.Label> Swipe Location  </Form.Label>
                        <Select
                          instanceId={"swipeLocation"}
                          options={this.state.swipeLocationOptions}
                          value={this.state.swipeLocation}
                          onChange={(e) => {this.handleDropdownData(e, "swipeLocation");}}
                          isSearchable
                          isFocused={false}
                          name="swipeLocation"
                          placeholder="Select Swipe Location"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="SIO">
                        <Form.Label>SIO Code / BC Field</Form.Label>
                        <Form.Control
                        type="text"
                        name="sioCode"
                        value={this.state.sioCode}
                        onChange={(e)=>{this.inputValidation(e.target.value , "sioCode")}}
                        placeholder=" Enter SIO Code / BC Field"
                        disabled={this.state.isEnable}
                      />
                      </Form.Group>
                    </Col>
                    <Col md="3">
  <Form.Group controlId="l2ManagerName">
    <Form.Label> L2 Manager Name </Form.Label><sup style={{color:'red'}}>*</sup>
    <Form.Control
      type="text"
      name="l2ManagerName"
      value={this.state.l2ManagerName}
      onChange={(e) => this.setState({ l2ManagerName: e.target.value })}
      disabled={!this.state.isL2Editable ? true : false}
      placeholder="Enter L2 Manager Name"
    />
  </Form.Group>
</Col>

<Col md="3">
  <Form.Group controlId="l2ManagerEmail">
    <Form.Label> L2 Manager Email </Form.Label><sup style={{color:'red'}}>*</sup>
    <Form.Control
      type="email"
      name="l2ManagerEmail"
      value={this.state.l2ManagerEmail}
      onChange={(e) => this.setState({ l2ManagerEmail: e.target.value })}
      disabled={!this.state.isL2Editable ? true : false}
      placeholder="Enter L2 Manager Email"
    />
  </Form.Group>
</Col>


                  </Row>

                  {/* ------------------------------------ Vendor Details ---------------------------------------- */}
                  <div className="form-section">
                    <span>Vendor Details</span>
                  </div>

                  <Row className="form-grid">

                  <Col md="3">
                        <Form.Group controlId="Vendor">
                          <Form.Label> Vendor / Company Name </Form.Label><sup style={{color:'red'}}>*</sup>
                          <Select
                            instanceId={"Vendor"}
                            options={this.state.vendorOption}
                            value={this.state.vendor}
                            onChange={(e) => {this.handleDropdownData(e, "vendor")}}
                            isSearchable
                            isFocused={false}
                            name="vendor"
                            placeholder="Select Vendor Company"
                            className="myclass"
                            noOptionsMessage={({ inputValue }) =>
                              "No results found"
                            }
                            isDisabled={this.state.isEnable}
                            // required
                          />
                        </Form.Group>
                  </Col>


                  <Col md="3">
                      <Form.Group controlId="projectName">
                        <Form.Label>Project Name</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.project}
                          value={this.state.projectName}
                          onChange={(e) => {this.handleDropdownData(e, "projectName")}}
                          isSearchable
                          name="projectName"
                          placeholder="Select Project Name"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    {
                      this.state.isxhown ? 
                      <Col md="3">
                      <Form.Group controlId="towerName">
                        <Form.Label>Tower Name</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.towerData}
                          value={this.state.towerName}
                          onChange={(e) => {this.handleDropdownData(e, "towerName")}}
                          isSearchable
                          name="towerName"
                          placeholder="Select Tower Name"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col> : null
                    }
                    
                    <Col md="3">
                      <Form.Group controlId="vendorManager">
                        <Form.Label>Vendor Manager&apos;s Name</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                          type="text"
                          name="vendorManager"
                          onChange={(e)=>{this.alphabetValidations(e.target.value , "vendorManager")}}
                          placeholder="Enter Vendor Manager Name"
                          disabled={this.state.isEnable}
                          value={this.state.vendorManager}
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="vendorManagerEmail">
                        <Form.Label>Vendor Manager&apos;s Email</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                          type="email"
                          name="vendorManagerEmail"
                          value={this.state.showMaskedContent ? this.state.vendorManagerEmail : this.maskEmail(this.state.vendorManagerEmail)}
                          onChange={(e) => {this.onChangeEmailHandler(e.target.value , this.state.vendorEmailMessage , "vendorEmailMessage" , 'vendorManagerEmail')}}
                          onBlur={()=>{this.emailValidations(this.state.vendorManagerEmail , "vendorEmailMessage" , "vendorManagerEmail")}}
                          placeholder="Enter Vendor Manager Email"
                          disabled={this.state.isEnable}
                          // required
                        />
                          { this.state.vendorEmailMessage !== "" && this.state.vendorEmailMessage ? 
                            <span style={{color:"red" , fontSize:"12px"}}>{this.state.vendorEmailMessage}</span> : null
                          }
                      </Form.Group>
                    </Col>

                  </Row> 

                  {/* ----------------------------------- Account Type --------------------------------- */}
                  <div className="form-section">
                    <span>Account Type</span>
                  </div>

                  <Row className="form-grid">   
                  <Col md="3">
                      <Form.Group controlId="validityFrom">
                        <Form.Label> Valid From  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <DatePicker
                          value={this.state.validityFrom}
                          onChange={(e) => {this.handleDate(e, "validityFrom")}}
                          name="validityFrom"
                          dateFormat="yyyy-MM-dd"
                          placeholderText="Select Valid From"
                          className="form-control display-block"
                          disabled={true}
                          minDate={this.state.validityTo ? this.state.validityTo.subtract(ExpiryMonthRange , 'month') : null}
                          maxDate={this.state.validityTo}
                          style={{fontFamily : 'sans-serif'}}
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="validityTo">
                        <Form.Label> Valid till </Form.Label><sup style={{color:'red'}}>*</sup>
                        <DatePicker
                          value={this.state.validityTo}
                          onChange={(e) => {this.handleDate(e, "validityTo")}}
                          name="validityTo"
                          dateFormat="yyyy-MM-dd"
                          placeholderText="Select Valid To" 
                          className="form-control"
                          showMonthDropdown
                          disabled={true}
                          style={{fontFamily : 'sans-serif'}}
                        />
                      </Form.Group>
                    </Col>

                    <Col md="6">
                      <Form.Group controlId="accountType">
                        <Form.Label>Account Type</Form.Label>
                        {["checkbox"].map((type) => (
                          <div key={`inline-${type}`} className="">
                            <Form.Check
                              type={type}
                              label="Domain ID"
                              id="Domain"
                              name="accountType"
                              defaultChecked={this.state.domainChecked}
                              inline
                              value="Domain"
                              onChange={this.handleCheckBox}
                              disabled
                            />
                            <Form.Check
                              inline
                              type={type}
                              label="Email Account"
                              name="emailType"
                              id="emailAccess"
                              value="emailCheck"
                              onChange={this.handleCheckBox}
                              checked={this.state.emailCheck}
                              disabled={this.state.isEmailDisable}
                            />
                            <Form.Check
                              inline  
                              type={type}
                              label="Internet"
                              id="Internet"
                              name="InternetType"
                              value="internetCheck"
                              onChange={this.handleCheckBox}
                              checked={this.state.internetCheck}
                              disabled={this.state.isEnable}
                            />
                          </div>
                        ))}
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row className="form-grid">
                    <Col md="12">
                      <Form.Group className="text-center" controlId="Submitbtn">
                        <Button disabled={this.state.isEnable} className="primary-button" onClick={this.syncEmployees}>Save</Button>
                        <Button disabled={this.state.isEnable} className="reset-button" onClick={this.resetForm}>Reset</Button>
                      </Form.Group>
                    </Col>
                  </Row>
                </div>
              </div>
            </Form>
            {/* </CardBox> */}
          </div>
        </div>
      </div>

      {
        this.state.isLoading ?
        <div className="loader">
          <div></div>
        </div> : null
      }
      </>
    );
  }
}
const mapStateToProps = (state) => ({
  counter: state.counter.value,
  baseUrl: state.counter.baseUrl,
  maxDay: state.counter.maxDay,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
  vendor : state.loginInfo.login.vendorName,
  isTmlEmployee : state.loginInfo.login.isTmlEmployee,
  requestType : state.counter.requestType
});

const mapDispatchToProps = {
  incrementCounter: incrementCounter,
  decrementCounter: decrementCounter,
  step1: step1,
  login:login
};
export default withRouter(connect(mapStateToProps, mapDispatchToProps)(CreateUpdateRequest));
