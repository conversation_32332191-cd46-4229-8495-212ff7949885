import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { EXTENSION, INTERNAL_SERVER_ERROR, PAGINATIONS, SOMETHING_WENT_WRONG } from '../../constants/message';
import { admin_dashboard, admin_extension_request, ApprovedRequestList, FRONTEND_URL, sizePerPage } from '../../constants/constants';
import { getAdminRequestDetails, triggerUpdateDomainID } from '../../constants/config';
import { getAPIResponse } from '../../constants/Utils';
import moment from 'moment';
import ToolkitProvider, { Search } from 'react-bootstrap-table2-toolkit';
import BootstrapTable from 'react-bootstrap-table-next';
import paginationFactory from 'react-bootstrap-table2-paginator';
import { Spinner } from 'react-bootstrap';
import { setId } from "../../redux/actions/counterActions";
import Router from "next/router";
import { Button } from 'antd';
import { errorToast, successToast } from '../../toast';

const { SearchBar } = Search;

class ExtensionRequest extends Component {
    constructor(props) {
        super(props);
        this.state = {
            headers: [
                { dataField: "srno", text: "Sr No.", sort: false },
                { dataField: "id", text: "Request ID", sort: false },
                { dataField: "fullName", text: "Person Name", sort: false },
                { dataField: "tmlManager", text: "Reporting Manager", sort: false },
                { dataField: "createdBy", text: "Created By", sort: false },
                { dataField: "createdAt", text: "Created At", sort: false },
                { dataField: "status", text: "Request Status", sort: false },
                { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: false },
            ],
            totalSize: 0,
            currentPage: 1,
            currentPageData: [],
            sizePerPage: sizePerPage,
            loading: false,
            searchText: '',
            isRetryDisableV2: {},
            loadingButtons: {},
        };
        this.onFollowChanged.bind(this);
    }

    componentDidMount() {
        if (['isNonTmlSPOC', 'isTmlManager', 'isSpoc'].includes(localStorage.getItem("role"))) {
            window.location.replace(FRONTEND_URL + '/login');
        }
        this.getData(this.state.currentPage);
    }


    getData = (page) => {
        this.setState({ loading: true });
        const queryParams = new URLSearchParams({
            'filter': EXTENSION,
            'requesttype': 'Extension',
            "action": admin_extension_request,
            "module": 'Extension' + ' ' + admin_dashboard,
            'page': page,
            ...(this.state.searchText ? { 'searchtext': this.state.searchText } : {})
        });
        const api_url = `${getAdminRequestDetails}?${queryParams.toString()}`;

        getAPIResponse(api_url, "GET", {})
            .then((response) => {
                if (response.status === 500) {
                    console.error("API Error", response);
                    return null;
                }
                return response.json();
            })
            .then((response) => {
                if (response !== null && response.status === 1) {
                    this.setState({
                        totalSize: Math.ceil(response.data.count / this.state.sizePerPage),
                        currentPage: page,
                        currentPageData: response.data.results.map((value, index) => ({
                            ...value,
                            srno: (page - 1) * this.state.sizePerPage + index + 1,
                            createdAt: moment(value.createdAt).format('DD-MM-YYYY HH:mm'),
                        })),
                        loading: false,
                    });
                }
            })
            .catch((error) => {
                console.error("API Error", error);
                this.setState({ loading: false });
            });
    };


    handleTableChange = (type, { page, searchText }) => {
        if (type === PAGINATIONS) {
            this.getData(page)
        } else {
            this.setState({ searchText: searchText ?? '' }, () => {
                this.getData(page)
            })
        }
    };


    onFollowChanged(row) {
        this.props.setId({ requestId: row.id });
        Router.push({
            pathname: "/RequestManager",
        });
    }

    linkFollow = (cell, row, rowIndex, formatExtraData) => {
        return (
            <div>
                <a style={{ marginRight: "2px", cursor: "pointer", padding: '8px 8px' }} title="view details" className="btn-info btn-xs" onClick={() => this.onFollowChanged(row)}><i className="far fa-eye"></i></a>
                {ApprovedRequestList.includes(row.status) ?
                    <Button style={{ marginLeft: "5px", cursor: "pointer", padding: '4px 10px' }} disabled={formatExtraData.disabledButtons[row.id]?.disabled} onClick={() => this.callRetry(row)}>
                        {formatExtraData.disabledButtons[row.id]?.loading ? (
                            <i className="fas fa-spinner fa-spin"></i>
                        ) : (
                            'Retry'
                        )}
                    </Button>
                    : null}
            </div>
        );
    };

    handleRetryDisableClick = (id, value, loading = false) => {
        this.setState(prevState => ({
            isRetryDisableV2: {
                ...prevState.isRetryDisableV2,
                [id]: { disabled: value, loading: loading }
            }
        }));
    };


    callRetry = (row) => {
        if (localStorage.getItem("role") == 'isSuperUser') {
            this.handleRetryDisableClick(row.id, true, true)
            const requestBody = {
                "action": admin_extension_request,
                "module": 'Extension' + ' ' + admin_dashboard,
                "id": row.id
            }
            getAPIResponse(triggerUpdateDomainID, "POST", requestBody)
                .then((response) => {
                    if (response.status === 500) {
                        errorToast(INTERNAL_SERVER_ERROR);
                        return null
                    }
                    else {
                        return response.json();
                    }
                })
                .then((response) => {
                    if (response && response.status === 1) {
                        successToast(response.message);
                        this.getData(this.state.currentPage);
                    }
                    else {
                        errorToast(response.message)
                    }
                })
                .catch((error) => {
                    console.log("callRetry() in approvedRequest", error)
                    errorToast(SOMETHING_WENT_WRONG)
                })
                .finally(() => {
                    this.handleRetryDisableClick(row.id, false ,false)
                })
        }
    }

    render() {
        const tableColumns = this.state.headers.map(col =>
            col.dataField === 'Action'
                ? { ...col, formatExtraData: { disabledButtons: this.state.isRetryDisableV2 } }
                : col
        );
        return (
            <Fragment>
                {this.state.loading ? (
                    <div className="text-center">
                        <Spinner animation="border" role="status">
                            <span className="sr-only">Loading...</span>
                        </Spinner>
                    </div>
                ) : (
                    <ToolkitProvider
                        keyField="id"
                        data={this.state.currentPageData}
                        columns={tableColumns}
                        search
                    >
                        {(props) => (
                            <>
                                <div className='text-right'>
                                    <SearchBar
                                        {...props.searchProps}
                                        className="custome-search-field"
                                        placeholder="Search"
                                        searchText={this.state.searchText}
                                    />
                                </div>
                                <BootstrapTable
                                    pagination={this.state.totalSize === 1 ? null : paginationFactory({
                                        page: this.state.currentPage,
                                        sizePerPage: this.state.sizePerPage,
                                        totalSize: this.state.totalSize,
                                        hideSizePerPage: false,
                                    })}
                                    remote
                                    striped
                                    wrapperClasses="table-responsive"
                                    {...props.baseProps}
                                    onTableChange={this.handleTableChange}
                                />
                            </>
                        )}
                    </ToolkitProvider>
                )}
            </Fragment>
        );
    }
}

const mapStateToProps = (state) => ({
    requestId: state.counter.requestId,
    baseUrl: state.counter.baseUrl,
    isLogin: state.loginInfo.isLogin,
    loginData: state.loginInfo.login,
});

const mapDispatchToProps = {
    setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(ExtensionRequest);
