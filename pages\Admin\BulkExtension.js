import { Component } from 'react';
import { connect } from 'react-redux';  
import BulkExtensionTab from '../../componets/onBoard/BulkExtensionTab';
import * as constants from '../../constants/constants'

class BulkExtension extends Component {
    constructor(props) {
        super(props);
        this.state = {
            activeKey: "Indents"
        }
    }
    componentDidMount(){    
        let role = localStorage.getItem("role")
        if(!(role === 'isSuperUser')){
            window.location.replace(constants.FRONTEND_URL + '/login');
        }
    }
    render() {
        const isLoggedIn = this.props.isLogin;
        if (!isLoggedIn) {
            window.location.replace(constants.FRONTEND_URL + '/login');
            return null 
        } 
        return (
            <BulkExtensionTab />
        )
    }
}

const mapStateToProps = state => ({
    isLogin: state.loginInfo.isLogin,
});

export default connect(mapStateToProps, null)(BulkExtension);