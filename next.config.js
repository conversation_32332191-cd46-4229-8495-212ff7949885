// module.exports = {
//     async rewrites() {
//         return [
//           {
//             source: '/api/:path*',
//             destination: 'https://api.example.com/:path*',
//           }
//         ]
//       },
//   };

module.exports = {
    devIndicators: {
        buildActivity: false
    },
    // reference: 
    // https://github.com/ant-design/pro-components/issues/4852#issuecomment-**********
    transpilePackages: [
        "antd",
        "@ant-design/plots",
        "@ant-design/icons",
        "@ant-design/icons-svg",
        "@ant-design/pro-components",
        "@ant-design/pro-layout",
        "@ant-design/pro-list",
        "@ant-design/pro-descriptions",
        "@ant-design/pro-form",
        "@ant-design/pro-skeleton",
        "@ant-design/pro-field",
        "@ant-design/pro-utils",
        "@ant-design/pro-provider",
        "@ant-design/pro-card",
        "@ant-design/pro-table",
        "rc-pagination",
        "rc-picker",
        "rc-util",
        "rc-tree",
        "rc-tooltip"
    ]
}