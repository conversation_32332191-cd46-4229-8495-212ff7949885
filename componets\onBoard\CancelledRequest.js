import React, { Fragment } from "react";
import { Component } from "react";
import { toast } from "react-toastify";
import Router from "next/router";
import BootstrapTable from "react-bootstrap-table-next";
import paginationFactory from "react-bootstrap-table2-paginator";
import ToolkitProvider, { Search } from "react-bootstrap-table2-toolkit";
const { SearchBar } = Search;
import { Spinner, fieldset, Modal, Col, Card, Container, Row, Button, Form, } from "react-bootstrap";
import { connect } from "react-redux";
import { setId } from "../../redux/actions/counterActions";
import { getAPIResponse } from '../../constants/Utils'
import * as config from '../../constants/config'
import moment from "moment";
import { CANCELLEDREQUEST, PERMISSION_DENIED_FOR_DELETE_OPERATION } from "../../constants/message";
import { admin_cancel_request, admin_dashboard, sizePerPage } from "../../constants/constants";
import { errorToast } from "../../toast"

class CancelledRequest extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      response: "",
      updateObj: "",
      headers: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "id", text: "Request ID", sort: true },
        { dataField: "fullName", text: "Person Name", sort: true },
        { dataField: "tmlManager", text: "Reporting Manager", sort: true },
        { dataField: "createdBy", text: "Created By", sort: true },
        { dataField: "createdAt", text: "Created At", sort: true },
        { dataField: "final_status", text: "Request Status", sort: true },
        {dataField: "Action",text: "Action",formatter: this.linkFollow,sort: true}
      ],
      dataArray: [],
      isFollow: true,
      isOpen: false,
      isSubmit: "No",
      CompleteName: "",
      ContactNo: "",
      EmailID: "",
      CompanyCodeDesc: "",
      spinner: false,
      totalcomplete: "",
      currentPage: 0,
      currentPageData: [],
      sizePerPage: sizePerPage, 
      totalSize: 0,
      searchText: '',
    };

    this.onFollowChanged.bind(this);
    this.onDeleteRow.bind(this);
    this.mySubmitHandler.bind(this);
    this.onChangeHandler = this.onChangeHandler.bind(this);
  }
  
  componentDidMount() {
    this.getCancelledRequest()
  }

  componentDidUpdate(prevProps){
    if(prevProps.requestType !== this.props.requestType){
      this.getCancelledRequest()
    }
  }

  getCancelledRequest = () => {
    let queryParams = new URLSearchParams({
      'filter' : CANCELLEDREQUEST,
      'requesttype' : this.props.requestType
    })
    let api_url = `${config.getCancelledRequest}?${queryParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      queryParams = new URLSearchParams({
        'filter' : CANCELLEDREQUEST,
        'requesttype' : this.props.requestType,
        "action": admin_cancel_request,
        "module": this.props.requestType +' '+ admin_dashboard
      })
      api_url = `${config.getAdminRequestDetails}?${queryParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response)=>{
      if(response.status === 500){
        console.log("getAssignedRequest() in allrequest",response)
        return null
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      if(data !== null && data.status === 1){
        this.setState({
          totalSize: data.data.count,
          currentPage: 1,
          currentPageData: data.data.results.map((value, index) => ({...value, srno: index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')}))
        })
      }
    })
    .catch((error)=>{
      console.log("handleSubmit() in request",error)
    })
  }

  getPaginatedCancelledRequests(page, searchText) {
    let searchParams = new URLSearchParams({
      "filter": CANCELLEDREQUEST,
      "page": page,
      "searchtext": searchText,
      'requesttype' : this.props.requestType
    }) 
    let api_url = `${config.getCancelledRequest}?${searchParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      searchParams = new URLSearchParams({
        "filter": CANCELLEDREQUEST,
        "page": page,
        "searchtext": searchText,
        'requesttype' : this.props.requestType,
        "action": admin_cancel_request,
        "module": this.props.requestType +' '+ admin_dashboard
      })
      api_url = `${config.getAdminRequestDetails}?${searchParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response) => {
      if (response.status === 500){
        console.log("getPaginatedCancelledRequests (CancelledRequest.js)", response)
        return null
      } else {
        return response.json()
      }  
    }).then((response) => {
      if (response !== null && response.data === null && this.state.searchText.length > 0) {
        this.setState({
          totalSize: 0,
          currentPage: 0,
          currentPageData: []
        })
      }
      if (response !== null && response.status === 1) {
        this.setState({
          totalSize: response.data.count,
          currentPage: page,
          currentPageData: response.data.results.map((value, index) => ({...value, srno: (page - 1) * this.state.sizePerPage + index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')})),
        })
      }
    }).catch((error) => {
      console.log("getPaginatedCancelledRequests (CancelledRequest.js)", error)
    })
  }

  onChangeHandler = (event) => {
    event.preventDefault();
    let name = event.target.name;
    let value = event.target.value;
    this.setState({ [name]: value });
  };

  mySubmitHandler = (event) => {
    event.preventDefault();
    this.setState({ isSubmit: "Yes" }, () => this.closeModal());
  };

  openModal = () => this.setState({ isOpen: true, isSubmit: "No" });

  closeModal = () => {
    if (this.state.isSubmit == "No") {
      this.setState({ isOpen: false });
      toast.warn("Operation Canceled...!", {
        position: toast.POSITION.TOP_CENTER,
      });
    } 
    else {
      this.setState({ isOpen: false });
      toast.info("Data Updated...!", { position: toast.POSITION.TOP_CENTER });
    }
  };

  onFollowChanged(row) {
    this.props.setId({ requestId: row.id });
    Router.push({
      pathname: "/RequestManager",
    });
  }

  onDeleteRow(row) {
    errorToast(PERMISSION_DENIED_FOR_DELETE_OPERATION)
  }

  linkFollow = (cell, row, rowIndex, formatExtraData) => {
    return (
      <div>
        <a style={{ marginRight: "2px", cursor: "pointer",padding:'8px 8px' }} title="view details" className="btn-info btn-xs" onClick={() => {this.onFollowChanged(row)}}><i className="far fa-eye"></i></a>
      </div>
    );
  };

  render() {
    return (
      <Fragment>
          <ToolkitProvider
            keyField="id"
            data={this.state.currentPageData}
            columns={this.state.headers}
            search
          >
            {(props) => (
              <div>
                <div className="text-right">
                  <SearchBar
                    {...props.searchProps}
                    className="custome-search-field"
                    placeholder="Search"
                  />
                </div>

                <BootstrapTable
                  pagination={paginationFactory({page: this.state.currentPage, sizePerPage: this.state.sizePerPage, totalSize: this.state.totalSize, hideSizePerPage: true})}
                  wrapperClasses="table-responsive"
                  striped
                  {...props.baseProps}
                  remote
                  onTableChange={(type, { page, searchText }) => {
                    if (type === 'search') {
                      this.setState({...this.state, searchText: searchText})
                      this.getPaginatedCancelledRequests(1, searchText)
                    }
                    if (type === 'pagination') {
                      this.getPaginatedCancelledRequests(page, this.state.searchText)
                    }       
                  }}
                />
              </div>
            )}
          </ToolkitProvider>
      </Fragment>
    );
  }
}
const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  requestType : state.counter.requestType
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(CancelledRequest);
