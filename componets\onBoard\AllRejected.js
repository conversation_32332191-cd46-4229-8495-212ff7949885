import React, { Fragment } from "react";
import { Component } from "react";
import { toast } from "react-toastify";
import Router from "next/router";
import BootstrapTable from "react-bootstrap-table-next";
import paginationFactory from "react-bootstrap-table2-paginator";
import ToolkitProvider, { Search } from "react-bootstrap-table2-toolkit";
const { SearchBar } = Search;
import { connect } from "react-redux";
import { setId, rejectedList } from "../../redux/actions/counterActions";
import { getAPIResponse } from "../../constants/Utils";
import * as config from '../../constants/config';
import moment from "moment";
import {RequestType, admin_approval_dashboard, admin_rejected_request, sizePerPage} from '../../constants/constants';
import { RejectedRequest , INTERNAL_SERVER_ERROR, REJECTEDREQUEST} from "../../constants/message";
import Select from "react-select";

class AllRejected extends Component {
  static getInitialProps({ store }) {}

  constructor(props) {
    super(props);
    this.state = {
      response: "",
      updateObj: "",
      headers: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "id", text: "Request ID", sort: true },
        { dataField: "fullName", text: "Person Name", sort: true },
        { dataField: "tmlManager", text: "Reporting Manager", sort: true },
        { dataField: "createdBy", text: "Created By", sort: true },
        { dataField: "createdAt", text: "Created At", sort: true },
        { dataField: "final_status", text: "Request Status", sort: true },
        { dataField: "requestType", text: "Request Type", sort: true },
        {dataField: "Action",text: "Action",formatter: this.linkFollow,sort: true}
      ],
      rejectArray: [],
      isFollow: true,
      isOpen: false,
      isSubmit: "No",
      CompleteName: "",
      ContactNo: "",
      EmailID: "",
      CompanyCodeDesc: "",
      spinner: false,
      totalcomplete: "",
      currentPage: 0,
      currentPageData: [],
      sizePerPage: sizePerPage, 
      totalSize: 0,
      searchText: '',
      selectOptions : RequestType,
      requestType:"All",
      selectedValue:{ label: 'All', value: 'All' },
    };
    this.onFollowChanged.bind(this);
    this.mySubmitHandler.bind(this);
    this.onChangeHandler = this.onChangeHandler.bind(this);
  }

  
  componentDidMount() {
    this.getManagerRejectedList(this.state.requestType)
  }

  getManagerRejectedList = (requestType) =>{
    let queryParams = new URLSearchParams({
      "filter":REJECTEDREQUEST,
      "requesttype":requestType
    })
    let api_url = `${config.managerAllrequest}?${queryParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      let queryParams = new URLSearchParams({
        "filter":REJECTEDREQUEST,
        "requesttype":requestType,
        "action": admin_rejected_request,
        "module": requestType +' '+ admin_approval_dashboard
      })
      api_url = `${config.adminApprovalrequest}?${queryParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response)=>{
      if(response.status === 500){
        toast.error(INTERNAL_SERVER_ERROR,{position:toast.POSITION.TOP_CENTER , autoClose:1500})
        console.log("getManagerRejectedList() in Allrejected",response)
        return null
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      if(data !==  null && data.status === 1){
        this.setState({
          totalSize: data.data.count,
          currentPage: 1,
          currentPageData: data.data.results.map((value, index) => ({...value, srno: index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')}))
        })
      }
    })
    .catch((error)=>{
      console.log("getManagerRejectedList() in Allrejected",error)
    })
  }

  getPaginatedRejectedRequests(page, searchText) {
    let searchParams = new URLSearchParams({
      "filter": REJECTEDREQUEST,
      "page": page,
      "searchtext": searchText,
      "requesttype":this.state.requestType
    }) 
    let api_url = `${config.managerAllrequest}?${searchParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      let searchParams = new URLSearchParams({
        "filter":REJECTEDREQUEST,
        "requesttype":this.state.requestType,
        "action": admin_rejected_request,
        "module": this.state.requestType +' '+ admin_approval_dashboard
      })
      api_url = `${config.adminApprovalrequest}?${searchParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response) => {
      if (response.status === 500){
        console.log("getPaginatedRejectedRequests (TMLApproval.js)", response)
        return null
      } else {
        return response.json()
      }  
    }).then((response) => {
      if (response !== null && response.data === null && this.state.searchText.length > 0) {
        this.setState({
          totalSize: 0,
          currentPage: 0,
          currentPageData: []
        })
      }
      if (response !== null && response.status === 1) {
        this.setState({
          totalSize: response.data.count,
          currentPage: page,
          currentPageData: response.data.results.map((value, index) => ({...value, srno: (page - 1) * this.state.sizePerPage + index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')})),
        })
      }
    }).catch((error) => {
      console.log("getPaginatedRejectedRequests (TMLApproval.js)", error)
    })
  }

  // common function

  onChangeHandler = (event) => {
    event.preventDefault();
    let name = event.target.name;
    let value = event.target.value;
    this.setState({ [name]: value });
  };

  mySubmitHandler = (event) => {
    event.preventDefault();
    this.setState({ isSubmit: "Yes" }, () => this.closeModal());
  };

  openModal = () => this.setState({ isOpen: true, isSubmit: "No" });

  closeModal = () => {
    if (this.state.isSubmit == "No") {
      this.setState({ isOpen: false });
      toast.warn("Operation Canceled...!", {
        position: toast.POSITION.TOP_CENTER,autoClose:1500
      });
    } else {
      this.setState({ isOpen: false });
      toast.info("Data Updated...!", { position: toast.POSITION.TOP_CENTER , autoClose:1500});
    }
  };

  onFollowChanged(row) {
    this.props.setId({ requestId: row.id });
    Router.push({
      pathname: "/UpdateIdPage",
    });
  }

  linkFollow = (cell, row, rowIndex, formatExtraData) => {
    return (
      <div>
        <a style={{ marginRight: "2px", cursor: "pointer",padding:'8px 8px' }} title="view details" className="btn-info btn-xs" onClick={() => { this.onFollowChanged(row); }}> <i className="far fa-eye"></i> </a>
      </div>
    );
  };

  render() {
    return (
      <Fragment>
        <ToolkitProvider
          keyField="id"
          data={this.state.currentPageData}
          columns={this.state.headers}
          search
        >
          {(props) => (
            <div>
              <div>
                <div className="text-right">
                  <div className="table-dropdown ">
                    <Select
                    options={this.state.selectOptions}
                    value={this.state.selectedValue}
                    onChange={(e)=>{this.setState({requestType : e.value, selectedValue:e},()=>{this.getPaginatedRejectedRequests(this.state.currentPage , this.state.searchText)})}}
                    isSearchable
                    name="requestType"
                    placeholder="Select Request Type"
                    className="myclass"
                    noOptionsMessage={({ inputValue }) =>
                      "No results found"
                    }
                    // required
                    />
                  </div>
                  <SearchBar
                    {...props.searchProps}
                    className="custome-search-field"
                    placeholder="Search"
                  />
                </div>
              </div>

              <BootstrapTable
                pagination={paginationFactory({page: this.state.currentPage, sizePerPage: this.state.sizePerPage, totalSize: this.state.totalSize, hideSizePerPage: true})}
                wrapperClasses="table-responsive"
                striped
                noDataIndication={"No data to display"}
                {...props.baseProps}
                remote
                onTableChange={(type, { page, searchText }) => {
                  if (type === 'search') {
                    this.setState({searchText: searchText})
                    this.getPaginatedRejectedRequests(1, searchText)
                  }
                  if (type === 'pagination') {
                    this.getPaginatedRejectedRequests(page, this.state.searchText)
                  }
                }}       
              />
            </div>
          )}
        </ToolkitProvider>
      </Fragment>
    );
  }
}
const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  rejectArray: state.counter.rejectArray,
});

const mapDispatchToProps = {
  setId: setId,
  rejectedList: rejectedList,
};
export default connect(mapStateToProps, mapDispatchToProps)(AllRejected);
