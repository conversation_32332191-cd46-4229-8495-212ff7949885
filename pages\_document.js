import Document, { Html, <PERSON>, <PERSON>, NextScript } from "next/document";


class MyDocument extends Document {

  render() {
    return (
      <Html>
        <Head>
          <meta charSet="utf-8" />
          <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <title>Partner Onboarding</title>
          <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback"/>
          <link rel="stylesheet" href="css/fontawesome-free/css/all.min.css"/>
          <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"></link>
          <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css"/>
          <link rel="stylesheet" href="css/adminlte.min.css"/>
          <link rel="stylesheet" href="css/Custom.css"/>
          <link rel="stylesheet" href="css/Bar2.css"/>
          <link rel="stylesheet" href="css/ReactToastify.css"/>
          <link rel="stylesheet" href="css/Select2.css"/>
          <link rel="stylesheet" href="css/alertBox.css"/>
          <link rel="stylesheet" href="css/nda.css"/>
          <link rel="stylesheet" href="css/notification.css"/>
          <link rel="stylesheet" href="css/ReactDatepicker.css"/>
        </Head>
        <body className="hold-transition sidebar-mini">
        <div className="wrapper">
        <Main />
          <NextScript />
          {/* <script src="js/jquery/jquery.min.js" async></script> */}
          {/* <script src="js/bootstrap/js/bootstrap.bundle.min.js" async></script> */}
          <script src="js/adminlte.js" async></script>
          <script src="js/demo.js" async></script>
       </div>
        </body>
      </Html>
    );
  }
}

export default MyDocument;
