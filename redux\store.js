import {legacy_createStore as createStore, applyMiddleware} from 'redux';
import { persistStore, persistReducer } from 'redux-persist';
import rootReducer from './reducers/rootReducer';
import storage from 'redux-persist/lib/storage';
import hardSet from 'redux-persist/lib/stateReconciler/hardSet'
import { thunk } from 'redux-thunk';

let middleWare = [thunk];
 
const persistConfig = {
    key: "root",
    storage: storage,
    timeout: null,
    stateReconciler: hardSet,
};
 
const persistStoreReducer = persistReducer(persistConfig, rootReducer)
 
export const configStore = createStore(persistStoreReducer,applyMiddleware(...middleWare));
 
export let persistor = persistStore(configStore);