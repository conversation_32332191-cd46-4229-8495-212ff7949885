import { Component } from 'react';
import Sidebar from '../componets/Sidebar';
import Footer from '../componets/Footer';
import Header from '../componets/Header';
import ApprovedRequestManager from '../componets/onBoard/ApprovedRequestManager';
import Router from 'next/router'
import { connect } from 'react-redux';  
import { decrementCounter, incrementCounter, step1, step2, step3, step4, step5 } from '../redux/actions/counterActions';
import { Tabs, Tab, ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form } from 'react-bootstrap';
import { login, logOut } from "../redux/actions/loginActions";
import * as constants from '../constants/constants'


class ApprovedRequest extends Component {
    static getInitialProps({ store }) { }
    constructor(props) {
        super(props);
        this.state = {
            activeKey: "Indents"
        }
        //this.handleSubmit.bind(this); 

    }
    
    componentDidMount(){
        let role = localStorage.getItem("role")
        if(!(role === 'isNonTmlSPOC' || role === 'isNonTmlManager' || role === 'isTmlManager' || role === 'isSuperUser')){
            window.location.replace(constants.FRONTEND_URL + '/login');
        }
    }


    render() {
        const isLoggedIn   = this.props.isLogin;
        const createdBy    = this.props.loginData.userRole
        const isTMLManager = this.props.loginData.isTmlEmployee
        if (!isLoggedIn) {
            // window.location.replace(constants.FRONTEND_URL + '/login');
            return null 
          } 
        else {
            return (
                <ApprovedRequestManager/>
            )
        }
        
    }
}

const mapStateToProps = state => ({
    counter: state.counter.value,
    baseUrl: state.counter.baseUrl, 
    isLogin: state.loginInfo.isLogin,
    loginData: state.loginInfo.login
});

const mapDispatchToProps = { 
    login:login
};
export default connect(mapStateToProps, mapDispatchToProps)(ApprovedRequest);
