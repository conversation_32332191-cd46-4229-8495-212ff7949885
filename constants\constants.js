// for devlopment
// const BASE_URL = 'http://127.0.0.1:8000';
//  for production 
const BASE_URL = 'https://partneronboard-api-dev.tatamotors.com';

const HEADERS = new Headers();
HEADERS.append('Content-Type', 'application/json')

// ---------------------------------------------- BASE URL -------------------------------------------------/
// for devlopment
// export const FRONTEND_URL = "http://localhost:3000
// For production
export const FRONTEND_URL = "https://partneronboard-dashboard-dev.tatamotors.com"

export { BASE_URL, HEADERS }

//   ----------------------------------- <PERSON><PERSON><PERSON> LOG OBJECT --------------------------------
export const ChangeLogFiels     =  {
    "firstName"                     : "First Name",
    "middleName"                    : "Middle Name",
    "lastName"                      : "Last Name",
    "mobile"                        : "Mobile No.",
    "email"                         : "Email ID",
    "gender"                        : "Gender",
    "yearsOfExperience"             : "Years of Experience",
    "isInternet"                    : "Internet Access",
    "isEmail"                       : "Email ID Creations",
    "tmlManager.email"              : "Reporting Manager Email",
    "vendorManagerName"             : "Vendor Manager Name",
    "vendorManagerEmail"            : "Vendor Manager Email",
    "designation.designationName"   : "Designation",
    "tmlRegion.regionName"          : "TML Region Name",
    "tmlOffice.officeName"          : "TML Office Name",
    "project.projectName"           : "Project Name",
    "skills"                        : "Skills",
    "validTill"                     : "Valid Till",
    "validFrom"                     : "Valid From",
    "isActive"                      : "Is Active" ,
    "edata.tower"                   : "Tower Name",
    "employeeType"                  : "Employee Type",
    "edata.sioCode"                 : "SIO Code"
}

//  ------------------------------------------- DROPDOWN DATA ----------------------------------------------

export const RequestType = [
    { label: 'Create', value: 'Create' },
    { label: 'Update', value: 'Update' },
    { label: 'Disable', value: 'Disable' },
    { label: 'All', value: 'All' },
  ]

export const Gender =  [
    { value: "Male", label: "Male" },
    { value: "Female", label: "Female" },
    { value: "Others", label: "Others" },
  ]

export const EmployeeType = [
  {value: "IT", label:"IT"},
  {value: "NONIT" , label:"Non IT"}
  
]

export const sizePerPage                      = 10
export const resendMessageForDomainCreation   = "Domain ID Creation Failed"
export const resendMessageForDomainUpdations  = "Domain ID Updation Failed"
export const changeLogList                    = ['Update','Disable','Extension']
export const ApprovedRequestList              = ["Domain ID Creation Failed","Domain ID Updation Failed"]
export const urlList                          = ['/login','/ValidateLoginOTP','/ValidateOTP','/Employee','/MessagePage']

export const MasterType = [
  { label: 'Vendor', value: 'vendor' },
  { label: 'Designation', value: 'designations' },
  { label: 'TML Region', value: 'tmlRegion' },
  { label: 'TML Office', value: 'tmlOffice' },
  { label: 'Skill', value: 'skill' },
  { label: 'Tower', value: 'tower' },
  { label: 'Project', value: 'project' },
  { label: 'Department', value: 'department' },
]

export const MasterModalType = [
  { label: 'Vendor', value: 'vendor' },
  { label: 'Designation', value: 'designations' },
  { label: 'Skill', value: 'skill' },
  { label: 'Tower', value: 'tower' },
  { label: 'Project', value: 'project' },
  { label: 'TML Region', value: 'tmlRegion' },
  { label: 'TML Office', value: 'tmlOffice' },
  { label: 'Department', value: 'department' },
]

export const admin_action                   = "action"
export const admin_modules                  = "module"
export const admin_master_list              = "Master listing"
export const admin_master                   = "Master"
export const admin_add_master               = "Master addition"
export const admin_update_master            = "Master update"
export const admin_user                     = "Access Management"
export const admin_user_listing             = "Access Management listing"
export const admin_add_user                 = "Access Management addition"
export const admin_update_user              = "Access Management update"
export const admin_validate_user            = "Access Management validation"
export const admin_spoc_report              = "SPOC report"
export const admin_spoc_report_list         = "SPOC report listing"
export const admin_spoc_report_mail         = "SPOC report send mail"
export const admin_dashboard                = "Dashboard"
export const admin_create_dashboard         = "Create Dashboard"
export const admin_dashboard_count          = "Get dashboard count"
export const admin_invite_request           = "Get invite request"
export const admin_assign_request           = "Get assign request"
export const admin_approved_request         = "Get approved request"
export const admin_fullfill_request         = "Get fullfilled request"
export const admin_rejected_request         = "Get rejected request"
export const admin_cancel_request           = "Get cancel request"
export const admin_disable_request          = "Get disable request"
export const admin_send_invite              = "Send invite"
export const admin_cancel_invite            = "Cancel invite"
export const admin_cancel_assign            = "Cancel assign"
export const admin_create_domainid          = "Create domain ID"
export const admin_update_domainid          = "Update domain ID"
export const admin_nda_download             = "Download NDA"
export const admin_approval_dashboard       = "Approval dashboard"
export const admin_approval_dashboard_count = "Get approval dashboard count"
export const admin_pending_request          = "Get pending request"
export const admin_request_screen           = "Request Detail View"
export const admin_request_update_screen    = "Request Detail Update ID View"
export const admin_request_update_nda_screen= "Request Detail Update NDA View"
export const admin_request_details          = "Get request details"
export const admin_accept_nda               = "Accept NDA form"
export const admin_reject_nda               = "Reject NDA form"
export const admin_accept_update_nda        = "Accept update NDA form"
export const admin_reject_update_nda        = "Reject update NDA form"
export const admin_extension_request        = "Admin Extension Request"
export const admin_download_extension_request = "Admin Download Extension Request"
export const admin_bulk_extension           = "Bulk extension"
export const admin_bulk_approved_request    = "Get bulk approved request"



export const emptyDataMessage = () => { return 'No Data to Display';}
export const Nodata = "No Data to Display"
// export const action = 'action'
// export const module = 'module'

export const bulkUpdateCSVcontent = 'First Name, Middle Name(Use \'NA\' to remove the field), Last Name, Gender, Email(Mandatory),Personal Mail, Mobile No(Use \'NA\' to remove the field), Department, Designation, Skills(separate by comma), Years of Experience, Reporting Manager Email, TML Region, TML Location, Project Name, Vendor Manager Name, Vendor Manager Email, SIO code, Employee Type (NONIT or IT), Extend Validity to (yyyy-mm-dd), isActive(0 for Disable)\n'

export const UpdateEmailRegex = /^[^ @]+@[a-z0-9.-]+\.[a-z]{2,}$/i;

export const blockedDomains = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com', 'ymail.com'];
