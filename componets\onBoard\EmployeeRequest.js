import { Component } from "react";
import { ToastContainer } from "react-toastify";
import { connect } from "react-redux";
import { setId } from "../../redux/actions/counterActions";
import Router from "next/router";
import React, { useState } from "react";
import { ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form, } from "react-bootstrap";
import { DateTime } from "luxon";
import { getAPIResponse } from '../../constants/Utils'
import * as config from '../../constants/config'
import { login } from "../../redux/actions/loginActions";
import { ACCEPTREQUEST, DISCLAMIER, EMPLOYEE_REJECT_NOTE, INTERNAL_SERVER_ERROR, LOGIN_WITH_EMAILID_AND_OTP, REJECTREQUEST } from "../../constants/message";
import { successToast, errorToast } from "../../toast"
import { MdCancel } from "react-icons/md";
class EmployeeRequest extends Component {
  static getInitialProps({ store }) { }

  constructor(props) {
    super(props);
    this.state = {
      personName: "",
      vendor: "",
      personMobile: "",
      personEmail: "",
      manager: "",
      acceptance: "",
      checked: false,
      checked3: false,
      checked2: true,
      ndalfag: false,
      emailfag: true,
      checkedValue: "Reject",
      isOpen: false,
      PersNo: "",
      titleCheckBtn: "please click checkbox if accept NDA",
      EmailID: "",
      location: "",
      swipeLocation:"",
      buCompanyCode:"",
      designation:"",
      isLoading:false,
      tmlManagerTitle:"",
      tmlManagerName:"",
      SPOC:"",
      ndaAcceptanceStatus:"",
      processDisable : false,
      ndaResponse :"",
      ID:"",
      otp:"",
      ID:"",
      requestFinalStatus:"",
      layoutShown:true,
      rejectPopUp:false,
      remarkNote:"",
      vendor:"",
      link:"",
      Reject : "Reject",
      Success : "Success",
      employeeID : "",
      projectName : "",
      isGroupNDASigned:false,
      groupNdaPopUp:false
    };
    this.handleSubmit.bind(this);
  }
 
  componentDidMount() {
    this.getRequestDetailsFromId()
  }

  //  get details from id
  getRequestDetailsFromId = () => {
    if (this.props.personEmail !== undefined) {
      this.setState({
        isLoading:true
      })
      let body = {
        email: this.props.personEmail,
        otp: this.props.otp
      }
      getAPIResponse(config.validateOtpUrl, "POST" , body)
      .then((response)=>{
        if(response.status === 500){
          this.setState({
            isLoading:false
          })
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("getDetailsByRequestId() in EmployeeRequest",response)
          return null
        }
        else{
          return response.json()
        }  
      })
      .then((data)=>{
        if(data !== null){
          if(data.status === 1){
            this.setState({
              manager:data.data.ndaEmployeeDetails.tmlManager.email,
              tmlManagerName:data.data.ndaEmployeeDetails.tmlManager.name,
              location:data.data.ndaEmployeeDetails.tmlOffice.officeName,
              swipeLocation:data.data.ndaEmployeeDetails?.swipeLocation?.locationName || 'NA',
              buCompanyCode:data.data.ndaEmployeeDetails?.buDetails?.compName,
              vendor:data.data.ndaEmployeeDetails.vendor.vendorName,
              designation:data.data.ndaEmployeeDetails.designation.designationName,
              isLoading:false,
              tmlManagerTitle:data.data.ndaEmployeeDetails.tmlManager.title,
              SPOC:data.data.createdBy,
              ID:data.data.id,
              requestFinalStatus:data.data.requestFinalState.id,
              employeeID:data.data.ndaEmployeeDetails.employeeId,
              projectName:data.data.ndaEmployeeDetails.project?.projectName,
              isGroupNDASigned:data.data.ndaEmployeeDetails.vendor.isGroupNDASigned
            });
          }
          else {
            this.setState({
              isLoading:false,
            })
            errorToast(data.message)
          } 
        }
      })
      .catch((error)=>{
        console.log("getDetailsByRequestId() in EmployeeRequest",error)
      })
    }
    else {
      this.setState({
        layoutShown : false
      })
    }
  }

  //  submit nda form 
handleSubmit = (event) => {
    event.preventDefault();
    this.setState({
      isLoading:true,
      checked:false,
      groupNdaPopUp : false
    })
    let body = {
      "id": this.state.ID,
      "secret":this.props.link,
      "requestStatus":ACCEPTREQUEST,
    }
    getAPIResponse(config.ndaAccept , "POST" , body)
    .then((response)=>{
      if(response.status === 500){
        this.setState({
          isLoading:false,
          checked:true
        })
        console.log("handleSubmit() in employeeRequest",response)
        errorToast(INTERNAL_SERVER_ERROR)
        return null
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      if(data !== null){
        if(data.status === 1){
          this.setState({
            isLoading:false,
            processDisable:true,
            ndaResponse:"success"
          },()=>{this.props.login({
            spoc:this.state.SPOC,
            tmlmanager:this.state.tmlManagerName,
            message:this.state.Success
          })
          })
          successToast(data.message)
          this.goNext()
        }
        else {
          this.setState({
            isLoading:false,
            checked:true
          })
          errorToast(data.message)
        }
      }
      else{
        this.setState({
          isLoading:false,
          checked:true
        })
      }
    })
    .catch((error)=>{
      this.setState({
        isLoading:false,
        checked:true
      })
      console.log("handleSubmit() in employeeRequest",error)
    })
  };

  rejectRequest = () => {
    if(this.state.remarkNote !== ""){
       this.setState({
        isLoading:true,
        checked:false
      })
      let body = {
        "id": this.state.ID,
        "secret":this.props.link,
        "requestStatus":REJECTREQUEST,
        "remark":`${EMPLOYEE_REJECT_NOTE} - ${this.state.remarkNote.trim()}`
      }
      getAPIResponse(config.ndaAccept , "POST" , body)
      .then((response)=>{
        if(response.status === 500){
          this.setState({
            isLoading:false,
            checked:true
          })
          console.log("handleSubmit() in employeeRequest",response)
          errorToast(INTERNAL_SERVER_ERROR)
          return null
        }
        else{
          return response.json()
        }  
      })
      .then((data)=>{
        if(data !== null){
          if(data.status === 1){
            this.setState({
              isLoading:false,
              processDisable:true,
              ndaResponse:"success"
            },()=>{this.props.login({
              spoc:this.state.SPOC,
              tmlmanager:this.state.tmlManagerName,
              message:this.state.Reject
            })
            })
            successToast(data.message)
            this.goNext()
          }
          else {
            this.setState({
              isLoading:false,
              checked:true
            })
            errorToast(data.message)
          }
        }
        else{
          this.setState({
            checked:true
          })
        }
      })
      .catch((error)=>{
        console.log("handleSubmit() in employeeRequest",error)
      })
    }
    
  }

  // common functions
  goNext = () => {
    Router.push({
      pathname: "/MessagePage",
    });
  };

  goToReject = () => {
    Router.push({
      pathname: "/RejectPage",
    });
  };

  onChangeHandler = (event) => {
    let name = event.target.name;
    let value = event.target.value;
    this.setState({ [name]: value });
  };

  openModal = () => {
    this.setState({ isOpen: true })
  }

  closeModal = () => {
    this.setState({ 
      isOpen: false , 
      checked:false })
  };

  openRejectPopup = () =>{
    this.setState({
      rejectPopUp:true,
    })
  }

  closeRejectpopup = () => {
    this.setState({
      rejectPopUp:false
    })
  }

  rejectReasonInput = (e) => {
    const regex = /[^A-Za-z0-9\s]+$/;
    if(!regex.test(e.target.value)){
      this.setState({
        remarkNote : e.target.value
      },()=>{this.state.remarkNote})
    }
  }

  handleSelectcheck = (e) => {
    this.openModal();
    var titlecheck = "please click checkbox if accept NDA";
    if (e.target.checked == false) {
      titlecheck = "please click checkbox if accept NDA";
      this.setState({
        checked: e.target.checked,
        checkedValue: "Reject",
        acceptance: "",
        checked2: true,
        titleCheckBtn: titlecheck,
      });
    } else {
      titlecheck = "NDA accepted";
      this.setState({
        checked: e.target.checked,
        checkedValue: "Yes Accepted",
        acceptance: "Yes Accepted",
        checked2: false,
        titleCheckBtn: titlecheck,
      });
    }
  };

  closeGroupNdaPopup = () => {
    this.setState({
      groupNdaPopUp : false
    })
  }

  openGroupNdaPopUp = () => {
    this.setState({
      groupNdaPopUp : true
    })
  }

  render() {
    return (
    <>
      <div>
        {/* <ToastContainer /> */}
        { this.state.layoutShown ? 
          <div className="row">
           <div className="col-12">
             <div className="card card-primary card-outline">
               <div className="card-body">
               <div className="form-name">Employee Details : </div>
                 <Row>
                  <Col md="6">
                     <Form.Group controlId="formGridEmailFristName">
                       <Form.Label>Employee ID</Form.Label>
                       <InputGroup>
                         <Form.Control
                           type="text"
                           name="employeeID"
                           readOnly
                           value={this.state.employeeID}
                           onChange={this.onChangeHandler}
                           placeholder="Enter Employee ID"
                         />
                       </InputGroup>
                     </Form.Group>
                   </Col>

                   <Col md="6">
                     <Form.Group controlId="formGridEmailFristName">
                       <Form.Label> Name Of Person</Form.Label>
                       <InputGroup>
                         <Form.Control
                           type="text"
                           name="personName"
                           readOnly
                           value={this.props.personName}
                           onChange={this.onChangeHandler}
                           placeholder=" Enter Person Name"
                         />
                       </InputGroup>
                     </Form.Group>
                   </Col>
                  </Row>

                  <Row>
                   <Col md="6">
                     <Form.Group controlId="Mobile">
                       <Form.Label> Mobile Number</Form.Label>
                       <InputGroup>
                         <Form.Control
                           type="text"
                           name="personMobile"
                           readOnly
                           value={this.props.personMobile}
                           onChange={this.onChangeHandler}
                           placeholder=" Enter Mobile"
                         />
                       </InputGroup>
                     </Form.Group>
                   </Col>
                 
                   <Col md="6">
                     <Form.Group controlId="Email">
                       <Form.Label>Email ID</Form.Label>
                       <Form.Control
                         type="text"
                         name="personEmail"
                         readOnly
                         value={this.props.personEmail}
                         onChange={this.onChangeHandler}
                         placeholder=" Enter Emai Id"
                       />
                     </Form.Group>
                   </Col>
                  </Row>

                  <Row>

                   <Col md="6">
                     <Form.Group controlId="Vendor">
                       <Form.Label>Vendor / Company Name</Form.Label>
                       <Form.Control
                         type="text"
                         name="vendor"
                         readOnly
                         value={this.state.vendor}
                         onChange={this.onChangeHandler}
                         placeholder=" Enter Vendor"
                       />
                     </Form.Group>
                   </Col>

                   <Col md="6">
                     <Form.Group controlId="projectName">
                       <Form.Label>Project Name</Form.Label>
                       <Form.Control
                         type="text"
                         name="projectName"
                         readOnly
                         value={this.state.projectName}
                         onChange={this.onChangeHandler}
                         placeholder="Enter Project Name"
                       />
                     </Form.Group>
                   </Col>

                 </Row>

                 <Row>
                   <Col md="6">
                     <Form.Group controlId="Manager">
                       <Form.Label>TML / TMBSL Approval Manager </Form.Label>
                       <InputGroup className="mb-3">
                         <FormControl
                           type="text"
                           name="manager"
                           aria-label="Recipient's username"
                           aria-describedby="basic-addon2"
                           onChange={this.onChangeHandler}
                           required
                           value={this.state.manager}
                           disabled={true}
                         />
                       </InputGroup>
                     </Form.Group>
                   </Col>
 
                   <Col md="6">
                     <Form.Group controlId="Contact">
                       <Form.Label> TML Location </Form.Label>
                       <FormControl
                           type="text"
                           name="location"
                           aria-label="Recipient's username"
                           aria-describedby="basic-addon2"
                           onChange={this.onChangeHandler}
                           required
                           value={this.state.location}
                           disabled={true}
                         />
                     </Form.Group>
                   </Col>

                   <Col md="6">
                     <Form.Group controlId="Contact">
                       <Form.Label> BU Company Code </Form.Label>
                       <FormControl
                           type="text"
                           name="buCompanyCode"
                           aria-label="BU Company Code"
                           aria-describedby="basic-addon2"
                           onChange={this.onChangeHandler}
                           required
                           value={this.state.buCompanyCode}
                           readOnly
                         />
                     </Form.Group>
                   </Col>

                   <Col md="6">
                     <Form.Group controlId="Contact">
                       <Form.Label> Swipe Location </Form.Label>
                       <FormControl
                           type="text"
                           name="swipeLocation"
                           aria-label="Swipe Location"
                           aria-describedby="basic-addon2"
                           onChange={this.onChangeHandler}
                           required
                           value={this.state.swipeLocation}
                           disabled={true}
                         />
                     </Form.Group>
                   </Col>
                 </Row>
 
                 <Row>
                   <Col md="12">
                     {
                       this.state.requestFinalStatus === 1 ? 
                       <Form.Group className="text-center" controlId="Submitbtn">
                         <Button  disabled={this.state.processDisable} style={{ marginRight: "10px" }} variant="info" onClick={this.state.isGroupNDASigned ? this.openGroupNdaPopUp : this.openModal} size="sm" type="button">{this.state.isGroupNDASigned ? "Accept":"Proceed For NDA"}</Button>
                         {
                          this.state.isGroupNDASigned ? 
                          <Button variant="error" size="sm" onClick={()=>{this.openRejectPopup()}} style={{backgroundColor:'red',color:'white', marginLeft:'5px'}} > Reject </Button> : null
                         }
                       </Form.Group> : null
                     }
                     
                   </Col>
                 </Row>
               </div>
             </div>
           </div>
          </div> : 
          <div className="card card-primary card-outline">
            <div className="card-body">
              <h5 className="text-danger">
                {LOGIN_WITH_EMAILID_AND_OTP}
              </h5>
            </div>
          </div>
        }
       

        {this.state.checked2 && (
          <Row>
            <Col md="12"></Col>
          </Row>
        )}

        {/* ---------------------------------------------- nda form ----------------------------------------------- */}
        <Modal
          border="primary"
          show={this.state.isOpen}
          onHide={this.closeModal}
          backdrop="static"
          keyboard={false}
          size="lg"
        >
          <Form onSubmit={this.handleSubmit}>
            <Modal.Header >
              <Modal.Title className="">NDA Form</Modal.Title>
              <div style={{cursor:"pointer"}}onClick={this.closeModal}>
                      <MdCancel />  
              </div>
            </Modal.Header>
            <Modal.Body style={{ height: "400px", overflowY: "scroll" }}>
              <div className="">
                <Row>
                  <Col lg="12">
                    <div className="text-center">
                      <table className="table1">
                        <tbody>
                          <tr>
                            <td rowSpan="3" className="td1">
                              <p className="p1">
                                <img
                                  width="59"
                                  src="data:image/jpeg;base64,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"
                                  alt="image"
                                ></img>
                                <span className="span1"> &nbsp; </span>&nbsp;
                              </p>
                            </td>
                            <td colSpan="2" className="td2">
                              <p className="p2">
                                <span className="span2">
                                  TATA MOTORS INFORMATION SECURITY &nbsp;
                                  &nbsp;&nbsp;
                                </span>
                                &nbsp;
                              </p>
                            </td>

                            <td rowSpan="3" className="td3">
                              <p className="p3">
                                <span className="span3">
                                  Document Ref&nbsp;
                                </span>
                                <span className="span33">ACC-D002</span>
                              </p>
                            </td>
                          </tr>

                          <tr>
                            <td className="td4">
                              <p className="p4">
                                <span className="span4">Document&nbsp;</span>
                                &nbsp;
                              </p>
                            </td>
                            <td className="td5">
                              <p className="p5">
                                <span className="span5">
                                  Third Party Access - NDA&nbsp;
                                </span>
                                &nbsp;
                              </p>
                            </td>
                          </tr>
                          <tr>
                            <td className="td6">
                              <p className="p6">
                                <span className="span6">&nbsp;</span>&nbsp;
                              </p>
                            </td>
                            <td className="td7">
                              <p className="p7">
                                <span className="span7">&nbsp;</span>&nbsp;
                              </p>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </Col>
                </Row>
                <Row>
                  <Col md="12">
                    <div className="text-center" style={{ marginTop: "5px" }}>
                      
                      <h5>THIRD PARTY NON DISCLOSURE AGREEMENT </h5>
                    </div>
                    <p>
                      {/* THIS AGREEMENT is entered into this <b><u>Saturday day of 3, 2022,</u></b> between */}
                      THIS AGREEMENT is entered into this
                      <b style={{padding:"0px 5px 0px 5px"}}>
                          <u>{DateTime.now().toFormat("MMMM dd, yyyy")}</u>
                      </b>
                      between Tata Motors Ltd., hereinafter &quot;TATA MOTORS&quot;, and
                      Mr./Ms.
                      <b>
                        <u style={{padding:"0px 5px 0px 5px"}}>{this.props.personName}</u>
                      </b>
                      representing
                      <b>
                        <u style={{padding:"0px 5px 0px 5px"}}> {this.state.vendor}</u>
                      </b>
                      &nbsp;(the Vendor / Company), hereinafter &quot;Recipient&quot;. The
                      persons executing this Agreement hereby warrant that they
                      are authorized to do so for and on behalf of the
                      above-named companies.
                    </p>

                    <h5>RECITALS: </h5>

                    <ol type="A">
                      <li className="text-justify">
                        TATA MOTORS is engaged in research, manufacture and
                        marketing of commercial vehicles and passenger cars and
                      </li>
                      <li className="text-justify">
                        TATA MOTORS and the Vendor / Company have entered into
                        an agreement to carry out business transactions ; and
                      </li>
                      <li className="text-justify">
                        In connection with such agreement, TATA MOTORS will
                        disclose to Recipient certain information concerning its
                        business and technologies, a portion of which
                        information is regarded by TATA MOTORS as confidential
                        or proprietary, and the parties desire to provide for a
                        means of determining which information is confidential
                        or proprietary information and for the respective right
                        and duties of the parties with respect thereto;
                      </li>
                      <li style={{ listStyle: "none", marginTop: "5px" }}>
                        NOW THEREFORE, the parties hereto agree as follows:
                      </li>
                    </ol>
                    <ol type="1">
                      <li>
                        
                        Definitions. As used herein: <br></br>
                        <ol type="a">
                          <li className="text-justify">
                            
                            The term &quot;Information&quot; shall mean all information
                            relating to TATA MOTORS business, technologies or
                            prospective business which is furnished to the
                            recipient by TATA MOTORS.
                          </li>
                          <li className="text-justify">
                            The term &quot;Confidential Information&quot; shall mean all
                            information which TATA MOTORS protects against
                            unrestricted disclosure to others and which: if in
                            written or other tangible form, is clearly
                            designated and labeled as &quot;Confidential&quot;; and if
                            disclosed orally, is reduced to a writing
                            designating such information as &quot;Confidential&quot; which
                            is delivered to the Recipient promptly following
                            such oral disclosure. By way of illustration, but
                            not limitation, such Confidential Information may
                            include inventions, concepts, designs, formulas,
                            techniques, processes, software, or market data.
                          </li>
                        </ol>
                      </li>
                      <li className="text-justify">
                        
                        Protection of Confidential Information. The Recipient
                        agrees, with respect to any Confidential Information
                        received by him or her and their represented companies:
                        <br></br>
                        <ol type="a">
                          <li className="text-justify">
                            To use such Confidential Information only for the
                            purpose previously stated (i.e., for the purpose of
                            effecting the proposed transaction between the
                            parties).
                          </li>
                          <li className="text-justify">
                            To use the same methods and degree of care to
                            prevent disclosure of such Confidential Information
                            as it uses to prevent disclosure of its own
                            proprietary and Confidential Information; and to
                            return such Confidential Information received in any
                            tangible form to TATA MOTORS at the request of TATA
                            MOTORS and to retain no copies or reproductions
                            thereof.
                          </li>
                        </ol>
                      </li>
                      <li>
                        <p className="text-justify">
                          Limitations. The Recipient shall not be obligated to
                          treat information as Confidential Information if such
                          information:
                        </p>
                        <br></br>
                        <ol type="a">
                          <li>
                            <p className="text-justify">
                              was rightfully in Recipient&apos;s possession or was
                              rightfully known to Recipient prior to receipt
                              from TATA MOTORS, or is or becomes public
                              knowledge without the fault of Recipient; or
                            </p>
                            <br></br>
                            <p className="text-justify">
                              is or becomes rightfully available to Recipient
                              without confidential restriction from a source not
                              under TATA MOTORS control; or
                            </p>
                            <br></br>
                            <p className="text-justify">
                              is independently developed by Recipient without
                              use of the Confidential Information disclosed
                              hereunder; provided, however, that the burden of
                              proof of such independent development shall be
                              upon Recipient; or
                            </p>
                            <br></br>
                            <p className="text-justify">
                              is disclosed pursuant to court or government
                              action provided, however, that recipient gives
                              TATA MOTORS reasonable prior notice of disclosure
                              pursuant to such court or government action.
                            </p>
                          </li>
                          <li style={{ listStyle: "none" }}>
                            <p className="text-justify">
                              Each party agrees that its obligations hereunder
                              are essential in order to protect the other, and
                              its business, and expressly acknowledges and
                              agrees that monetary damages would be inadequate
                              compensation for any breach of any covenant or
                              agreement set forth herein. Accordingly, each
                              party agrees and acknowledges that any such
                              violation or threatened violation will cause
                              irreparable injury to the other and that, in
                              addition to and without waiving any other remedies
                              (Including damages) that may be available, in law,
                              in equity or otherwise, the aggrieved party shall
                              be entitled to obtain temporary, preliminary
                              and/or permanent injunctive relief against the
                              threatened breach of this Agreement or the
                              continuation of any such breach, without the
                              necessity of proving damage.
                            </p>
                          </li>
                        </ol>
                      </li>
                      <li style={{ listStyle: "none" }}>
                        This agreement is acknowledged and signed by an
                        authorized representative of the respective companies:
                      </li>
                      <ul style={{ listStyle: "none" }}>
                        <li>
                          <Row>
                            <Col md="6">
                              <ul style={{ listStyle: "none", margin: "5px" }}>
                                <b>Recipient</b>
                                <li> Printed Name: <b> <u>{this.props.personName}</u> </b> </li>
                                <li>Title: {this.state.designation}</li>
                                {this.state.checked ? (
                                  <li> Date: <b> <u> {DateTime.now().toFormat("yyyy-MM-dd")}</u> </b> </li>
                                ) : (
                                  <li> Date:<b>--------</b> </li>
                                )}
                              </ul>
                            </Col>
                            <Col md="6">
                              <ul style={{ listStyle: "none", margin: "5px" }}>
                                <b>Tata Motors</b>
                                <li> Printed Name: <b> <u>{this.state.tmlManagerName}</u> </b> </li>
                                <li>Title: {this.state.tmlManagerTitle}</li>
                              </ul>
                            </Col>
                          </Row>
                          <Row>
                            <Col md="12">
                              <Form.Group>
                                <Form.Check
                                  size="md"
                                  type="checkbox"
                                  label={DISCLAMIER}
                                  id="nda"
                                  defaultChecked={this.state.checked}
                                  onChange={this.handleSelectcheck}
                                  value={this.state.checkedValue}
                                  name="acceptance"
                                  inline
                                  title={this.state.titleCheckBtn}
                                  style={{right:'50px' , marginTop:'10px'}}
                                />
                              </Form.Group>
                            </Col>
                          </Row>
                        </li>
                      </ul>
                    </ol>
                  </Col>
                </Row>
              </div>
            </Modal.Body>
            <Modal.Footer style={{ display: "block" }}>
              <Row>
                <Col>
                  <Form.Group className="text-center" controlId="Submitbtn">
                    <Button disabled={this.state.checked && this.state.requestFinalStatus === 1 ? false : true} variant="info" size="sm" type="submit" > Submit </Button>
                    <Button variant="error" size="sm" onClick={()=>{this.openRejectPopup()}} style={{backgroundColor:'red',color:'white', marginLeft:'5px'}} > Reject </Button>
                  </Form.Group>
                </Col>
              </Row>
            </Modal.Footer>
          </Form>
        </Modal>
        {/* ----------------------------------------- reject popup ----------------------------------------------- */}
        <Modal
          show={this.state.rejectPopUp}
          onHide={this.closeRejectpopup}
          aria-labelledby="contained-modal-title-vcenter"
          centered
        >
            <Modal.Header>
              <Modal.Title id="contained-modal-title-vcenter">
                Remark
              </Modal.Title>
              <div style={{cursor:"pointer"}}onClick={this.closeRejectpopup}>
                      <MdCancel />  
              </div>
            </Modal.Header>
            <Modal.Body>
              <Row className="reject-header">
                <Form.Group controlId="RequestID">
                  <Form.Label>Note <sup style={{color:'red', fontWeight:'900'}}>*</sup>: </Form.Label>
                </Form.Group>
                <Form.Group controlId="RequestID">
                  <Form.Label>Request ID :  </Form.Label>
                  <span>{this.state.ID}</span>
                </Form.Group>
              </Row>
              <>
                  <Form.Group controlId="remark">
                    <Form.Control 
                    as='textarea' 
                    rows={4}
                    value={this.state.remarkNote}
                    style={{resize:"none"}}
                    onChange={(e)=>{this.rejectReasonInput(e)}}
                    />
                  </Form.Group>
              </>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="danger" disabled={this.state.remarkNote.trim() ? false : true} onClick={this.rejectRequest}>Reject</Button>
              <Button variant="primary" onClick={this.closeRejectpopup}>Cancel</Button>
            </Modal.Footer>
        </Modal>
        
        {/* --------------------------------------------- group nda pop up ------------------------------------------------ */}

        <Modal
          show={this.state.groupNdaPopUp}
          onHide={this.closeGroupNdaPopup}
          aria-labelledby="contained-modal-title-vcenter"
          centered
        >
          <Modal.Header className="display-justify-end">
            <div style={{cursor:"pointer"}} onClick={this.closeGroupNdaPopup}>
              <MdCancel />  
            </div>
          </Modal.Header>
          <Modal.Body className="group-nda-message">
            Are you sure you want to accept Request ?
          </Modal.Body>
          <Modal.Footer>
            <Button variant="primary" onClick={this.handleSubmit}>Accept</Button>
            <Button variant="danger" onClick={this.closeGroupNdaPopup}>Cancel</Button>
          </Modal.Footer>
        </Modal>

      </div>

      {
        this.state.isLoading ? 
        <div className="loader">
          <div></div>
        </div> : null
      }
    </>
    );
  }
}
const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  baseUrl: state.counter.baseUrl,
  personName: state.loginInfo.login.personName,
  personMobile: state.loginInfo.login.personMobile,
  personEmail: state.loginInfo.login.personEmail,
  otp:state.loginInfo.login.otp,
  vendor: state.loginInfo.login.vendor,
  link: state.loginInfo.login.link,
  requestType:state.counter
});

const mapDispatchToProps = {
  setId: setId,
  login:login
};
export default connect(mapStateToProps, mapDispatchToProps)(EmployeeRequest);
