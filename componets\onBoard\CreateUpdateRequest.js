import { Component } from "react";
import { connect } from "react-redux";
import {decrementCounter,incrementCounter,step1} from "../../redux/actions/counterActions";
import Router from "next/router";
import { DatePicker } from "antd";
import React from "react";
import '../../styles/Home.module.css'
import { FormControl, InputGroup,Col, Row, Button, Form, Spinner} from "react-bootstrap";
import Select from "react-select";
import AsyncSelect from "react-select/async"
import moment from "moment";
import { addDays } from "date-fns";
import { getAPIResponse } from '../../constants/Utils'
import * as config from '../../constants/config'
import '../../styles/Home.module.css'
import { login, logOut } from "../../redux/actions/loginActions";
import makeAnimated from 'react-select/animated';
import { withRouter } from "next/router";
import Image from "next/image";
import { VscClearAll } from "react-icons/vsc";
import { EmployeeType, Gender,UpdateEmailRegex } from "../../constants/constants";
import { ExpiryMonthRange, INTERNAL_SERVER_ERROR, MANAGER_DETAILS_NOT_FOUND, MANAGER_VENDOR_CANNOT_SAME, PLEASE_ENTER_REQUIRED_FIELD, PLEASE_ENTER_VALID_EAMIL_ID, PLEASE_SELECT_VENDOR_COMPANY, SOMETHING_WENT_WRONG, YEARS_OF_EXPERIENCE_ERROR, PLEASE_ENTER_EMPLOYEE_DETAILS,ERROR_MESSAGE_FOR_VALID_FROM, PLEASE_ENTER_MANAGER_EMAIL, PLEASE_SELECT_EMPLOYEE_TYPE,PLEASE_ENTER_VALID_MOBILE_NO } from "../../constants/message";
import dayjs from "dayjs";
import { successToast, errorToast } from "../../toast"
import { Tooltip } from 'antd';
import { FaEye, FaEyeSlash,FaInfoCircle } from "react-icons/fa";

const animatedComponents = makeAnimated();
class CreateUpdateRequest extends Component {
  static getInitialProps({ store }) {}

  constructor(props) {
    super(props);
    this.state = {
      personEmail: '',
      personMobile: '',
      vendor: null,
      middleName: '',
      firstName: "",
      lastName: "",
      locationOption: [],
      manager: "",
      emailfag: true,
      positionPerson: "",
      PersNo: "",
      location: null,
      tmlManagerEmail: "",
      CompleteName: "",
      designation: "",
      validityFrom: "",
      validityTo:"",
      validityToNew: "",
      validityFromNew: "",
      accountType: ["Domain"],
      response: [],
      employeeId: "",
      department: "",
      domainChecked: true,
      city: "",
      gender: Gender,
      genderType: null,
      vendorManager: "",
      vendorManagerEmail: "",
      project: [],
      projectName: "",
      sapID: "",
      designationList : [],
      tmlManagerPerno : "",
      vendorOption: [],
      tmlManagerCompanyCode :"",
      skillData : [], 
      skill : [],
      yearOfExperience : "",
      showDropDown : false,
      target : "",
      tmlLocation:"",
      swipeLocation:"",
      swipeLocationOptions:[],
      isBuDisabled:true,
      buCompanyCode:"",
      buCompanyCodeList:[],
      tmlRegion:"",
      tmlLocationData:[],
      tmlRegionData :[],
      isLoading:false,
      sameManagerField:"",
      tmlManagerTitle:"",
      disable:false,
      tmlManagerData:[],
      email:false,
      internet:false,
      managerLoder : false ,
      managerErrorMessage : "",
      towerName:"",
      towerData:"",
      isTowerShown:false,
      employeeDetailsData : [],
      employeeUpdate : "",
      isEnable:true,
      errorMessageForValidation : "",
      isErrorShown:false,
      loader:false,
      personEmailMessage:"",
      experienceErrorMessage:"",
      vendorEmailMessage:"",
      emailCheck:false,
      internetCheck:false,
      employeeObject : {},
      smartSearhText: "",
      smartSearchEnable:false,
      domainID:"",
      isUpdateEnable:true,
      prevFormData:{},
      isEmailDisable:true,
      isTmlManagerChange:false,
      employeeType:"",
      employeeTypeErrorMessage:"",
      sioCode:"",
      isSioDisable:true,
      // test:[{ "request": "R1710252678", "fullName": "Roahn Desai", "vendor": "Sankey Business Solutions", "email": "<EMAIL>", "employeeId": "3454" }, { "request": "R1710252679", "fullName": "bahvesh rathod", "vendor": "Sankey Business Solutions", "email": "<EMAIL>", "employeeId": "3454" }, { "request": "R1710252610", "fullName": "Ajay dange", "vendor": "Sankey Business Solutions", "email": "<EMAIL>", "employeeId": "3454" }, { "request": "R1710252123", "fullName": "Pankaj Pal", "vendor": "Sankey Business Solutions", "email": "<EMAIL>", "employeeId": "3454" },{ "request": "R1710252600", "fullName": "Anand vishwkarma", "vendor": "Sankey Business Solutions", "email": "<EMAIL>", "employeeId": "3454" }]
      departmentData : [], 
      edataDepartment:[],
      departmentType: "",
      isDepartmentDisable: true,
      departmentTempData:[],
      showMaskedContent:false,
      existingBuDetails : {},
      isL2Editable:false,
      l2ManagerEmail:"",
      l2ManagerName:"",
      l2ManagerEmployeeId: "",
      source:"",
      l2ManagerCompCode:""

    };
    this.updateUser.bind(this);
    this.onInputChangeForSmartSearch.bind(this);
  }

  componentDidMount() {
    
    this.getVendorList()
    this.getDesignationList()
    this.getProjectList(this.props.vendor)
    this.getTmlLocation()
    this.getSwipeLocationList()
    this.getBuCompanyCodeList()
    this.getTmlRegion()
    this.getSkill()
    this.getTower()
    this.getDepartment()
  }

  isFieldChanged = () => {
    if(
      this.state.prevFormData['firstName'] === this.state.firstName &&
      this.state.prevFormData['middleName'] == this.state.middleName &&
      this.state.prevFormData['lastName'] == this.state.lastName &&
      this.state.prevFormData['personMobile'] == this.state.personMobile &&
      this.state.prevFormData['personEmail'] == this.state.personEmail &&
      this.state.prevFormData['genderType'].value == this.state.genderType.value &&
      this.state.prevFormData['yearOfExperience'] == this.state.yearOfExperience &&
      this.state.prevFormData['designation'].value == this.state.designation.value &&
      this.state.prevFormData['employeeId'] == this.state.employeeId &&
      this.state.prevFormData['manager'] == this.state.manager &&
      this.state.prevFormData['city'] == this.state.city &&
      this.state.prevFormData['department'] == this.state.department &&
      this.state.prevFormData['tmlRegion'].value == this.state.tmlRegion.value &&
      this.state.prevFormData['tmlLocation'].value == this.state.tmlLocation.value &&
      this.state.prevFormData['swipeLocation'].value == this.state.swipeLocation.value &&
      this.state.prevFormData['buCompanyCode'].value == this.state.buCompanyCode.value &&
      this.state.prevFormData['vendorManager'] == this.state.vendorManager &&
      this.state.prevFormData['vendorManagerEmail'] == this.state.vendorManagerEmail &&
      this.state.prevFormData['projectName'].value == this.state.projectName.value &&
      this.state.prevFormData['towerName'] == this.state.towerName &&
      new Date(this.state.prevFormData['validityTo']).toString() == new Date(this.state.validityTo).toString() &&
      this.state.prevFormData['internetCheck'] == this.state.internetCheck &&
      this.state.prevFormData['emailCheck'] == this.state.emailCheck &&
      this.state.prevFormData['tmlManagerPerno'] == this.state.tmlManagerPerno &&
      this.state.prevFormData['tmlManagerCompanyCode'] == this.state.tmlManagerCompanyCode &&
      this.state.prevFormData['domainID'] == this.state.domainID &&
      this.state.prevFormData['employeeType'] == this.state.employeeType.value &&
      this.state.prevFormData['sioCode'] == this.state.sioCode &&
      this.state.prevFormData['department'] == this.state.edataDepartment?.label 
    )
    {
      if(this.state.prevFormData['skill'].length != this.state.skill.length){
        this.setState({
          isUpdateEnable:false
        })
      }
      else{
        this.state.skill.map((skills, index) => {
          if(this.state.prevFormData['skill'][index].skillCode != skills.value){
            this.setState({
              isUpdateEnable:false
            })
            return
          }else{
            this.setState({
              isUpdateEnable:true
            })
          }
        })
      }
      
    }
    else{
      this.setState({
        isUpdateEnable:false
      })
    }
  }

  getVendorList = () => {
    getAPIResponse(config.getVendorList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
        this.setState({
          vendorOption : data.data
        },()=>{this.getVendorName()})
      }
      else {
        console.log("getVendorList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getVendorList() in Request",error)
    })

  }

  getVendorName = () => {
    let vendorname = this.props.vendor
    this.state.vendorOption.map((obj , index) => {
      if(vendorname === obj.vendorCode){
        this.setState({
          vendor:obj.vendorName
        })
      }
    })
  }

  getDesignationList = () =>{
    getAPIResponse(config.getDesignationList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getDesignationList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              designationList : [...prevState.designationList , {value : obj.designationCode , label : obj.designationName}]
            }))
          })
      }
      else {
        console.log("getDesignationList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getDesignationList() in Request",error)
    })
  }

  getProjectList = (vendor) => {
    if(vendor !== null){
      let queryParams = new URLSearchParams({
        'vendor': vendor
      })
      this.setState({
        project:[]
      })
      getAPIResponse(`${config.getProjectList}?${queryParams.toString()}` , "GET" , {})
      .then((response) => {
        if(!response.ok){
          console.log("getProjectList() in Request",response)
          return null
        }
        else{
          return response.json()
        }
      }).then((data) => {
        if(data !== null){
            data.data.map((obj, index) => {
              this.setState(prevState => ({
                project : [...prevState.project , {value : obj.projectCode , label : obj.projectName}]
              }))
            })
        }
        else {
          console.log("getProjectList() in Request",response)
        }
      })
      .catch((error) => {
        console.log("getProjectList() in Request",error)
      })
    }
  }

  getTmlLocation = () => {
    getAPIResponse(config.getTmlOffice , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getTmlLocation() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              tmlLocationData : [...prevState.tmlLocationData , {value : obj.officeCode , label : obj.officeName}]
            }))
          })
      }
      else {
        console.log("getTmlLocation() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getTmlLocation() in Request",error)
    })
  }

  getSwipeLocationList = () => {
    getAPIResponse(config.getSwipeLocationList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getSwipeLocationList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
        const swipeLocationOptions = data.data.map((obj) => ({value : obj.locationCode , label : obj.locationName}))
        this.setState(prevState => ({
          ...prevState,
          swipeLocationOptions
        }),() => {this.getSwipeLocationName()})
      }
      else {
        console.log("getSwipeLocationList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getSwipeLocationList() in Request",error)
    })
  }

  getSwipeLocationName = () => {
    let LocationName = this.props.swipeLocation
    this.state.swipeLocationOptions.map((obj , index) => {
      if(LocationName === obj.value){
        this.setState({
          vendor:obj
        })
      }
    })
  }

  getBuCompanyCodeList = () => {
    getAPIResponse(config.getBuCompanyCodeList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getBuCompanyCodeList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj) => {
            this.setState(prevState => ({
              buCompanyCodeList : [...prevState.buCompanyCodeList , {value : obj.compCode , label : obj.compName}]
            }))
          })
      }
      else {
        console.log("getBuCompanyCodeList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getBuCompanyCodeList() in Request",error)
    })
  }

  getTmlRegion = () => {
    getAPIResponse(config.getTmlRegion , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getTmlRegion() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              tmlRegionData : [...prevState.tmlRegionData , {value : obj.regionCode , label : obj.regionName}]
            }))
          })
      }
      else {
        console.log("getTmlRegion() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getTmlRegion() in Request",error)
    })
  }

  getSkill = () => {
    getAPIResponse(config.getSkillList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getSkill() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              skillData : [...prevState.skillData , {value : obj.skillCode , label : obj.skillName}]
            }))
          })
      }
      else {
        console.log("getSkill() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getSkill() in Request",error)
    })
  }

  getTower = () => {
    getAPIResponse(config.getTowerList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getSkill() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
        data.data.map((obj)=>{
          this.setState(prevState => ({
            towerData : [...prevState.towerData,{value:obj.towerCode , label:obj.towerName}]
          }),()=>{this.getTowerName()})
        })
        
      }
      else {
        console.log("getTower() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getTower() in Request",error)
    })
  }

  getDepartment = () => {
    getAPIResponse(config.getDepartmentList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getDepartment() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              departmentData : [...prevState.departmentData , {value : obj.departmentCode , label : obj.departmentName, type: obj.empType}]
            }))
          })
      }
      else {
        console.log("getDepartment() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getDepartment() in Request",error)
    })
  }
  getTowerName = () => {
    let towerName = this.state.towerName
    this.state.towerData.map((obj , index) => {
      if(towerName === obj.value){
        this.setState({
          towerName:obj
        })
      }
    })
  }

  getVendorEmployeeDetails = (vendor) => {
    let searchParams = new URLSearchParams({
        'vendor':vendor
    })
    getAPIResponse(`${config.getVendorEmployeeDeatils}?${searchParams.toString()}` , "GET", {})
    .then((resposne) => {
        if(resposne.status === 500){
            errorToast(INTERNAL_SERVER_ERROR)
            console.log("getVendorEmployeeDetails() in CreateUpdateRequest",resposne)
            return null
        }
        else{
            resposne.json()
        }
    })
    .then((data)=>{
        if(data !== null && data.status === 1){
        }
        else {
            console.log("getVendorEmployeeDetails() in CreateUpdateRequest",data)
        }
    })
    .catch((error)=>{
        console.log("getVendorEmployeeDetails() in CreateUpdateRequest",error)
        errorToast(SOMETHING_WENT_WRONG)
    })
  }

  resetForm = () => {
    this.setState({
      CompleteName: "",
      employeeId : "",
      firstName : "",
      lastName : "",
      middleName:'',
      personMobile : '',
      personEmail : '',
      designation : "",
      gender : "",
      projectName : "",
      vendorManager : "",
      vendorManagerEmail : "",
      tmlManagerEmail : "",
      location:"",
      department: "",
      genderType:"",
      city:"",
      validityFrom:null,
      validityTo:null,
      manager:"",
      skill: [],
      yearOfExperience:"",
      tmlLocation:"",
      swipeLocation:"",
      buCompanyCode:'',
      tmlRegion:"",
      domainChecked:false,
      emailCheck:false,
      internetCheck:false,
      sioCode:"",
      employeeType:"",
      edataDepartment:[]
    });
  }

  onChangeHandler = (event) => {
    let name = event.target.name; 
    let value = event.target.value;
      if(name === "manager"){
        this.setState({
          sameManagerField :false ,
          CompleteName:"",
          city:"",
          department:"",
          [name]: value  ,
          managerErrorMessage:"",
          isTmlManagerChange:true
        },()=>{this.isFieldChanged()})
      }
      else {
        this.setState({ 
          [name]: value,
        },()=>{this.isFieldChanged()});
      }
  };

  handleDropdownData = (selectedOption, name) => {
    if(name == 'skill'){
      this.setState({
        skill : []
      })
        if (selectedOption.length == 0) {
          this.setState({
            skill: []
          },()=>{this.isFieldChanged()})
        } 
        else {
          let skillArray = selectedOption.map((value) =>{
            if (selectedOption.length < this.state.skill.length) {
              this.setState({
                skill: selectedOption
              },()=>{this.isFieldChanged()})
            } 
            else {
              this.setState(prevState => ({
                skill : [...prevState.skill,value]
              }),()=>{this.isFieldChanged()})
            }
          })
        }
        this.getSkillList()
    }
    else if(name == 'vendor'){
      if(selectedOption.value === 'TCS'){
        this.setState({
          isTowerShown : true,
          [name] : selectedOption,
        },()=>{
            this.getProjectList(selectedOption.value) , 
            this.getTower()
        })
      }
      else {
        this.setState({
          isTowerShown : false,
          [name] : selectedOption
        },()=>{
            this.getProjectList(selectedOption.value),
            this.isFieldChanged()
        });
      }
      
    }
    else if(name == 'towerName'){
      if(this.state.prevFormData[name] == selectedOption.value){
        this.setState({ 
          [name] : selectedOption,
        },()=>{this.isFieldChanged()});
      }
    else {
      this.setState({ 
        [name] : selectedOption,
      },()=>{this.isFieldChanged()});
    }
    }
    else {
        this.setState({ 
          [name] : selectedOption,
        },()=>{this.isFieldChanged()});
    }
  };

 objectsAreEqual = (obj1, obj2) => {
    const keys1 = Object.keys(obj1)
    const keys2 = Object.keys(obj2)
  
    if (keys1.length !== keys2.length) {
      return false;
    }
  
    for (let key of keys1) {
      if (obj1[key] !== obj2[key]) {
        return false;
      }
    }

    return true;
  }
  

  handleDate = (value, name) => {
    let dateValue = value
    let date1 = dayjs(dateValue).format("YYYY-MM-DD")
        this.setState({ 
            [name]: value,
        },()=>{this.isFieldChanged()});
  };

  handleEmailCheck = (event) => {
    const { value, name, checked } = event.target;
          this.setState({
            email:true,
            emailCheck:checked,
          },()=>{this.isFieldChanged()})
  };

  
  handleInternetCheck = (event) => {
    const { value, name, checked } = event.target;
          this.setState({
            internet:true,
            internetCheck:checked,
          },()=>{this.isFieldChanged()})
  };

  validateForm = (CompleteName,employeeId,firstName,lastName,personEmail,personMobile,tmlLocation,buCompanyCode,swipeLocation,designation,validityTo,city,department,gender,vendor,projectName,vendorManager,vendorManagerEmail,tmlManagerEmail,skills,yearOfExperience,tmlRegion,sioCode,edataDepartment) => {
      console.log(sioCode)
      // if ( employeeId == "" || firstName == "" || lastName == "" || personEmail == "" || tmlLocation == "" || designation == "" || validityTo == "" || gender == null || vendor == null || projectName == "" || vendorManager == "" || vendorManagerEmail == "" || tmlManagerEmail == null || skills == [].length || yearOfExperience == "" || tmlRegion == "" || sioCode == null) {
      if ( employeeId == "" || firstName == "" || lastName == "" || personEmail == "" || tmlLocation == "" || buCompanyCode == "" || swipeLocation == "" || designation == "" || validityTo == "" || gender == null || vendor == null || projectName == "" || vendorManager == "" || vendorManagerEmail == "" || tmlManagerEmail == null || skills == [].length || yearOfExperience == "" || tmlRegion == "" || edataDepartment == "") {
          errorToast(PLEASE_ENTER_REQUIRED_FIELD)
          return false;
      }
      else if(personMobile && personMobile.length !== 10){
        errorToast(PLEASE_ENTER_VALID_MOBILE_NO)
          return false;
      }
      else if(!UpdateEmailRegex.test(personEmail) || !UpdateEmailRegex.test(vendorManagerEmail)){
        errorToast(PLEASE_ENTER_VALID_EAMIL_ID);
        return false
      }
      else if (this.state.employeeType?.value === "IT" && (city == null || department == null)){
        errorToast(PLEASE_ENTER_REQUIRED_FIELD);
          return false
      }
      else if (this.state.vendor.value == 'TCS' && this.state.towerName == ""){
          errorToast(PLEASE_ENTER_REQUIRED_FIELD);
          return false
      } 
      else if(this.state.vendorManagerEmail.toLocaleLowerCase() === this.state.manager.toLocaleLowerCase()){
        errorToast(MANAGER_VENDOR_CANNOT_SAME)
        return false
      }
      else if (this.state.manager == "") {
        errorToast(MANAGER_DETAILS_NOT_FOUND)
        return false;
      }
      else if (this.state.isTmlManagerChange && (CompleteName == "" || CompleteName == null) ) {
        errorToast(MANAGER_DETAILS_NOT_FOUND)
        return false;
      }
      else if (this.state.validityFrom > this.state.validityTo){
        errorToast(ERROR_MESSAGE_FOR_VALID_FROM)
        return false
      }
      return true
  };

  getSkillList = () => {
    console.log("getSkillList");
    let skillList = this.state.skill.map((value , index) => value.value)
    return skillList
  }

  capitalizeEachLetter = (vendorName) => {
    return vendorName
    .split(' ')
    .map(word => word.split('').map((letter, index) => index === 0 ? letter.toUpperCase() : letter).join(''))
    .join(' ');
  }

  getSkillDropdownData = (list) => {
    let skillList = list.map(value =>({
      value : value.skillCode,
      label : value.skillName
    }))
    return skillList
  }

  customLabel = (value, index) => {
    const employeeName = value.request
    this.setState(prevState => ({
      employeeObject : {
        ...prevState.employeeObject,
        [employeeName]: [value.fullName , value.vendor.vendorName , value.vendor.vendorCode]
      }
    }));
    
    return (
      <div key={`employee-details-${index}`} className="search-conatiner">
        <b><span key="fullName">{value.fullName}</span></b>
        <br key="br1"/>
        <span key="employeeId" className="smartsearch">{value.employeeId}</span>
        <span key="separator2" className="smartsearch"> |  </span>
        <span key="vendor" className="smartsearch">{value.vendor.vendorName}</span>
        <br key="br2"/>
        <span key="email" className="smartsearch">{value.email}</span>
      </div>
    )
  }

  smartSearchForEmployee = () => {
    if(this.state.smartSearhText !== ""){
      this.setState({
        loader:true
      })
      let searchParams = new URLSearchParams({
        "search_text":this.state.smartSearhText,
        "include_disable_accounts": false
      })
      return getAPIResponse(`${config.getVendorEmployeeDeatils}?${searchParams.toString()}` , 'GET' , {})
      .then((response)=>{
        if(response.status === 500){
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("smartSearchForEmployee() in CreateUpdateRequest",response)
          return null
        }
        else {
          return response.json()
        }
      }).then((data)=>{
        if(data !== null && data.status == 1){
          let options = []
          options = data.data.map((value, index) => {   return {value: value.request, label: this.customLabel(value, index)}})
          return options
        } 
        else {
          this.setState({
            loader:false,
          })
          console.log("smartSearchForEmployee() in CreateUpdateRequest",data)
        }
      })
      .catch((error)=>{
        console.log("smartSearchForEmployee() in CreateUpdateRequest",error)
        errorToast(SOMETHING_WENT_WRONG)
      })
    }
    
  }


  validateUser = () => {
    this.setState({
      errorMessageForValidation:""
    })
    if(this.state.vendor === null || this.state.vendor === ""){
      errorToast(PLEASE_SELECT_VENDOR_COMPANY)
    }
    else if(this.state.employeeUpdate === null || this.state.employeeUpdate === ""){
      errorToast(PLEASE_ENTER_EMPLOYEE_DETAILS)
    }
    else {
        this.setState({
          loader:true,
        })
        getAPIResponse(`${config.getDetailsByRequestId}/${this.state.employeeUpdate?.value}` , "GET" , {})
        .then((response) => {
            if(response.status === 500){
              errorToast(INTERNAL_SERVER_ERROR)
              console.log("validateUser() in createUpdateRequest",response)
              return null
            }
            else {
              return response.json()
            }
        })
        .then((data)=>{
            if(data !== null){
                if(data.status === 1){
                  // console.log("test:",data.data.ndaEmployeeDetails.edata?.department ? true : data.data.ndaEmployeeDetails.employeeType == "IT" ? true : false)
                    let itdepartment = this.state.departmentData.find(obj => obj.value === 'IT');
                    let nonItDepartment = this.state.departmentData.find(obj => obj.value === "NONIT");
                    let tempDepartmentList =data.data.ndaEmployeeDetails.employeeType != "IT"? this.state.departmentData.filter(obj => obj.type !== 'IT'): this.state.departmentData.filter(obj => obj.type !== 'NONIT')
                    let empDepartment = data.data?.ndaEmployeeDetails?.edata?.department ? this.state.departmentData.find(obj => obj.label === data.data.ndaEmployeeDetails.edata?.department): tempDepartmentList[0]
                    let swipeLocation = data.data?.ndaEmployeeDetails?.swipeLocation ? this.state.swipeLocationOptions.find(o=>o.value === data.data?.ndaEmployeeDetails?.swipeLocation?.locationCode):this.state.swipeLocationOptions.find(o=>o.label==='NA');
                    let buDetails = data.data?.ndaEmployeeDetails?.buDetails ? this.state.buCompanyCodeList.find(o=>String(o.value)===String(data?.data?.ndaEmployeeDetails?.buDetails?.compCode)) : this.state.buCompanyCodeList.find(o=>String(o.value)===String(data?.data?.ndaEmployeeDetails?.tmlManager?.compCode));
                    let managerEmail = data.data?.ndaEmployeeDetails?.tmlManager.email.trim()
                    const l2 = data?.data?.l2Manager;

                    let L2Editable = false;
                    let l2Name = "";
                    let l2Email = "";

                    if (l2 && Object.keys(l2).length > 0) {
                      l2Name = l2.name || "";
                      l2Email = l2.email || "";
                      L2Editable = false;  
                    } else {
                      L2Editable = true;   
                    }
                    this.setState({
                        isEnable:false,
                        firstName:data.data.ndaEmployeeDetails.firstName,
                        middleName:data.data.ndaEmployeeDetails.middleName,
                        lastName:data.data.ndaEmployeeDetails.lastName,
                        personMobile:data.data.ndaEmployeeDetails.mobile,
                        personEmail:data.data.ndaEmployeeDetails.email,
                        genderType:{value:data.data.ndaEmployeeDetails.gender,label:data.data.ndaEmployeeDetails.gender},
                        yearOfExperience:data.data.ndaEmployeeDetails.yearsOfExperience,
                        skill:this.getSkillDropdownData(data.data.ndaEmployeeDetails.skills),
                        designation:{value:data.data.ndaEmployeeDetails.designation?.designationCode,label:data.data.ndaEmployeeDetails.designation?.designationName},
                        employeeId:data.data.ndaEmployeeDetails.employeeId,
                        manager:data.data.ndaEmployeeDetails.tmlManager.email,
                        city:data.data.ndaEmployeeDetails.tmlManager.city, 
                        department:data.data.ndaEmployeeDetails.tmlManager.functionText,
                        tmlRegion:{value:data.data.ndaEmployeeDetails.tmlRegion?.regionCode,label:data.data.ndaEmployeeDetails.tmlRegion?.regionName},
                        tmlLocation:{value:data.data.ndaEmployeeDetails.tmlOffice?.officeCode,label:data.data.ndaEmployeeDetails.tmlOffice?.officeName},
                        swipeLocation,
                        buCompanyCode:buDetails,
                        vendorManager:data.data.ndaEmployeeDetails.vendorManagerName,
                        vendorManagerEmail:data.data.ndaEmployeeDetails.vendorManagerEmail,
                        projectName:{value:data.data.ndaEmployeeDetails.project?.projectCode,label:data.data.ndaEmployeeDetails.project?.projectName},
                        towerName:data.data.ndaEmployeeDetails.edata?.tower,
                        validityTo:dayjs(data.data.ndaEmployeeDetails.validTill),
                        validityFrom:dayjs(data.data.ndaEmployeeDetails.validFrom),
                        internetCheck:data.data.ndaEmployeeDetails.isInternet,
                        emailCheck:data.data.ndaEmployeeDetails.isEmail,
                        isEmailDisable:data.data.ndaEmployeeDetails.isEmail,
                        tmlManagerPerno:data.data.ndaEmployeeDetails.tmlManager.employeeId,
                        tmlManagerCompanyCode:data.data.ndaEmployeeDetails.tmlManager.compCode,
                        domainID:data.data.ndaEmployeeDetails.domainId,
                        loader:false,
                        smartSearchEnable:true,
                        employeeType:{value:data.data.ndaEmployeeDetails.employeeType,label:data.data.ndaEmployeeDetails.employeeType == "" ? "" : data.data.ndaEmployeeDetails.employeeType == "NONIT" ? "Non IT" : "IT"},
                        sioCode:data.data.ndaEmployeeDetails.edata?.sioCode,
                        isSioDisable:data.data.ndaEmployeeDetails.edata?.sioCode,
                        // edataDepartment:data.data.ndaEmployeeDetails.edata?.department ? data.data.ndaEmployeeDetails.edata?.department : data.data.ndaEmployeeDetails.employeeType == "IT" ? itdepartment : nonItDepartment,
                        edataDepartment: typeof empDepartment !== 'undefined' && Object.keys(empDepartment).length > 0 ? empDepartment :  data.data.ndaEmployeeDetails.employeeType == "IT" ? itdepartment : nonItDepartment,
                        // isDepartmentDisable:data.data.ndaEmployeeDetails.edata?.department ? true : data.data.ndaEmployeeDetails.employeeType == "IT" ? true : false,
                        departmentTempData: data.data.ndaEmployeeDetails.employeeType != "IT"? tempDepartmentList.filter(obj => obj.value !== 'IT'): tempDepartmentList,
                        existingBuDetails : {
                          manager: managerEmail,
                          buCode : buDetails.value
                        },
                        l2ManagerName: l2Name,
                        l2ManagerEmail: l2Email,
                        l2ManagerEmployeeId:data.data.ndaEmployeeDetails.tmlManager.l2_manager,
                        l2ManagerCompCode:data.data.ndaEmployeeDetails.tmlManager.l2_compcode,
                        
                    },()=>{if(this.state.towerName) this.getTowerName()})
                    // ----------------------------- store to check previous value ---------------------------------------
                    this.setState({
                      prevFormData : {
                        ['firstName']:data.data.ndaEmployeeDetails.firstName,
                        ['middleName']:data.data.ndaEmployeeDetails.middleName,
                        ['lastName']:data.data.ndaEmployeeDetails.lastName,
                        ['personMobile']:data.data.ndaEmployeeDetails.mobile,
                        ['personEmail']:data.data.ndaEmployeeDetails.email,
                        ['genderType']:{value:data.data.ndaEmployeeDetails.gender,label:data.data.ndaEmployeeDetails.gender},
                        ['yearOfExperience']:data.data.ndaEmployeeDetails.yearsOfExperience,
                        ['skill']:data.data.ndaEmployeeDetails.skills,
                        ['designation']:{value:data.data.ndaEmployeeDetails.designation?.designationCode,label:data.data.ndaEmployeeDetails.designation?.designationName},
                        ['employeeId']:data.data.ndaEmployeeDetails.employeeId,
                        ['manager']:data.data.ndaEmployeeDetails.tmlManager.email,
                        ['city']:data.data.ndaEmployeeDetails.tmlManager.city,
                        ['department']:data.data.ndaEmployeeDetails.tmlManager.functionText,
                        ['tmlRegion']:{value:data.data.ndaEmployeeDetails.tmlRegion?.regionCode,label:data.data.ndaEmployeeDetails.tmlRegion?.regionName},
                        ['tmlLocation']:{value:data.data.ndaEmployeeDetails.tmlOffice?.officeCode,label:data.data.ndaEmployeeDetails.tmlOffice?.officeName},
                        ['swipeLocation']:swipeLocation,
                        ['buCompanyCode']:buDetails,
                        ['vendorManager']:data.data.ndaEmployeeDetails.vendorManagerName,
                        ['vendorManagerEmail']:data.data.ndaEmployeeDetails.vendorManagerEmail,
                        ['projectName']:{value:data.data.ndaEmployeeDetails.project?.projectCode,label:data.data.ndaEmployeeDetails.project?.projectName},
                        ['towerName']:data.data.ndaEmployeeDetails.edata?.tower,
                        ['validityTo']:dayjs(data.data.ndaEmployeeDetails.validTill),
                        ['validityFrom']:dayjs(data.data.ndaEmployeeDetails.validFrom),
                        ['internetCheck']:data.data.ndaEmployeeDetails.isInternet,
                        ['emailCheck']:data.data.ndaEmployeeDetails.isEmail,
                        ['tmlManagerPerno']:data.data.ndaEmployeeDetails.tmlManager.employeeId,
                        ['tmlManagerCompanyCode']:data.data.ndaEmployeeDetails.tmlManager.compCode,
                        ['domainID']:data.data.ndaEmployeeDetails.domainId,
                        ['employeeType']:data.data.ndaEmployeeDetails.employeeType,
                        ['sioCode'] : data.data.ndaEmployeeDetails.edata?.sioCode,
                        ['department'] : data.data.ndaEmployeeDetails.edata?.department
                      }
                    })
                }
                else{
                    this.setState({
                        isErrorShown:true,
                        errorMessageForValidation:data.message,
                        loader:false
                    })
                }
            }
        })
        .catch((error)=>{
            console.log("validateUser() in createUpdateRequest",error)
            errorToast(SOMETHING_WENT_WRONG)
        })
    }
  }

  updateUser = (event) => {
    // if (!this.state.buCompanyCode || !this.state.buCompanyCode.value) {
    //     errorToast("Please select BU Company Code");
    //     return;
    // }
    let flag = this.validateForm(this.state.CompleteName,this.state.employeeId,this.state.firstName,this.state.lastName,this.state.personEmail,this.state.personMobile,this.state.tmlLocation,this.state.buCompanyCode,this.state.swipeLocation,this.state.designation,this.state.validityTo,this.state.city,this.state.department,this.state.genderType,this.state.vendor,this.state.projectName,this.state.vendorManager,this.state.vendorManagerEmail,this.state.manager,this.state.skill,this.state.yearOfExperience,this.state.tmlRegion,this.state.sioCode,this.state.edataDepartment);
    if (flag) {
      this.setState({
        isLoading : true,
        disable:true
      })
      let apiFormatedData = {
        employeeId : this.state.employeeId,
        firstName : (this.state.firstName.charAt(0).toUpperCase() + this.state.firstName.toLowerCase().slice(1)).trim(),
        middleName : this.state.middleName?.trim().charAt(0).toUpperCase() + this.state.middleName?.trim().toLowerCase().slice(1),
        lastName : (this.state.lastName.charAt(0).toUpperCase() + this.state.lastName.toLowerCase().slice(1)).trim(),
        mobile : this.state.personMobile || null,
        email : this.state.personEmail.toLowerCase().trim(),
        designation : this.state.designation.value,
        gender : this.state.genderType.value,
        vendor : this.state.vendor.value,
        project : this.state.projectName.value,
        vendorManagerName: this.capitalizeEachLetter(this.state.vendorManager.trim()),
        vendorManagerEmail: this.state.vendorManagerEmail.toLowerCase().trim(),
        tmlManagerEmployeeId:this.state.tmlManagerPerno,
        tmlManagerCompCode:this.state.tmlManagerCompanyCode,
        createdBy:this.props.loginData.empId,
        validTill:dayjs(this.state.validityTo).format("YYYY-MM-DD"),
        validFrom:dayjs(this.state.validityFrom).format("YYYY-MM-DD"),
        skills : this.getSkillList(),
        tmlRegion:this.state.tmlRegion.value,
        tmlOffice:this.state.tmlLocation.value,
        swipeLocation:this.state.swipeLocation.value,
        buCompCode:this.state.buCompanyCode.value,
        yearsOfExperience:this.state.yearOfExperience,
        isEmail:this.state.emailCheck,
        isInternet:this.state.internetCheck,
        requestType:'Update',
        // tower:this.state.towerName?.value,
        edata:{tower:this.state.towerName?.value,sioCode:this.state.sioCode},
        old_request_id:this.state.employeeUpdate?.value,
        domainID:this.state.domainID,
        employeeType:this.state.employeeType?.value,
        edataDepartment:this.state.edataDepartment?.label,
        tmlManagerEmail:this.state.manager,
        l2ManagerEmail:this.state.l2ManagerEmail,
        l2ManagerName:this.state.l2ManagerName,
        l2ManagerEmployeeId:this.state.l2ManagerEmployeeId,
        l2ManagerCompCode:this.state.l2ManagerCompCode

      };
      if(this.props.isTmlEmployee !== true &&  this.props.vendor !== 'TCS'){
        delete apiFormatedData.edata.tower
      }
      getAPIResponse(config.updateRequest , "POST", apiFormatedData)
      .then((response)=>{
        if(response.status === 500){
          this.setState({
            isLoading:false,
            disable:false
          })
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("handleSubmit() in request",response)
          return null
          }
          else {
           return response.json()
          }
      })
      .then((data)=>{
        if(data !== null){
          if(data.status === 1){
            this.setState({
              isLoading : false,
              isTowerShown:false
            })
            successToast(data.message)
            this.goNext()
          }
          else {
            this.setState({
              isLoading:false,
              disable:false
            })
            errorToast(data.message)
          }
        }
      })
      .catch((error)=>{
        this.setState({
          isLoading:false,
          disable:false
        })
        console.log("handleSubmit() in request",error)
      })
      //  https://stackoverflow.com/questions/43842793/basic-authentication-with-fetch
    }
  };

  // managerDetails = () => {
  //   if(this.state.manager == "") {
  //     this.setState({
  //       managerErrorMessage : PLEASE_ENTER_MANAGER_EMAIL
  //     })
  //   }
  //   else if (this.state.employeeType == ""){
  //     this.setState({
  //       employeeTypeErrorMessage : PLEASE_SELECT_EMPLOYEE_TYPE
  //     })
  //   }
  //   else {
  //     let body = {
  //       "manager" : this.state.manager.trim(),
  //       "employeeType" : this.state.employeeType?.value  
  //     }
  //     this.setState({
  //       managerLoder : true
  //     })
  //     getAPIResponse(config.getManagerDetails , "POST" , body)
  //     .then((response)=>{
  //       this.setState({
  //         managerLoder : false
  //       })
  //       if(response.status === 500){
  //         errorToast(INTERNAL_SERVER_ERROR)
  //         console.log("managerDetails() in Request",response)
  //         return null
  //       }
  //       else {
  //         return response.json()
  //       }
  //     })
  //     .then((data) => {
  //       if(data !== null && data.status === 1){
  //           this.setState({
  //             city:data.data.citytown,
  //             department:data.data.department,
  //             tmlManagerTitle:data.data.title,
  //             CompleteName:data.data.fullName,
  //             tmlManagerPerno:data.data.employeeId,
  //             tmlManagerCompanyCode:data.data.compCode,
  //             isTmlManagerChange:false,
  //             source:data.data.source
  //           },()=>{
  //             this.isFieldChanged()
  //             this.handleBuCode()
  //           })
  //       }
  //       else {
  //         this.setState({
  //           CompleteName:"",
  //           managerErrorMessage:data.message
  //         },()=>{this.isFieldChanged()})
  //         console.log("managerDetails() in Request",data)
  //       }
  //     })
  //     .catch((error) => {
  //       console.log("managerDetails() in Request",error)
  //     })
  //   }
    
  // }

    managerDetails = () => {
    if(this.state.manager == "") {
      this.setState({
        managerErrorMessage : PLEASE_ENTER_MANAGER_EMAIL
      })
    }
    else if(this.state.employeeType == ""){
      this.setState({
        employeeTypeErrorMessage : PLEASE_SELECT_EMPLOYEE_TYPE
      })
    }
    else 
    {
      let body = {
        "manager" : this.state.manager.trim(),
        "employeeType" : this.state.employeeType?.value  
      }
      this.setState({
        managerLoder : true
      })
  getAPIResponse(config.getManagerDetails_v2 , "POST" , body)
      .then((response)=>{
        this.setState({
          managerLoder : false
        })
        if(response.status === 500){
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("managerDetails() in Request",response)
          return null
        }
        else {
          return response.json()
        }
      })
      
      .then((data) => {
        if(data !== null && data.status === 1){
          const l1 = data.data.l1_manager_details;
      const l2 = data.data.l2_manager_details || {};
      const source = l1.source;

      const hasL2Data = l2.fullName && l2.email;

      let l2Editable = false;
      if(source === "SAP"){
        if (l2?.error){
          errorToast(l2.error);
       l2Editable = true;
       l2.fullName = "";
       l2.email = "";
        }
        else if (!l2?.fullName || !l2?.email){
          if (!l2?.fullName) {
          errorToast("L2 Manager Name not found in response.");
        }
        if (!l2?.email) {
          errorToast("L2 Manager Email not found in response.");
        }

        }
      } 
      if (source === "AD") {
        alert("Enter the Manager Name and Email")
        l2Editable = true;
        // l2.fullName = "";
        // l2.email = "";
      }
        else {
        
        l2Editable = false; 
      }
            this.setState({
              city: l1.citytown,
              department: l1.department,
              tmlManagerTitle: l1.title,
              CompleteName: l1.fullName,
              tmlManagerPerno: l1.employeeId,
              tmlManagerCompanyCode: l1.compCode,
              source: source,
              isTmlManagerChange:false,
              l2ManagerName: l2.fullName || "",
              l2ManagerEmail: l2.email || "",
              l2FieldsEditable: l2Editable,

              l2ManagerEmployeeId: l2.employeeId || "",
              l2ManagerCompCode: l2.compCode || "",

            },()=>this.handleBuCode())
        }
        else {
          errorToast(data.message);
          console.log("managerDetails() in Request",data)
        }
      })
      .catch((error) => {
        console.log("managerDetails() in Request",error)
      })
    }
    
  }


fetchL2ManagerDetails = async () => {
  const email = this.state.l2ManagerEmail;
  if (this.state.l2ManagerEmail == "") {
    errorToast("Please Enter Manager Email")
  }
  else if (this.state.employeeType === "") {
    errorToast("Please Select Employee Type")
  }
  else{
    let body = {
        "manager" : this.state.l2ManagerEmail.trim(),
        "employeeType" : this.state.employeeType?.value  
      }
      this.setState({
        managerLoder : true
      })
      getAPIResponse(config.getManagerDetails , "POST", body)
      .then((response)=>{
        console.log("Raw Response:", response);
        this.setState({
          managerLoder : false
        })
        if(response.status === 500){
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("managerDetails() in Request",response)
          return null
        }
        else {
          return response.json()
        }
      })
      .then((data) => {
        if (data.data.source === "AD") {
                errorToast("L2 Manager details not found. Please enter manually.");
                return;
              }
        if(data !== null && data.status === 1){
          this.setState({
          l2ManagerName: data.data.fullName || "",
          l2ManagerEmployeeId: data.data.employeeId || "",
          tmlManagerCompanyCode: data.data.compCode || "",
        },()=>this.handleBuCode())
        }
        else {
          errorToast(data.message);
          console.log("managerDetails() in Request",data)
        }
      })
      .catch((error) => {
        console.log("managerDetails() in Request",error)
      })
    }
  }

  // ---------------------------sujal gupta------------------------

  handleBuCode = ()=>{
    if(this.state.source === 'SharePoint' || this.state.source === 'SAP'){
      const buCompanyCode = this.state.buCompanyCodeList.find(obj=>String(obj.value)===String(this.state.tmlManagerCompanyCode));
      this.setState(prevState=>({...prevState,buCompanyCode,isBuDisabled:true}))
    }
    if(this.state.source === 'AD'){
      let existingManagerDetails = this.state.existingBuDetails
      let managerEmail = this.state.manager.trim();
      
      if("manager" in existingManagerDetails && existingManagerDetails.manager==managerEmail){
        const buCompanyCode = this.state.buCompanyCodeList.find(obj=>String(obj.value)===String(existingManagerDetails.buCode));
        
        this.setState(prevState=>({...prevState,buCompanyCode,isBuDisabled:false}))
      }
      else{
        // this.setState(prevState=>({...prevState,buCompanyCode:"",isBuDisabled:false}))
        const buCompanyCode = this.state.buCompanyCodeList.find(obj=>String(obj.value)===String(this.state.tmlManagerCompanyCode));
        this.setState(prevState=>({...prevState,buCompanyCode,isBuDisabled:true}))
      }
    }
  }

  handleDatalist = (event) => {
    this.setState({
      showDropDown:true,
      target: event.target
    })
  }

  handleCloseDropdown = (event) => {
    let value = event.target.value
    if(value.length > 5){
    }
    this.setState({
      showDropDown:false
    })
  }

  handleEmployeeSearch = (event) => {
    if(this.state.employeeObject[event.value][2] == "TCS"){
      this.setState({
          isTowerShown:true,
          vendor:{value:this.state.employeeObject[event.value][2],label:this.state.employeeObject[event.value][1]},
          employeeUpdate : {value:event.value,label:this.state.employeeObject[event.value][0]}
      },()=>{this.getProjectList(this.state.employeeObject[event.value][2]) , this.validateUser()})
    }
    else {
      this.setState({
        vendor:{value:this.state.employeeObject[event.value][2],label:this.state.employeeObject[event.value][1]},
        employeeUpdate : {value:event.value,label:this.state.employeeObject[event.value][0]}
      },()=>{this.getProjectList(this.state.employeeObject[event.value][2]) , this.validateUser()})
    }
  }


  validateInput = (event) => {
    input = event.target.value.replace(/\D/g, ''); // Remove non-numeric characters
    if (input.length > 4) {
      input.value = input.value.slice(0, 4); // Limit to 4 digits
    }
  }

  setValidTill = (value) => {
    const validityFrom = this.state.validityFrom
    console.log("validityFrom",validityFrom)
    this.setState({
      validityTo : value.setMonth(value.getMonth() + 6)
    })
  }

  onChangeEmailHandler = (value , errorValue , error , name) => {
      if (errorValue !== "") {
        this.setState({[error] : "" , [name] : value },()=>{this.isFieldChanged()})
      }
      else {
        this.setState({[name] : value },()=>{this.isFieldChanged()})
      }
  }

  onMobileNumberChange = ({target:{value}})=>{
    let pattern = /^[0-9\b]+$/;
    if(pattern.test(value) || value == ''){
      this.setState({
        personMobile:value
      },()=>{this.isFieldChanged()})
    }
  }

  inputValidation = (value , name) => {
      if(value !== this.state[name]){
        const regex = /[^A-Za-z0-9\s]+$/;
        const regex_employeeID = /[^A-Za-z0-9]+$/;
        if(name === "employeeId"){
          if(!regex_employeeID.test(value)){
            this.setState({
              [name] : value
            },this.isFieldChanged())
          }
        }
        else if (!regex.test(value)){
          this.setState({
            [name] : value
          }, ()=>{this.isFieldChanged()})
        }
      }
  }

  emailValidations = (value , error , name) => {
    const regex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/i;
    // if(value !== '' && !regex.test(value)){
    if(value !== '' && !UpdateEmailRegex.test(value)){
      this.setState({
        [error] : PLEASE_ENTER_VALID_EAMIL_ID,
        [name] : null
      })
    } 
  }

  alphabetValidations = (value, name) => {
    const regex = /[^A-Za-z\s]+$/;
    if(!regex.test(value)){
      this.setState({
        [name] : value
      })
    }
  }

  numberValidation = (value , name , error) => {
    if(this.state.experienceErrorMessage !== "") this.setState({experienceErrorMessage : ""})
    const pattern = /^[0-9\b]+$/;
    if(pattern.test(value)){
      if(parseInt(value) > 60){
        this.setState({ [name] : null , [error]  : YEARS_OF_EXPERIENCE_ERROR })
      }
      else {
        this.setState({
          [name] : value,
        },()=>{this.isFieldChanged()})
      }
    }
    else if(value == ""){
      this.setState({
        [name] : value
      },()=>{this.isFieldChanged()})
    } 
  }

  onChangeMobileNumber = ({target:{value}}) =>{
    let pattern = /^[0-9\b]+$/;
    if(pattern.test(value) || value == ''){
      this.setState({
        personMobile:value
      },()=>{this.isFieldChanged()})
    }
  }


  onInputChangeForSmartSearch = (value) => {
      this.setState({
        smartSearhText : value,
      })
  }

  clearSearch = () => {
    this.setState({
      employeeUpdate:"",
      isEnable:true,
      smartSearchEnable:false,
      vendor:this.props.isTmlEmployee === true ? "" : this.state.vendor,
      isUpdateEnable:true,
      isSioDisable:true,
      isEmailDisable:true
    },() => {this.resetForm()})
  }

  handleEmployeeType = (e) => {
    if (e.value == 'IT'){
      let department = this.state.departmentData.find(obj => obj.value === 'IT');
      let departmentTempData = this.state.departmentData.filter(obj => obj.value === 'IT');
      console.log("department:",department)
      this.setState({
        isDepartmentDisable:true,
        departmentTempData,
        edataDepartment: department
      })
    }
    else{
      let department = this.state.departmentData.filter(obj => obj.value !== 'IT');
      let selectedDepartment = this.state.departmentData.find(obj => obj.value === "NONIT");
      console.log("selectedDepartment:",selectedDepartment )
      this.setState({
        isDepartmentDisable:false,
        departmentTempData: department,
        edataDepartment: selectedDepartment,
      })
    }
    
    this.setState({
        employeeType : e,
        manager:"",
        employeeTypeErrorMessage:"",
        CompleteName:"",
        city:"",
        department:"",
    },()=>{this.isFieldChanged()})
  }

  handleProjectName = (e) => {
    this.setState({
      projectName : e
    },()=>{this.isFieldChanged()})
    if(JSON.stringify(this.state.prevFormData["projectName"]) != JSON.stringify(e)){
      this.setState({
        isSioDisable:false
      })
    }
    else{
      if(!this.state.sioCode){
        this.setState({
          isSioDisable:false
        })
      }else {
        this.setState({
          isSioDisable:true
        })
      }
      
    }
  }

  goNext = () => {
    Router.push({
      pathname: "/Dashboard",
    });
  };


  onInputChange2 = (value) => {
    console.log("Input Value:", value);
    this.setState({ smartSearhText: value });
    return value; // Returning value prevents unnecessary re-renders in AsyncSelect
  };

  smartSearch = async (inputValue) => {
    if (!inputValue) return [];

    this.setState({ loader: true });

    let searchParams = new URLSearchParams({
      "search_text":inputValue,
      "include_disable_accounts": false
    })

    try {
      const response = await getAPIResponse(`${config.getVendorEmployeeDeatils}?${searchParams.toString()}` , 'GET' , {})
      if (response.status === 500) {
        errorToast(SOMETHING_WENT_WRONG);
        console.error("smartSearchForEmployee() Error:", response);
        return [];
      }

      const data = await response.json();

      if (data && data.status === 1) {
        let options = data.data.map((value, index) => ({
          value: value.request,
          label: this.customLabel(value, index),
        }));
        console.log("Fetched Options:", options);
        return options;
      } else {
        console.log("No valid data:", data);  
        return [];
      }
    } catch (error) {
      console.error("API Call Error:", error);
      errorToast(SOMETHING_WENT_WRONG);
      return [];
    } finally {
      this.setState({ loader: false });
    }
  }


  maskEmail = (email) => {
    if(!email) return '';
    if (!email || !email.includes("@")) return "****@****.***";
  
    const [name, domain] = email.split("@");
    const maskedName = name[0] + "*".repeat(Math.max(0, name.length - 2)) + name.slice(-1);
    
    return maskedName + "@" + domain;
  };

  maskMobileNumber = (number) => {
    if(!number) return '';
    if (number.length < 4) return "****"; 
    return number.replace(/\d(?=\d{4})/g, "*");
  };


  render() {
    return (
      <>
      <div>
        <div className="row">
          <div className="col-12">
            <Form autoComplete="off">
              <div className="card card-primary card-outline">
                <div className="card-header with-border">
                  <h2 className="card-title">Update Request</h2>
                </div>
                <div className="card-body">

                  {/* ------------------------------ Validation part ---------------------------   */}
                    <div className="validate-group">
                        <Col md="3">
                            <Form.Group controlId="Vendor">
                            <Form.Label> Vendor Name </Form.Label><sup style={{color:'red'}}>*</sup>
                            <Select
                                instanceId={"Vendor"}
                                options={this.state.vendorOption}
                                value={this.state.vendor}
                                onChange={(e) => {this.handleDropdownData(e, "vendor")}}
                                isSearchable
                                isFocused={false}
                                name="vendor"
                                placeholder="Vendor Company Name"
                                className="myclass"
                                noOptionsMessage={({ inputValue }) =>
                                "No results found"
                                }
                                isDisabled={true}
                            />
                            </Form.Group>
                        </Col>

                        <Col md="3">
                            <Form.Group controlId="employeeDetails">
                            <Form.Label>Employee Details </Form.Label><sup style={{color:'red'}}>*</sup>
                                <AsyncSelect
                                    instanceId={"employeeDetails"}
                                    value={this.state.employeeUpdate}
                                    onChange={(e) => {this.handleEmployeeSearch(e)}}
                                    // onInputChange={(e)=>{this.onInputChangeForSmartSearch(e)}} 
                                    // loadOptions={this.smartSearchForEmployee}
                                    onInputChange={this.onInputChange2}
                                    loadOptions={this.smartSearch}
                                    name="employeeUpdate"
                                    placeholder="Search By Employee Details"
                                    className="myclass"
                                    isDisabled={this.state.smartSearchEnable}
                                    noOptionsMessage={()=>"No data present.Please Sync"}
                                />
                                
                                {this.state.isErrorShown? <span className="validation-error">{this.state.errorMessageForValidation}</span> : null}
                            </Form.Group>
                        </Col>
                        
                        <Col md='5' className="d-flex justify-content-between">
                        <div className="validate" onClick={this.clearSearch}>
                              <Tooltip title="Clear" color="#FFF" overlayInnerStyle={{color:"#000000"}}>
                                <div className="clear-button"><VscClearAll className="clear-icon"/></div> 
                              </Tooltip>  
                              
                        </div>
                        <div className="validate" onClick={()=>{this.setState(prevState=>({...prevState,showMaskedContent:!prevState.showMaskedContent}))}}>
                        <Tooltip title={this.state.showMaskedContent ? 'Hide':'Show'} color="#FFF" overlayInnerStyle={{color:"#000000"}}>
                                <div className="clear-button">{this.state.showMaskedContent ? <FaEyeSlash className="clear-icon"/>:<FaEye className="clear-icon"/>}</div> 
                              </Tooltip> 
                        </div>
                        </Col>
                         
                    </div> 
                  {/* ------------------------------ Person Details ----------------------------- */}
                  <div className="form-section">
                    <span>Personal Details</span>
                  </div>

                  <Row className="form-grid">
                    <Col md="3">
                      <Form.Group controlId="Firstname">
                        <Form.Label> First Name  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <InputGroup>
                          <Form.Control
                            value={this.state.firstName}
                            type="text"
                            name="firstName"
                            onChange={(e)=>{this.alphabetValidations(e.target.value , "firstName")}}
                            placeholder=" Enter First Name"
                            disabled={this.state.isEnable}
                            maxLength="20"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="middleName">
                        <Form.Label> Middle Name </Form.Label>
                        <InputGroup>
                          <Form.Control
                            value={this.state.middleName}
                            type="text"
                            name="middleName"
                            onChange={(e)=>{this.alphabetValidations(e.target.value , "middleName")}}
                            placeholder=" Enter Middle Name"
                            disabled={this.state.isEnable}
                            maxLength="20"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="lastName">
                        <Form.Label> Last Name  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <InputGroup>
                          <Form.Control
                            value={this.state.lastName}
                            type="text"
                            // required
                            name="lastName"
                            onChange={(e)=>{this.alphabetValidations(e.target.value , "lastName")}}
                            placeholder=" Enter Last Name"
                            disabled={this.state.isEnable}
                            maxLength="20"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>

                    <Col md="3" className="detail-view-flex">
                      <Form.Group controlId="Contact">
                        <Form.Label> Mobile No. </Form.Label>
                          <Form.Control
                            pattern="[1-9]{1}[0-9]{9}"
                            type="text"
                            name="personMobile"
                            onChange={this.onChangeMobileNumber}
                            readOnly={this.state.isEnable} // Makes the field read-only if isEnable is true
                            value={this.state.showMaskedContent ? this.state.personMobile : this.maskMobileNumber(this.state.personMobile)}
                            placeholder="Enter Contact Number"
                            maxLength="10"
                            disabled={this.state.isEnable} // Disables the field if isEnable is true
                          />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="Email">
                        <div className="d-flex align-items-center justify-content-between">
                        <div>
                        <Form.Label> Vendor Partner Email ID </Form.Label><sup style={{color:'red'}}>*</sup>
                        </div>
                        <Tooltip placement="top" title={`Enter the official email ID provided by the vendor for onboarding.`} arrow >
                        <FaInfoCircle/>
                        </Tooltip>
                        </div>
                        <Form.Control
                          type="email"
                          name="personEmail"
                          value={this.state.showMaskedContent ? this.state.personEmail : this.maskEmail(this.state.personEmail)}
                          onChange={(e) => {this.onChangeEmailHandler(e.target.value , this.state.personEmailMessage , "personEmailMessage" , 'personEmail')}}
                          placeholder="Enter Vendor Partner Email"
                          onBlur={()=>{this.emailValidations(this.state.personEmail , "personEmailMessage" , "personEmail")}}
                          disabled={this.state.isEnable}
                        />
                        { this.state.personEmailMessage !== "" && this.state.personEmailMessage ? 
                              <span style={{color:"red" , fontSize:"12px"}}>{this.state.personEmailMessage}</span> : null
                        }
                      </Form.Group>
                    </Col>
                    
                    <Col md="3">
                        <Form.Group controlId="Gender">
                          <Form.Label>Gender</Form.Label><sup style={{color:'red'}}>*</sup>
                          <Select
                            options={this.state.gender}
                            value={this.state.genderType}
                            onChange={(e) => {this.handleDropdownData(e, "genderType")}}
                            isSearchable
                            name="genderType"
                            placeholder="Select Gender"
                            className="myclass"
                            noOptionsMessage={({ inputValue }) =>
                              "No results found"
                            }
                            isDisabled={this.state.isEnable}
                            // required
                          />
                        </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="yearOfExperience">
                        <Form.Label>Years of Experience</Form.Label><sup style={{color:'red'}}>*</sup>
                        <div style={{position:'relative'}}>
                        <FormControl
                            name="yearOfExperience"
                            type="text"
                            placeholder="Enter Years of Experience"
                            className="input-field-apperence"
                            maxLength="2"
                            value={this.state.yearOfExperience}
                            onChange={(e) => {this.numberValidation(e.target.value, "yearOfExperience" , "experienceErrorMessage")}}
                            disabled={this.state.isEnable}
                          />
                           { this.state.experienceErrorMessage !== "" && this.state.experienceErrorMessage ? 
                              <span style={{color:"red" , fontSize:"12px"}}>{this.state.experienceErrorMessage}</span> : null
                          }
                        </div>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="skills">
                        <Form.Label>Skills</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.skillData}
                          value={this.state.skill}
                          onChange={(e) => {this.handleDropdownData(e, "skill")}}
                          isSearchable
                          name="skill"
                          placeholder="Select Skills"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isMulti
                          isDisabled={this.state.isEnable}
                          // components={animatedComponents}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="designation">
                        <Form.Label> Designation  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"designation"}
                          options={this.state.designationList}
                          value={this.state.designation}
                          onChange={(e) => {this.handleDropdownData(e, "designation");}}
                          isSearchable
                          isFocused={false}
                          placeholder="Select Designation"
                          isOptionDisabled={(option) => option.label == "a"}
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="employeeId">
                        <Form.Label>Employee ID</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                          type="text"
                          name="employeeId"
                          onChange={(e)=>{this.inputValidation(e.target.value , "employeeId")}}
                          placeholder="Enter Employee ID"
                          disabled={true}
                          value={this.state.employeeId}
                          maxLength="20"
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="employeeType">
                        <Form.Label> Employee Type </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"employeeType"}
                          options={EmployeeType}
                          value={this.state.employeeType}
                          onChange={this.handleEmployeeType}
                          isSearchable
                          isFocused={false}
                          placeholder="Select Employee Type"
                          isOptionDisabled={(option) => option.label == "a"}
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={true}
                          // required
                        />
                        { this.state.employeeTypeErrorMessage !== "" && this.state.employeeTypeErrorMessage ? 
                            <span style={{color:"red" , fontSize:"12px"}}>{this.state.employeeTypeErrorMessage}</span> : null
                        }
                      </Form.Group>
                    </Col>
                    <Col md="3">
                      <Form.Group controlId="department">
                        <Form.Label>Department</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.departmentTempData.length > 0 ? this.state.departmentTempData :this.state.departmentData}
                          value={this.state.edataDepartment}
                          onChange={(e) => {this.handleDropdownData(e, "edataDepartment")}}
                          isSearchable
                          name="department"
                          placeholder="Select Department"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // isDisabled={this.state.isDepartmentDisable}
                          // isMulti
                          // components={animatedComponents}
                          // required
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  {/* --------------------------------- TML company Details ------------------------------ */}
                  <div className="form-section">
                    <span>Reporting Manager And Company Details</span>
                  </div>

                  <Row className="form-grid">  

                    <Col md="3">
                        <Form.Group controlId="manager">
                          <Form.Label> Reporting Manager&apos;s Email </Form.Label><sup style={{color:'red'}}>*</sup>
                          <InputGroup className={this.state.sameManagerField ?"error-input mb-3 flex-direction" : "mb-3 flex-direction"}>
                            <InputGroup className="manager-loader">
                            <FormControl
                              type="text"
                              name="manager"
                              placeholder="Enter TML Email"
                              aria-label="Recipient's username"
                              aria-describedby="basic-addon2"
                              onChange={this.onChangeHandler}
                              value={this.state.manager}
                              onBlur={this.managerDetails}
                              style={{width:'100%'}}
                              disabled={this.state.isEnable || this.state.managerLoders}
                            />
                            { this.state.managerLoder ? 
                              <div style={{paddingLeft:'5px'}}>
                                <Spinner size="sm" animation="border" role="status" variant="primary"/>
                              </div> : null
                            }
                            </InputGroup>
                          { this.state.CompleteName ? 
                              <span style={{color:"green" , fontSize:"15px" , position:"absolute" , top:'2.3rem'}}>{this.state.CompleteName}</span> : 
                              this.state.managerErrorMessage ? 
                              <span style={{color:"red" , fontSize:"15px" , position:"absolute" , top:'2.3rem'}}>{this.state.managerErrorMessage}</span> : null
                          }
                          </InputGroup>
                        </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="city To">
                        <Form.Label> City  </Form.Label>{this.state.employeeType?.value === 'NONIT' ? null : <sup style={{color:'red'}}>*</sup> }
                        <Form.Control
                        type="text"
                        name="city"
                        disabled={this.state.isEnable ||  this.state.employeeType?.value === "IT"}
                        value={this.state.city}
                        placeholder=" Enter City"
                        onChange={(e)=>{this.alphabetValidations(e.target.value , "city")}}
                      />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="Department No">
                        <Form.Label> Department  </Form.Label>{this.state.employeeType?.value === 'NONIT' ? null : <sup style={{color:'red'}}>*</sup> }
                        <Form.Control
                        type="text"
                        name="department"
                        disabled={this.state.isEnable || this.state.employeeType?.value === "IT"}
                        value={this.state.department}
                        placeholder=" Enter Department"
                        onChange={(e)=>{this.alphabetValidations(e.target.value , "department")}}
                      />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="tmlRegion">
                        <Form.Label> TML Region  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"tmlRegion"}
                          options={this.state.tmlRegionData}
                          value={this.state.tmlRegion}
                          onChange={(e) => {this.handleDropdownData(e, "tmlRegion");}}
                          isSearchable
                          isFocused={false}
                          name="tmlRegion"
                          placeholder="Select TML Region"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="tmlLocation">
                        <Form.Label> TML Location  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"tmlLocation"}
                          options={this.state.tmlLocationData}
                          value={this.state.tmlLocation}
                          onChange={(e) => {this.handleDropdownData(e, "tmlLocation");}}
                          isSearchable
                          isFocused={false}
                          name="tmlLocation"
                          placeholder="Select TML Location"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="buCompanyCode">
                        <Form.Label> BU Company Code </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"buCompanyCode"}
                          options={this.state.buCompanyCodeList}
                          value={this.state.buCompanyCode}
                          isDisabled={this.state.isBuDisabled}
                          onChange={(e) => {this.handleDropdownData(e, "buCompanyCode");}}
                          isSearchable
                          isFocused={false}
                          name="buCompanyCode"
                          placeholder="Select BU Company Code"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="swipeLocation">
                        <Form.Label> Swipe Location  </Form.Label>
                        <Select
                          instanceId={"swipeLocation"}
                          options={this.state.swipeLocationOptions}
                          value={this.state.swipeLocation}
                          onChange={(e) => {this.handleDropdownData(e, "swipeLocation");}}
                          isSearchable
                          isFocused={false}
                          name="swipeLocation"
                          placeholder="Select Swipe Location"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>
                    <Col md="3">
                      <Form.Group controlId="l2ManagerEmail">
                          <Form.Label> L2 Manager Email </Form.Label><sup style={{color:'red'}}>*</sup>
                          <Form.Control
                          type="email"
                          name="l2ManagerEmail"
                          value={this.state.l2ManagerEmail}
                          onChange={(e) => this.setState({ l2ManagerEmail: e.target.value })}
                          onBlur={this.state.source === "AD" ? this.fetchL2ManagerDetails : undefined}
                          disabled={!this.state.l2FieldsEditable}
                          placeholder="Enter L2 Manager Email"
                        />

                        </Form.Group>
                    </Col> 
                    <Col md="3">
                      <Form.Group controlId="l2ManagerName">
                        <Form.Label> L2 Manager Name </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                        type="text"
                        name="l2ManagerName"
                        value={this.state.l2ManagerName}
                        onChange={(e) => this.setState({ l2ManagerName: e.target.value })}
                        disabled={!this.state.l2FieldsEditable ? true : false}
                        placeholder="Enter L2 Manager Name"
                        />
                         </Form.Group>
                    </Col>

                  </Row>

                  {/* ------------------------------------ Vendor Details ---------------------------------------- */}
                  <div className="form-section">
                    <span>Vendor Details</span>
                  </div>

                  <Row className="form-grid">

                  <Col md="3">
                      <Form.Group controlId="projectName">
                        <Form.Label>Project Name</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.project}
                          value={this.state.projectName}
                          // onChange={(e) => {this.handleDropdownData(e, "projectName")}}
                          onChange={(e)=>{this.handleProjectName(e)}}
                          isSearchable
                          name="projectName"
                          placeholder="Select Project Name"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="SIO">
                        <Form.Label> SIO Code / BC Field</Form.Label>
                        <Form.Control
                        type="text"
                        name="sioCode"
                        value={this.state.sioCode}
                        onChange={(e)=>{this.inputValidation(e.target.value , "sioCode")}}
                        placeholder=" Enter SIO Code / BC Field"
                        disabled={this.state.isSioDisable}
                      />
                      </Form.Group>
                    </Col>

                    {
                      this.props.vendor === 'TCS' || this.state.isTowerShown ? 
                      <Col md="3">
                      <Form.Group controlId="towerName">
                        <Form.Label>Tower Name</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.towerData}
                          value={this.state.towerName}
                          onChange={(e) => {this.handleDropdownData(e, "towerName")}}
                          isSearchable
                          name="towerName"
                          placeholder="Select Tower Name"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col> : null
                    
                    }
                    
                  
                    <Col md="3">
                      <Form.Group controlId="vendorManager">
                        <Form.Label>Vendor Manager&apos;s Name</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                          type="text"
                          name="vendorManager"
                          onChange={(e)=>{this.alphabetValidations(e.target.value , "vendorManager")}}
                          placeholder="Enter Vendor Manager Name"
                          disabled={this.state.isEnable}
                          value={this.state.vendorManager}
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="vendorManagerEmail">
                        <Form.Label>Vendor Manager&apos;s Email</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                          type="email"
                          name="vendorManagerEmail"
                          value={this.state.showMaskedContent ? this.state.vendorManagerEmail : this.maskEmail(this.state.vendorManagerEmail)}
                          onChange={(e) => {this.onChangeEmailHandler(e.target.value , this.state.vendorEmailMessage , "vendorEmailMessage" , 'vendorManagerEmail')}}
                          onBlur={()=>{this.emailValidations(this.state.vendorManagerEmail , "vendorEmailMessage" , "vendorManagerEmail")}}
                          placeholder="Enter Vendor Manager Email"
                          disabled={this.state.isEnable}
                          // required
                        />
                          { this.state.vendorEmailMessage !== "" && this.state.vendorEmailMessage ? 
                            <span style={{color:"red" , fontSize:"12px"}}>{this.state.vendorEmailMessage}</span> : null
                          }
                      </Form.Group>
                    </Col>

                  </Row> 

                  {/* ----------------------------------- Account Type --------------------------------- */}
                  <div className="form-section">
                    <span>Account Type</span>
                  </div>

                  <Row className="form-grid">   
                  <Col md="3">
                      <Form.Group controlId="validityFrom">
                        <Form.Label> Valid From  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <DatePicker
                          value={this.state.validityFrom}
                          onChange={(e) => {this.handleDate(e, "validityFrom")}}
                          name="validityFrom"
                          dateFormat="yyyy-MM-dd"
                          placeholderText="Select Valid From"
                          className="form-control display-block"
                          disabled={true}
                          minDate={this.state.validityTo ? this.state.validityTo.subtract(ExpiryMonthRange , 'month') : null}
                          style={{fontFamily : 'sans-serif'}}  
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="validityTo">
                        <Form.Label> Valid till </Form.Label><sup style={{color:'red'}}>*</sup>&nbsp;&nbsp;&nbsp;<span style={{color:"red" , fontSize:"14px"}}>Please use the Domain ID Extension Tab to update Employee Valid Till date</span>
                        <DatePicker
                          value={this.state.validityTo}
                          onChange={(e) => {this.handleDate(e, "validityTo")}}
                          name="validityTo"
                          dateFormat="yyyy-MM-dd"
                          placeholderText="Select Valid To" 
                          className="form-control"
                          showMonthDropdown
                          minDate={dayjs()}
                          maxDate={dayjs().add(ExpiryMonthRange,'month')}
                          disabled={true}
                          style={{fontFamily : 'sans-serif'}}
                        />
                        
                      </Form.Group>
                    </Col>

                    <Col md="6">
                      <Form.Group controlId="accountType">
                        <Form.Label>Account Type</Form.Label>
                        {["checkbox"].map((type) => (
                          <div key={`inline-${type}`} className="">
                            <Form.Check
                              type={type}
                              label="Domain ID"
                              id="Domain"
                              name="accountType"
                              defaultChecked={this.state.domainChecked}
                              inline
                              value="Domain"
                              onChange={this.handleCheck}
                              disabled
                            />
                            <Form.Check
                              inline
                              type={type}
                              label="Email Account"
                              name="emailType"
                              id="emailAccess"
                              value="emailCheck"
                              onChange={this.handleEmailCheck}
                              disabled={this.state.isEmailDisable}
                              checked={this.state.emailCheck}
                            />
                            <Form.Check
                              inline  
                              type={type}
                              label="Internet"
                              id="Internet"
                              name="InternetType"
                              value="internetCheck"
                              onChange={this.handleInternetCheck}
                              disabled={this.state.isEnable}
                              checked={this.state.internetCheck}
                            />
                          </div>
                        ))}
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row className="form-grid">
                    <Col md="12">
                      <Form.Group className="text-center" controlId="Submitbtn">
                        <Button disabled={this.state.isUpdateEnable} className="primary-button" onClick={()=>{this.updateUser()}}>Update</Button>
                        <Button  className="reset-button" onClick={this.resetForm}>Reset</Button>
                      </Form.Group>
                    </Col>
                  </Row>
                </div>
              </div>
            </Form>
            {/* </CardBox> */}
          </div>
        </div>
      </div>

      {
        this.state.isLoading ?
        <div className="loader">
          <div></div>
        </div> : null
      }
      </>
    );
  }
}
const mapStateToProps = (state) => ({
  counter: state.counter.value,
  baseUrl: state.counter.baseUrl,
  maxDay: state.counter.maxDay,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
  vendor : state.loginInfo.login.vendorName,
  isTmlEmployee : state.loginInfo.login.isTmlEmployee,
  requestType : state.counter.requestType
});

const mapDispatchToProps = {
  incrementCounter: incrementCounter,
  decrementCounter: decrementCounter,
  step1: step1,
  login:login
};
export default withRouter(connect(mapStateToProps, mapDispatchToProps)(CreateUpdateRequest));
