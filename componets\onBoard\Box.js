import React, { Component } from 'react'
import { connect } from 'react-redux'; 
import { setId,dashboardBoxCount,setRequestType } from '../../redux/actions/counterActions';
import { getAPIResponse } from "../../constants/Utils";
import * as config from '../../constants/config'
import {admin_dashboard_count,admin_dashboard,admin_action,admin_modules} from '../../constants/constants'
import Image from "next/image";
import pending from '../../public/img/employee_pending.svg'
import send_invite from '../../public/img/send_invite.svg'
import manager_pending from '../../public/img/manager-pending.svg'
import disable_user from '../../public/img/user-slash-solid.svg'
import delete_user from '../../public/img/user-minus-solid.svg'
 class Box extends Component {
    static getInitialProps({ store }) { }

    constructor(props) {
        super(props);
        this.state = {
            activeKey: "Indents",
            countData:{},
            activeCard:""
        }
    }

    componentDidMount() {
        this.getSpocDashboardCount()
    }

    getSpocDashboardCount = () => {
        let api_url = config.getSpocDashboardCount
        if (localStorage.getItem("role") == 'isSuperUser'){
            let queryParams = new URLSearchParams({
                admin_action: admin_dashboard_count,
                admin_dashboard_count: admin_dashboard
            })
            api_url = `${config.getAdminDashboardCount}?${queryParams.toString()}`
        }
        getAPIResponse(api_url,"GET",{})
        .then((response)=>{
        if(!response.ok){
            console.log("getManagerDashboardCount() in ManagerBox",response)
            return null
        }
        else {
            return response.json()
        }
        })
        .then((data)=>{
        if(data !== null){
            if(data.status === 1){
            this.props.dashboardBoxCount({
                countData: data.data,
            });
            }
            else {
            console.log("getManagerDashboardCount() in ManagerBox",data)
            }
        }
        else {
            console.log("getManagerDashboardCount() in ManagerBox",data)
        }
        })
        .catch((error)=>{
        console.error("getManagerDashboardCount() in ManagerBox",error)
        })
    }

    cardStatusChange = (card) => {
        this.setState({
            activeCard : card
        },()=>{
            this.props.setRequestType ({
                requestType:card
            })
        })
    }

    render() {
        return (
        <div className='request-container'>
            <div className={this.props.requestType === "Create" ? 'create-container card-active':'create-container'} onClick={()=>{this.cardStatusChange("Create")}}>
                <div className='total-request'>
                    <div className='1'>Create Request</div>
                    <div className='request-count'>
                        <div className='outline-count'>
                            <div className='count'>{this.props.countData.total_create_request}</div>
                        </div>
                        <div>Total Create Request</div>
                    </div>
                </div>
                <div className='sub-count'>
                    <div className='subcount-container'>
                        <div className='subcount-img'>
                            <Image
                            src={send_invite}
                            alt='send_invite'
                            width={30}
                            height={30}
                            />
                        </div>
                        <div className='subcount-total'>
                            <div className='sub-title'>Invite Sent</div>
                            <div>{this.props.countData.invite}</div>
                        </div>
                    </div>
                    <div className='subcount-container'>
                        <div className='subcount-img'>
                            <Image
                            src={pending}
                            alt='user_pending'
                            width={30}
                            height={30}
                            />
                        </div>
                        <div className='subcount-total'>
                            <div className='sub-title'>Employee Approval Pending</div>
                            <div>{this.props.countData.employee_pending}</div>
                        </div>
                    </div>
                    <div className='subcount-container'>
                        <div className='subcount-img'>
                            <Image
                            src={manager_pending}
                            alt='manager_pending'
                            width={30}
                            height={30}
                            />
                        </div>
                        <div className='subcount-total' >
                            <div className='sub-title'>Manager Approval Pending</div>
                            <div>{this.props.countData.manager_pending}</div>
                        </div>
                    </div>
                    
                   
                </div>
            </div>
            <div className={this.props.requestType === "Update" ? 'update-container card-active':'update-container'} onClick={()=>{this.cardStatusChange("Update")}}>
            <div className='total-request'>
                    <div className='1'>Update Request</div>
                </div>
                <div className='sub-count'>
                    <div className='subcount-container'>
                        <div className='subcount-img'>
                            <Image
                            src={manager_pending}
                            alt='manager_pending'
                            width={30}
                            height={30}
                            />
                        </div>
                        <div className='subcount-total' >
                            <div className='sub-title'>Manager Approval Pending</div>
                            <div>{this.props.countData.update_request_manager_pending}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div className={this.props.requestType === "Disable" ? 'update-container card-active':'update-container'} onClick={()=>{this.cardStatusChange("Disable")}}>
            <div className='total-request'>
                    <div className='1'>Disable Request</div>
                </div>
                <div className='sub-count'>
                    <div className='subcount-container'>
                        <div className='subcount-img'>
                            <Image
                            src={disable_user}
                            alt='manager_pending'
                            width={30}
                            height={25}
                            />
                        </div>
                        <div className='subcount-total' >
                            <div className='sub-title'>Total Disable Request</div>
                            <div>{this.props.countData.total_disable_request}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        )
    }
}
const mapStateToProps = state => ({
    requestId: state.counter.requestId,
    countData: state.counter.countData,
    requestType : state.counter.requestType
});

const mapDispatchToProps = {
    setId: setId,
    dashboardBoxCount: dashboardBoxCount,
    setRequestType:setRequestType
};
export default connect(mapStateToProps, mapDispatchToProps)(Box);