import React, { Component } from "react";
import {Tabs,Tab,Col,Row} from "react-bootstrap";
import { connect } from "react-redux";
import {setId} from "../../redux/actions/counterActions";
import TmlAprrovalTable from "./TmlAprrovalTable";
import AllRejected from "./AllRejected";
import IdTable from './IdTable'
import {toast } from "react-toastify";
import ManagerBox from "./ManagerBox";
import BulkAprrovalTable from "./BulkAprrovalTable";

class ApprovedRequestManager extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      activeKey: "Indents",
    };
  }

  handleShow = (e) => {
    this.setState({
      activeKey: e,
    });
    toast.dismiss();
  };

  renderTabContent() {
    const { activeKey } = this.state;
    console.log(activeKey)
    switch (activeKey) {
      case 'Indents':
        return <TmlAprrovalTable baseUrl={this.props.baseUrl} loginData={this.props.loginData}/>
      case 'Bulk':
        return <BulkAprrovalTable baseUrl={this.props.baseUrl} loginData={this.props.loginData}/>
      case 'Aproved':
        return <IdTable baseUrl={this.props.baseUrl} loginData={this.props.loginData}/>
      case 'Rejected':
        return <AllRejected baseUrl={this.props.baseUrl} loginData={this.props.loginData}/>
      default:
        return null;
    }
  }
  render() {
    const userRole = this.props.loginData.userRole;
    const empId = this.props.loginData.empId;
      return (
        <div>
          <ManagerBox ticketId={empId} baseUrl={this.props.baseUrl} loginData={this.props.loginData}/>
          <div className="table-container">
            <Row>
              <Col md="12">
                <Tabs
                  id="controlled-tab-example"
                  activeKey={this.state.activeKey}
                  onSelect={this.handleShow}
                  className="mb-3"
                >
                  <Tab
                    style={{ color: "#495057"}}
                    eventKey="Indents"
                    title="Pending Request"
                    tabClassName={this.state.activeKey == "Indents"? "tabActive" : "tabInactive"}
                  >
                    {/* <TmlAprrovalTable baseUrl={this.props.baseUrl} loginData={this.props.loginData}/> */}
                  </Tab>
                  {/* <Tab
                    style={{ color: "#495057"}}
                    eventKey="Bulk"
                    title="Pending Bulk Request"
                    tabClassName={this.state.activeKey == "Bulk"? "tabActive" : "tabInactive"}
                  >
                  </Tab> */}
                  <Tab
                    style={{ color: "#495057"}}
                    eventKey="Aproved"
                    title="Approved Request"
                    tabClassName={this.state.activeKey == "DraftIndents"? "tabActive":"tabInactive"}
                  >
                    {/* <IdTable baseUrl={this.props.baseUrl} loginData={this.props.loginData}/> */}
                  </Tab>
                  <Tab
                    style={{ color: "#495057"}}
                    eventKey="Rejected"
                    title="Rejected Request"
                    tabClassName={this.state.activeKey == "DraftIndents"? "tabActive":"tabInactive"}
                  >
                    {/* <AllRejected baseUrl={this.props.baseUrl} loginData={this.props.loginData}/> */}
                  </Tab>
                </Tabs>
                {this.renderTabContent()}
              </Col>
            </Row>
          </div>
         
        </div>
      )
    } 
  }

const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(ApprovedRequestManager);
