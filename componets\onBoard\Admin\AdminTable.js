import { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { Tabs, Tab, ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form } from 'react-bootstrap';
// import { login, logOut } from "../../../redux/actions/loginActions";
import { login, logOut } from "../../../redux/actions/loginActions";
// import * as constants from '../../../constants/constants'
import BootstrapTable from 'react-bootstrap-table-next';
import { ReactNotifications, Store } from "react-notifications-component";
import Select from "react-select";
import { MasterType, RequestType, sizePerPage } from "../../../constants/constants";
import paginationFactory from 'react-bootstrap-table2-paginator';
import ToolkitProvider, { Search } from "react-bootstrap-table2-toolkit";
import { getAPIResponse } from '../../../constants/Utils';
import * as config from '../../../constants/config'
import { FaRegEdit } from "react-icons/fa";
import { Switch } from 'antd';
const { SearchBar } = Search;

// import { pageAccessWithRole } from '../../constants/navigations';


class MasterTable extends Component {

  static getInitialProps({ store }) { }
  constructor(props) {
    super(props);
    this.state = {
      nonTextFields : ['isactive','action']
    }
  }

  componentDidMount() {}
  
  render() {
    let { headers, currentPageData, noDataMessage, handledToggleActiveEmployee, handledRemoveEmployee,isActiveDisable} = this.props
    return (
      <Fragment>
        <ReactNotifications />
        <div className='table-fix-height table-overflow'>
          <table className='table table-bordered fixed-header-table'>
            <thead className='table-header'>
              <tr className="label-text">
                {headers.map((listData, index) => {
                  return (
                    <th key={index}>
                      <div className="thead">{listData.text}</div>
                    </th>)
                })}
              </tr>
            </thead>
            <tbody>
              {currentPageData.length == 0 ?
                <td className='table-secondary table-striped' colSpan={headers.length}>
                  <text>{ noDataMessage ?? 'No Records Found' }</text>
                </td>
              :
              currentPageData.map((listData, index) => {
                return (
                  <tr className={index % 2 !== 0 ? 'table-secondary table-striped ' : ''} key={index}>
                  {/* <tr className={index % 2 !== 0 ? 'table-dark table-striped' : ''} key={index}> */}
                    {headers.map((data, index) => {
                      if (!(this.state.nonTextFields.includes(data.dataField.toLowerCase()))){
                        return (
                          <td key={index}>
                            <div className="thead">{listData[data.dataField]}</div>
                          </td>
                        )
                      }
                      else{
                        if (data.dataField.toLowerCase() == 'isactive') {
                          return (
                            <td key={index}>
                              {handledToggleActiveEmployee ? <Switch defaultChecked={listData[data.dataField]} onChange={()=>{handledToggleActiveEmployee(listData.email)}} disabled={isActiveDisable?isActiveDisable:false}/> : <div className="thead">{listData[data.dataField]}</div>}
                            </td>
                          )
                        }
                        if (data.dataField.toLowerCase() == 'action'){
                          return (
                            <td key={index}>
                              {handledRemoveEmployee ? 
                              <a style={{ marginRight: "5px", cursor: "pointer", padding:'8px 8px' }}
                              title ="Delete Record" className="btn-danger btn-xs"
                              onClick={()=> {handledRemoveEmployee(listData.email)}}><i className="fas fa-trash-alt m-1"></i></a>
                              : <div className="thead">{listData[data.dataField]}</div>}
                            </td>
                          )
                        }
                      }
                    })}
                  </tr>
                )
              })}

            </tbody>
          </table>
        </div>
      </Fragment>
    )
  }
}


const mapStateToProps = state => ({
  counter: state.counter.value,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login
});

const mapDispatchToProps = {
  login: login
};

export default connect(mapStateToProps, mapDispatchToProps)(MasterTable);
