import React, { Component, Fragment } from "react";
import DatePicker from "react-datepicker";
import FileBase64 from "react-file-base64";
import "react-datepicker/dist/react-datepicker.css";
import moment from "moment";

class Datecomonent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      startDate: "",
      files: [],
    };
    this.handleDateChange = this.handleDateChange.bind(this);
    this.handleDateSelect = this.handleDateSelect.bind(this);
  }

  componentDidMount() {
    var startDate = new Date();
    var endDateMoment = moment(startDate).add(6, "months");
    var da = endDateMoment.add(1, "months");
  }

  handleDateChange(date) {
    alert(date);
    this.setState({
      startDate: date,
    });
  }

  handleDateSelect(date) {
    console.log(date, "is finale ");
  }

  getFiles(files) {
    this.setState({ files: files });
    console.log(this.state.files);
  }
  
  render() {
    return (
      <div>
        <DatePicker
          selected={moment("04-08-2022").toDate()}
          onChange={this.handleDateChange}
          name={this.props.name}
          dateFormat="dd/MM/yyyy"
          placeholderText={this.props.placeholderText}
          className="form-control"
          onSelect={this.handleDateSelect}
          minDate={new Date()}
          maxDate={new Date("02-29-2023")}
          peekNextMonth
          showMonthDropdown
          showYearDropdown

        />
      </div>
    );
  }
}

export default Datecomonent;
