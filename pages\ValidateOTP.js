import { Component } from "react";
import { ToastContainer, toast } from "react-toastify";
import Router from "next/router";
import { Spinner, ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form, } from "react-bootstrap";
import { connect } from "react-redux";
import { login, logOut } from "../redux/actions/loginActions";
import { setId } from "../redux/actions/counterActions";
import { validateOtpUrl, validateUrl } from "../constants/config";
import { getAPIResponse } from "../constants/Utils";
import { BASE_URL, HEADERS } from "../constants/constants"
import Image from "next/image";
import logo from '../public/img/Tata-Motors-Logo-Vector.svg'
import { INTERNAL_SERVER_ERROR, LINK_EXPIRED_ERROR_MESSAGE, SOMETHING_WENT_WRONG, VERIFIED_SUCCESSFULLY } from "../constants/message";
import { successToast, errorToast } from '../toast'

class ValidateOTP extends Component {

  static getInitialProps({ store }) { }

  constructor(props) {
    super(props);
    this.state = {
      generatedOTP: "",
      enteredOTP: "",
      isDone: false,
      isPending: true,
      email: "",
      disable:false,
      otpValue:""
    };
  }

  componentDidMount() {
    this.validateURL()
  }

   validateURL = async () => {
    const param = Router.query.Link;
    getAPIResponse(`${validateUrl}${param}`, 'GET' , {})
      .then((response) => {
        if (response.status === 500) {
          console.log("validateURL() in validateOIP",response)
          errorToast(INTERNAL_SERVER_ERROR)
          this.setState({
            isDone: true,
            isPending: false,
          });
          return null
        }
        else {
          return response.json()
        }
      })
      .then((response) => {
        if(response !== null && response.status === 1){
          this.setState({
            isDone: false,
            isPending: true,
          });
        }
        else {
          errorToast(response.message)
          this.setState({
            isDone: true,
            isPending: false,
          });
        }
      })
      .catch((error) => {
        console.log(error)
        errorToast(SOMETHING_WENT_WRONG)
        this.setState({
          isDone: true,
          isPending: false,
        });
      });
  }

  handleSubmit = (event) => {
    event.preventDefault();
    this.setState({
      disable:true
    })
    let param = Router.query.Link;
      let body = {
        email: this.state.email,
        otp: this.state.otpValue
      }
      getAPIResponse(validateOtpUrl, 'POST', body)
      .then((response) => {
        if (response.status === 500) {
          errorToast(SOMETHING_WENT_WRONG)
          console.log("handleSubmit() in validateOTP",response)
          this.setState({
            isDone: false,
            isPending: true,
            disable:false
          });
          return null
        } else {
          return response.json()
        }
      })
      .then((response) => {
        if(response !== null){
          if(response.status === 1){
            successToast(VERIFIED_SUCCESSFULLY)
            this.props.setId({ requestId: response.data.id })
            this.props.login({
              personName: `${response.data.ndaEmployeeDetails.firstName} ${response.data.ndaEmployeeDetails.lastName}`,
              personMobile: response.data.ndaEmployeeDetails.mobile,
              personEmail: response.data.ndaEmployeeDetails.email,
              vendor: response.data.ndaEmployeeDetails.vendor.vendorCode,
              link:param,
              otp:response.data.otp
            })
            this.goNext()
          }
          else {
            this.setState({
              isDone: false,
              isPending: true,
              disable:false
            })
            errorToast(response.message)
          }
        }
      })  
  };

  otpValidation = (otp) => {
    let pattern = /^\d+$/;
    if(otp!== "" && pattern.test(otp)){
      this.setState({
        otpValue:otp
      })
    }
    else {
      this.setState({
        otpValue:""
      })
    }
  }

  goNext = () => {
    Router.push({
      pathname: "/Employee",
    });
  };

  render() {
    return (
      <div className="hold-transition login-page">
        <Row>
          <Col md="12">
            <form onSubmit={(e) => this.handleSubmit(e)}>
              <div className="login-box text-primary">
                <div className="display-center">
                </div>
                {
                  this.state.isPending && (
                    <div className="card">
                      <div className="card-body login-card-body">
                        <div className="display-center">
                          <Image
                            src={logo}
                            alt="tata-logo"
                            width={250}
                            height={100}  
                          />
                        </div>
                        <div className="margin-top">
                          <div className="input-group mb-3">
                            <input
                              onChange={(e) => this.setState({ email: e.target.value })}
                              required
                              type="email"
                              name="email"
                              className="form-control"
                              placeholder=" Enter your Email"
                            />
                          </div>
                          <div className="input-group mb-3">
                            <input
                              onChange={(e)=>this.otpValidation(e.target.value)}
                              value={this.state.otpValue}
                              required
                              type="text"
                              name="otp"
                              className="form-control"
                              placeholder=" Enter your OTP"
                            />
                          </div> 
                        </div>
                        <div className="row" style={{justifyContent:'center'}}>
                          <div style={{width:'100%',marginTop:'1rem'}}>
                            <button  disabled={this.state.disable} type="submit" className="btn btn-primary btn-block">Verify</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                }
                {
                  this.state.isDone && (
                    <div className="card card-primary card-outline">
                      <div className="card-body">
                        <h5 className="text-danger">
                          {LINK_EXPIRED_ERROR_MESSAGE}
                        </h5>
                      </div>
                    </div>
                  )
                }
              </div>
            </form>
          </Col>
        </Row>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  isLogin: state.loginInfo.isLogin,
  baseUrl: state.counter.baseUrl,
});

const mapDispatchToProps = {
  login: login,
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(ValidateOTP);
