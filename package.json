{"name": "myapppp", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-brands-svg-icons": "^6.5.1", "@fortawesome/free-regular-svg-icons": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@reduxjs/toolkit": "^2.0.1", "antd": "^5.15.4", "axios": "^1.6.8", "babel-runtime": "^6.26.0", "base-64": "^1.0.0", "bootstrap": "^5.3.3", "date-fns": "^3.6.0", "dayjs": "^1.11.10", "file-base64": "^1.0.0", "formik": "^2.4.5", "fuse.js": "^7.0.0", "js-file-download": "^0.4.12", "jwt-decode": "^4.0.0", "luxon": "^3.4.4", "mgr-pdf-viewer-react": "^1.0.3", "moment": "^2.30.1", "next": "^14.1.3", "next-redux-wrapper": "^8.1.0", "papaparse": "^5.4.1", "react": "^18.2.0", "react-bootstrap": "^2.10.2", "react-bootstrap-table-next": "^4.0.3", "react-bootstrap-table2-paginator": "^2.1.2", "react-bootstrap-table2-toolkit": "^2.1.3", "react-confirm-alert": "^3.0.6", "react-datepicker": "^6.4.0", "react-dom": "^18.2.0", "react-file-base64": "^1.0.3", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-notifications": "^1.7.4", "react-notifications-component": "^4.0.1", "react-redux": "^9.1.0", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-select-search": "^4.1.7", "react-simple-captcha": "^9.3.1", "react-toastify": "^10.0.5", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "web-vitals": "^2.1.4"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.0.0", "@types/react-router-dom": "^5.3.3", "eslint": "8.8.0", "eslint-config-next": "12.0.10"}}