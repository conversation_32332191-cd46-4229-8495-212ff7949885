import React from 'react';
import { connect } from 'react-redux';
import { decrementCounter, incrementCounter } from '../redux/actions/counterActions';
import { Card, Container, Navbar, Nav, NavDropdown, Form, FormControl, Button } from 'react-bootstrap';
import { login } from '../redux/actions/loginActions';
import * as constants from '../constants/constants'

class App extends React.Component {

    static getInitialProps({ store }) { }
    constructor(props) {
        super(props);
    }


    render() {
        const isLoggedIn = this.props.loginData.isLogin;
        const roles=this.props.loginData.userRole
        if (!isLoggedIn) {
                window.location.href = constants.FRONTEND_URL + '/login'
                return null
        } 
        else {
                window.location.href = constants.FRONTEND_URL + '/Dashboard'
                return null

        }
    }
}

const mapStateToProps = state => ({
    counter: state.counter.value,
    isLogin: state.loginInfo.isLogin,
    loginData: state.loginInfo.login
});

const mapDispatchToProps = {
    incrementCounter: incrementCounter,
    decrementCounter: decrementCounter,
    login: login,

};
export default connect(mapStateToProps, mapDispatchToProps)(App);

