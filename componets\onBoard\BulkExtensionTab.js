import { Component } from 'react'
import { connect } from 'react-redux'
import AsyncSelect from "react-select/async"
import { getAPIResponse } from '../../constants/Utils';
import * as config from '../../constants/config';
import { <PERSON><PERSON>, Card, DatePicker, Divider, List, Modal, Tooltip, Upload,Skeleton } from 'antd';
import { VscClearAll } from 'react-icons/vsc';
import dayjs from 'dayjs';
import { Form } from 'react-bootstrap';
import AdminTable from './Admin/AdminTable';
import { FaArrowLeft, FaArrowRight, FaExclamationCircle } from 'react-icons/fa';
import { MdOutlineDownload, MdOutlineUploadFile } from 'react-icons/md';
import Title from 'antd/es/typography/Title';
import fileDownload from 'js-file-download';
import { errorToast, successToast } from '../../toast';
import Papa from 'papaparse';
import { INTERNAL_SERVER_ERROR, MANAGER_DETAILS_NOT_FOUND, SOMETHING_WENT_WRONG } from '../../constants/message';
import dynamic from 'next/dynamic';
import { admin_master, admin_master_list, bulkUpdateCSVcontent } from '../../constants/constants';

const Table = dynamic(() => import('antd').then(mod => mod.Table), { ssr: false });

const itemsPerPage = 10;
const alphabetSpaceRegex = /^[A-Za-z\s]+$/;
const emailRegex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/i;
const phoneRegex = /^(\d{10}|NA)$/;
const alphaNumbericRegex = /^[A-Za-z0-9]+$/;
const numbericRegex = /^[0-9]+$/;
const genderRegex = /^(male|female)$/i;
const empTypeRegex = /^(NONIT|IT)$/i;
const isActiveRegex = /^(0|1)$/i;

export class BulkExtensionTab extends Component {
    constructor(props) {
        super(props);
        this.state = {
            employeeData: [],
            smartSearhText: "",
            employeeObjects: {},
            selectedEmployee: "",
            validTill: "",
            defaultHeaders: [
                { dataField: "id", text: "Sr. No.", sort: false },
                { dataField: "firstName", text: "First Name", sort: false },
                { dataField: "middleName", text: "Middle Name", sort: false },
                { dataField: "lastName", text: "Last Name", sort: false },
                { dataField: "fullName", text: "Person Name", sort: false },
                { dataField: "domainId", text: "Domain ID", sort: false },
                { dataField: "email", text: "Email ID", sort: false },
                { dataField: "personalMail", text: "Personal Email", sort: false },
                { dataField: "mobile", text: "Mobile", sort: false },
                { dataField: "yearsOfExperience", text: "Years of Exprience", sort: false },
                { dataField: "gender", text: "Gender", sort: false },
                { dataField: "displaySkill", text: "Skills", sort: false },
                { dataField: "designation", text: "Designation", sort: false },
                { dataField: "tmlManager", text: "Reporting Manager's Email", sort: false },
                { dataField: "tmlRegion", text: "TML Region", sort: false },
                { dataField: "tmlOffice", text: "TML Location", sort: false },
                { dataField: "project", text: "Project Name", sort: false },
                { dataField: "vendorManagerName", text: "Vendor Manager's Name", sort: false },
                { dataField: "vendorManagerEmail", text: "Vendor Manager's Email", sort: false },
                { dataField: "employeeType", text: "Employee Type", sort: false },
                { dataField: "department", text: "Department", sort: false },
                { dataField: "sioCode", text: "SIO Code / BC Field", sort: false },
                { dataField: "validTill", text: "Extend Validity To", sort: false },
                { dataField: "isactive", text: "Active", sort: false },
                { dataField: "action", text: "Action", sort: false }
            ],
            employeeFound: [
                { title: 'Person Name', dataIndex: 'fullName', key: 'fullName' },
                { title: 'Domain ID', dataIndex: 'domainId', key: 'domainId' },
                { title: 'Email ID', dataIndex: 'email', key: 'email' },
            ],
            employeeNotFound: [
                { title: 'Email ID', dataIndex: 'email', key: 'email' },
                { title: 'Status', dataIndex: 'status', key: 'status' },
            ],
            employeeIssue: [
                { title: 'Person Name', dataIndex: 'fullName', key: 'fullName' },
                { title: 'Domain ID', dataIndex: 'domainId', key: 'domainId' },
                { title: 'Email ID', dataIndex: 'email', key: 'email' },
                { title: 'Reason', dataIndex: 'reason', key: 'reason' },
            ],
            currentPage: 1,
            isModelOpen: false,
            designationList : [],
            skillData : [], 
            project: [],
            projectName: "",
            tmlLocationData:[],
            tmlRegionData :[],
            departmentTempData:[],
            departmentData : [], 
            edataDepartment:[],
            vendor:[],
            isLoading: false
        }
    }

    componentDidMount() {
        let role = localStorage.getItem("role")

        if((role === 'isSuperUser')){
            this.getProjectMasterList()
        }else{
            this.getProjectList(this.props.vendor)
            if(this.props.vendor === "TCS") this.getTower()
        }
        this.getVendorList()
        this.getDesignationList()
        this.getTmlLocation()
        this.getTmlRegion()
        this.getSkill()
        this.getDepartment()
    }

    getProjectMasterList = (req_body={}) => {
        this.setState({
          isLoading : true
        })
        let body = {
          "master": 'project',
          "action": admin_master_list,
          "module": admin_master 
        }
        if (Object.keys(req_body).length > 0) body = req_body
        getAPIResponse(config.adminMasterList, "POST", body)
          .then((response) => {
            if (!response.ok) {
              return null
            }
            else {
              return response.json()
            }
          }).then((data) => {
            if (data !== null) {
                data.data.map((obj, index) => {
                    this.setState(prevState => ({
                        project: [...prevState.project, { value: obj.projectCode, label: obj.projectName }]
                    }))
                })
            }
            else{

            }
        })
        this.setState({
            isLoading : false
          })
    }

    getVendorList = () => {
        getAPIResponse(config.getVendorList , "GET" , {})
        .then((response) => {
          if(!response.ok){
            return null
          }
          else{
            return response.json()
          }
        }).then((data) => {
          if(data !== null){
            data.data.map((obj, index) => {
                this.setState(prevState => ({
                    vendor: [...prevState.vendor, { value: obj.vendorCode, label: obj.vendorName }]
                }))
            })
          }
          else {
            console.log("getVendorList() in Request",response)
          }
        })
        .catch((error) => {
          console.log("getVendorList() in Request",error)
        })
    
      }

    getDesignationList = () => {
        getAPIResponse(config.getDesignationList, "GET", {})
            .then((response) => {
                if (!response.ok) {
                    return null
                }
                else {
                    return response.json()
                }
            }).then((data) => {
                if (data !== null) {
                    data.data.map((obj, index) => {
                        this.setState(prevState => ({
                            designationList: [...prevState.designationList, { value: obj.designationCode, label: obj.designationName }]
                        }))
                    })
                }
                else {
                    console.log("getDesignationList() in Request", response)
                }
            })
            .catch((error) => {
                console.log("getDesignationList() in Request", error)
            })
    }

    getProjectList = (vendor) => {
        if (vendor !== null) {
            let queryParams = new URLSearchParams({
                'vendor': vendor
            })
            this.setState({
                project: []
            })
            getAPIResponse(`${config.getProjectList}?${queryParams.toString()}`, "GET", {})
                .then((response) => {
                    if (!response.ok) {
                        return null
                    }
                    else {
                        return response.json()
                    }
                }).then((data) => {
                    if (data !== null) {
                        data.data.map((obj, index) => {
                            this.setState(prevState => ({
                                project: [...prevState.project, { value: obj.projectCode, label: obj.projectName }]
                            }))
                        })
                    }
                    else {
                        console.log("getProjectList() in Request", response)
                    }
                })
                .catch((error) => {
                    console.log("getProjectList() in Request", error)
                })
        }
    }

    getTmlLocation = () => {
        getAPIResponse(config.getTmlOffice, "GET", {})
            .then((response) => {
                if (!response.ok) {
                    console.log("getTmlLocation() in Request", response)
                    return null
                }
                else {
                    return response.json()
                }
            }).then((data) => {
                if (data !== null) {
                    data.data.map((obj, index) => {
                        this.setState(prevState => ({
                            tmlLocationData: [...prevState.tmlLocationData, { value: obj.officeCode, label: obj.officeName }]
                        }))
                    })
                }
                else {
                    console.log("getTmlLocation() in Request", response)
                }
            })
            .catch((error) => {
                console.log("getTmlLocation() in Request", error)
            })
    }

    getTmlRegion = () => {
        getAPIResponse(config.getTmlRegion, "GET", {})
            .then((response) => {
                if (!response.ok) {
                    console.log("getTmlRegion() in Request", response)
                    return null
                }
                else {
                    return response.json()
                }
            }).then((data) => {
                if (data !== null) {
                    data.data.map((obj, index) => {
                        this.setState(prevState => ({
                            tmlRegionData: [...prevState.tmlRegionData, { value: obj.regionCode, label: obj.regionName }]
                        }))
                    })
                }
                else {
                    console.log("getTmlRegion() in Request", response)
                }
            })
            .catch((error) => {
                console.log("getTmlRegion() in Request", error)
            })
    }

    getSkill = () => {
        getAPIResponse(config.getSkillList, "GET", {})
            .then((response) => {
                if (!response.ok) {
                    console.log("getSkill() in Request", response)
                    return null
                }
                else {
                    return response.json()
                }
            }).then((data) => {
                if (data !== null) {
                    data.data.map((obj, index) => {
                        this.setState(prevState => ({
                            skillData: [...prevState.skillData, { value: obj.skillCode, label: obj.skillName }]
                        }))
                    })
                }
                else {
                    console.log("getSkill() in Request", response)
                }
            })
            .catch((error) => {
                console.log("getSkill() in Request", error)
            })
    }

    getTower = () => {
        getAPIResponse(config.getTowerList, "GET", {})
            .then((response) => {
                if (!response.ok) {
                    console.log("getSkill() in Request", response)
                    return null
                }
                else {
                    return response.json()
                }
            }).then((data) => {
                if (data !== null) {
                    data.data.map((obj) => {
                        this.setState(prevState => ({
                            towerData: [...prevState.towerData, { value: obj.towerCode, label: obj.towerName }]
                        }), () => { this.getTowerName() })
                    })

                }
                else {
                    console.log("getTower() in Request", response)
                }
            })
            .catch((error) => {
                console.log("getTower() in Request", error)
            })
    }

    getTowerName = () => {
        let towerName = this.state.towerName
        this.state.towerData.map((obj, index) => {
            if (towerName === obj.value) {
                this.setState({
                    towerName: obj
                })
            }
        })
    }

    getDepartment = () => {
        getAPIResponse(config.getDepartmentList, "GET", {})
            .then((response) => {
                if (!response.ok) {
                    console.log("getDepartment() in Request", response)
                    return null
                }
                else {
                    return response.json()
                }
            }).then((data) => {
                if (data !== null) {
                    data.data.map((obj, index) => {
                        this.setState(prevState => ({
                            departmentData: [...prevState.departmentData, { value: obj.departmentCode, label: obj.departmentName, employeeType: obj.empType }]
                        }))
                    })
                }
                else {
                    console.log("getDepartment() in Request", response)
                }
            })
            .catch((error) => {
                console.log("getDepartment() in Request", error)
            })
    }

    handleFindMaster = (state,value,master='') => {
        let masterData = state.find(obj => obj.label === value);
        // console.log("masterData:",masterData)
        if (masterData){
            return true
        }
        return false                
    }

    handleDepartment = async (value,empType)=>{
        let depData = this.state.departmentData.find(obj => obj.label.toLowerCase() === value.toLowerCase());
        if (depData){
            if (depData['employeeType']===empType){
                return true, depData.label
            }
            return false, null
        }
        return false, null
    }

    handleSkills = (value)=>{
        // Split the input string, trim each item
        const empSkills = value.split(',').map(skill => skill.trim());
        
        // Initialize arrays to store found and missing skills
        const foundSkills = [];
        const missingSkills = [];

        // Check each skill in empSkills
        empSkills.forEach(skill => {
            const validSkill = this.state.skillData.find(item => item.label.trim() === skill);
            if (validSkill) {
                foundSkills.push(validSkill.value.trim());
            } else {
                missingSkills.push(skill);
            }
        });

        // Check if there are any missing skills
        const allEmpSkillsValid = missingSkills.length === 0;

        // Return an object with found, missing skills, and a flag for allEmpSkillsValid
        return { foundSkills, missingSkills, allEmpSkillsValid };
    }

    onClear = () => {
        this.setState({
            smartSearhText: "",
            employeeObjects: {},
            selectedEmployee: "",
            validTill: "",
        })
    }

    getTotalPages = () => {
        const number = Math.ceil(this.state.employeeData.length / itemsPerPage);
        return number === 0 ? 1 : number;
    };

    getCurrentItems = () => {
        const employeeData = [...this.state.employeeData].map((value, index) => {
            return { id: index + 1, ...value }
        });
        const { currentPage } = this.state;
        const response = employeeData.sort((a, b) => b.id - a.id).map((value, index) => {
            return { ...value, id: index + 1 }
        });
        const startIndex = (currentPage - 1) * itemsPerPage;
        return response.slice(startIndex, startIndex + itemsPerPage);
    }

    handlePageChange(pageNumber) {
        this.setState({ currentPage: pageNumber });
    }

    isDisable = () => {
        return (this.state.selectedEmployee === "" || this.state.selectedEmployee == {}) ||
            (this.state.validTill === "" || this.state.validTill == null) ? true : false
    }

    onInputChangeForSmartSearch = (value) => {
        this.setState({
            smartSearhText: value,
        })
    }

    handleEmployeeSearch = (event) => {
        this.setState({
            selectedEmployee: {
                value: event.value, label: this.state.employeeObjects[event.value][0],
                domainId: this.state.employeeObjects[event.value][3],
                email: this.state.employeeObjects[event.value][4]
            }
        })
    }

    customLabel = (value, index) => {
        const employeeName = value.request
        this.setState(prevState => ({
            employeeObjects: {
                ...prevState.employeeObjects,
                [employeeName]: [value.fullName, value.vendor.vendorName, value.vendor.vendorCode, value.domainId, value.email],
            }
        }));

        return (
            <div key={`employee-details-${index}`} className="search-conatiner">
                <b><span key="fullName">{value.fullName}</span></b>
                <br key="br1" />
                <span key="employeeId" className="smartsearch">{value.employeeId}</span>
                <span key="separator2" className="smartsearch"> |  </span>
                <span key="vendor" className="smartsearch">{value.vendor.vendorName}</span>
                <br key="br2" />
                <span key="email" className="smartsearch">{value.email}</span>
            </div>
        )
    }

    smartSearchForEmployee = () => {
        if (this.state.smartSearhText !== "") {
            let searchParams = new URLSearchParams({
                "search_text": this.state.smartSearhText,
                "include_disable_accounts": false
            })
            return getAPIResponse(`${config.getVendorEmployeeDeatils}?${searchParams.toString()}`, 'GET', {})
                .then((response) => {
                    if (response.status === 500) {
                        errorToast(INTERNAL_SERVER_ERROR)
                        return null
                    }
                    else {
                        return response.json()
                    }
                }).then((data) => {
                    if (data !== null && data.status == 1) {
                        let options = []
                        data.data.map((value, index) => {
                            const isExist = this.state.employeeData.find(emp => emp.email === value.email);
                            if (!isExist) {
                                options.push({ value: value.request, label: this.customLabel(value, index), ...value })
                            }
                        })
                        return options
                    }
                })
                .catch((error) => {
                    errorToast(SOMETHING_WENT_WRONG)
                })
        }
    }

    handleDate = (value) => {
        this.setState({
            validTill: value
        })
    }

    handledAddEmployee = () => {
        const validTill = dayjs(this.state.validTill).format("YYYY-MM-DD")
        const dateError = this.validateExtendValidityDate(validTill, false);
        if (dateError) {
            errorToast(dateError);
            return false;
        }
        const employeeData = [...this.state.employeeData];
        employeeData.push({
            fullName: this.state.selectedEmployee.label,
            email: this.state.selectedEmployee.email,
            domainId: this.state.selectedEmployee.domainId,
            validTill: validTill,
            isactive: true
        });
        this.setState({ employeeData }, () => {
            this.onClear();
        })
    }

    handledRemoveEmployee = (email) => {
        const employeeData = [...this.state.employeeData];
        const afterRemoveData = employeeData.filter((emp) => emp.email != email);
        this.setState({ employeeData: afterRemoveData })
    }

    handledToggleActiveEmployee = (email) => {
        const employeeData = [...this.state.employeeData];
        employeeData.map(data => {
            if (data.email === email) {
                data.isactive = !data.isactive
            }
        })
        this.setState({ employeeData })
    }

    handledSubmitRequest = () => {
        if (this.state.employeeData.length > 0) {
            const requestBody = {
                employees: this.state.employeeData,
                action: "Bulk Update - Employees ValidityTo & Status",
                module: "Bulk Update",
                // createdBy:this.props.loginData.empId,
            }
            // const url = localStorage.getItem("role") == 'isSuperUser' ? config.adminBulkEmployeeValidityAndStatusExtension : config.bulkExtensionUpdate
            const url = config.adminBulkEmployeeValidityAndStatusUpdate
            return getAPIResponse(url, 'POST', requestBody)
                .then((response) => {
                    if (response.status === 500) {
                        errorToast(INTERNAL_SERVER_ERROR)
                        console.log("handledSubmitRequest() in bulkExtensionTab",error)
                        return null
                    }
                    else {
                        return response.json()
                    }
                }).then((response) => {
                    if(response != null){
                        if (response.status == 1) {
                            if (response.data.is_request_in_process) {
                                successToast(response.message)
                            }
                            if (response.data.request_already_exist.length > 0) {
                                this.showCsvDataModal([],[],[],response.data.request_already_exist)
                            }
                            this.setState({
                                employeeData: [],
                                smartSearhText: "",
                                employeeObjects: {},
                                selectedEmployee: "",
                                validTill: "",
                                currentPage: 1
                            })
                        }else{
                            if (response.data && response.data.length > 0){
                                successToast(response.message)
                                let csv_data = this.state.employeeData
                                response.data.forEach(data =>{
                                    csv_data[data.index]['reason'] = Object.entries(data.errors).length > 0
                                        ? Object.entries(data.errors)
                                            .map(([key, value]) => `${key}: ${value.join(", ")}`)
                                            .join(", ")
                                        : "Valid entry";

                                })
                                this.showCsvDataModal([],[],[],[],csv_data)
                            }
                            else{
                                errorToast(response.message == null ? SOMETHING_WENT_WRONG : response.message)
                                console.log("handledSubmitRequest() in bulkExtensionTab")
                            }
                        }
                    }
                })
                .catch((error)=>{
                    console.log("handledSubmitRequest() in bulkExtensionTab",error)
                })
        }
    }

    normalizeDate = (dateString) => {
        const regexDDMMYYYY = /^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-(\d{4})$/;
        const regexYYYYMMDD = /^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/;

        let match;

        // Check for dd-mm-yyyy format
        if ((match = dateString.match(regexDDMMYYYY))) {
            const [day, month, year] = match.slice(1);
            return `${year}-${month}-${day}`
        }

        // Check for yyyy-mm-dd format
        if ((match = dateString.match(regexYYYYMMDD))) {
            const [year, month, day] = match.slice(1);
            return `${year}-${month}-${day}`
        }

        return null;
    }
    validateDate = (date) => {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(date)) return false;
        const parsedDate = new Date(date);
        return !isNaN(parsedDate.getTime());
    };

    groupErrorsByRow = (errors) => {
        return errors.reduce((acc, error) => {
            if (!acc[error.row]) {
                acc[error.row] = [];
            }
            acc[error.row].push({ field: error.field, value: error.value });
            return acc;
        }, {});
    };

    isDuplicate(value, set) {
        if (set.has(value)) {
            return true;
        } else {
            set.add(value);
            return false;
        }
    }

    showValidationErrors = (invalidEntries) => {
        Modal.error({
            title: `CSV Validation Errors (${invalidEntries.length})`,
            width: 800,
            style: { top: 20 },
            content: (
                <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
                    <List
                        size="small"
                        dataSource={invalidEntries}
                        renderItem={({ row, errors }) => (
                            <List.Item>
                                <List.Item.Meta
                                    avatar={<FaExclamationCircle style={{ color: 'red' }} />}
                                    title={`Row ${row}`}
                                    description={
                                        <div style={{ color: 'black' }}>
                                            {/* {errors.join(' | ')} */}
                                            {errors.join(' \n ')}
                                        </div>
                                    }
                                />
                            </List.Item>
                        )}
                    />
                </div>
            ),
            footer: (
                <div className='d-flex' style={{ justifyContent: 'flex-end' }}>
                    <Button type='primary' onClick={() => Modal.destroyAll()} className='button-mail' > Close </Button>
                </div>
            ),
        });
    };

    validateExtendValidityDate(validTill, includeDate = true) {
        try{
            const today = dayjs();
            const sixMonthsFromNow = today.add(6, 'month').add(1, 'day');
            const extendValidityDate = dayjs(validTill);

            // Construct date string for error messages if includeDate is true
            const dateString = includeDate ? `: "${validTill}"` : '';

            // Check if the date is after today and within 6 months + 1 day
            if (!extendValidityDate.isAfter(today)) {
                return `Valid till date must be in the future${dateString}`;
            } else if (extendValidityDate.isAfter(sixMonthsFromNow)) {
                return `Valid till date must be within the next 6 months${dateString}`;
            }
            return null; // No error
        }
        catch(error){
            console.log("validateExtendValidityDate:",error);
        }
        
    }

    handleCardClick = async (action, file = null) => {
        if (action === "download") {
            // const csvContent = 'Email,Extend Validity(yyy-mm-dd) To\n';
            // fileDownload(csvContent, `BulkExtensionTemplate.csv`, 'text/csv');
            fileDownload(bulkUpdateCSVcontent, `BulkUpdateTemplate.csv`, 'text/csv');
        }

        if (action === "upload") {
            // this.setState({
            //     isLoading:true,
            // })
            Papa.parse(file, {
                complete: async (result) => {
                    this.setState({
                        isLoading:true,
                    })
                    const data = result.data;

                    if (data.length === 0 || (data.length === 1 && data[0].length === 1 && !data[0][0].trim())) {
                        errorToast('The CSV file is empty.');
                        this.setState({
                            isLoading:false,
                        })
                        return;
                    }

                    const headers = data[0].map(header => header.trim());
                    const rows = data.slice(1);
                    if (rows.length === 0) {
                        errorToast('The CSV file is empty.');
                        this.setState({
                            isLoading:false,
                        })
                        return;
                    }
                    const domainSet = new Set();
                    const rowErrors = {};
                    const jsonData = await Promise.all(
                        rows.map(async (row, index) => {
                            const rowIndex = index + 1;
                            const email = row[headers.indexOf('Email(Mandatory)')];
                            const fname = row[headers.indexOf('First Name')];
                            var mname = row[headers.indexOf('Middle Name(Use \'NA\' to remove the field)')];
                            const lname = row[headers.indexOf('Last Name')];
                            var mobile = row[headers.indexOf('Mobile No(Use \'NA\' to remove the field)')];
                            var gender = row[headers.indexOf('Gender')];
                            var department = row[headers.indexOf('Department')];//
                            const designation = row[headers.indexOf('Designation')];//
                            var skill = row[headers.indexOf('Skills(separate by comma)')];//
                            const displaySkill = row[headers.indexOf('Skills(separate by comma)')];//
                            const yearOfExp = row[headers.indexOf('Years of Experience')];
                            const rnManager = row[headers.indexOf('Reporting Manager Email')];//
                            const tmlRegion = row[headers.indexOf('TML Region')];//
                            const tmlLocation = row[headers.indexOf('TML Location')];//
                            const project = row[headers.indexOf('Project Name')];//
                            const vnMangerMail = row[headers.indexOf('Vendor Manager Email')];
                            const vnMangerName = row[headers.indexOf('Vendor Manager Name')];
                            var sioCode = row[headers.indexOf('SIO code')];
                            var employeeType = row[headers.indexOf('Employee Type (NONIT or IT)')];
                            const personalMail = row[headers.indexOf('Personal Mail')];
                            const validTill = this.normalizeDate(row[headers.indexOf('Extend Validity to (yyyy-mm-dd)')]);
                            var isActive = row[headers.indexOf('isActive(0 for Disable)')];
                            var updateSkill = {}
                            if (!rowErrors[rowIndex]) {
                                rowErrors[rowIndex] = [];
                            }
                            if (!email) {
                                rowErrors[rowIndex].push(`Email is mandatory: "${rowIndex}"`);
                            }
                            else {
                                if ((!fname && !mname && !lname && !mobile && !gender && !department && !designation && !skill && !displaySkill && !yearOfExp && !rnManager && !tmlRegion && !tmlLocation && !project && !vnMangerMail && !vnMangerName && !sioCode && !employeeType && !validTill && !isActive && !personalMail)) {
                                    rowErrors[rowIndex].push(`Atleast one field Should filled for update: "${rowIndex}"`);
                                }
                                if (validTill) {
                                    if (!this.validateDate(validTill)) {
                                        rowErrors[rowIndex].push(`Invalid Extend Validity To: "${validTill}"`);
                                    } else {
                                        const dateError = this.validateExtendValidityDate(validTill);
                                        if (dateError) {
                                            rowErrors[rowIndex].push(dateError);
                                        }
                                    }
                                }
                                if (this.isDuplicate(email, domainSet)) {
                                    rowErrors[rowIndex].push(`Duplicate Email: "${email}"`);
                                }
                                if (email== personalMail){
                                    rowErrors[rowIndex].push(`Personal email ID should not be the same as the employee's official Tata Motors email:"${email}"`)
                                }

                                if (fname && !alphabetSpaceRegex.test(fname)) {
                                    rowErrors[rowIndex].push(`Invalid First Name: "${fname}"`);
                                }
                                if (mname && (mname != '-' || mname != 'NA') && !alphabetSpaceRegex.test(mname)) {
                                    rowErrors[rowIndex].push(`Invalid Middle Name: "${mname}"`);
                                }
                                if (lname && !alphabetSpaceRegex.test(lname)) {
                                    rowErrors[rowIndex].push(`Invalid Last Name: "${lname}"`);
                                }

                                if (vnMangerName && !alphabetSpaceRegex.test(vnMangerName)) {
                                    rowErrors[rowIndex].push(`Invalid Vendor Manager Name: "${vnMangerName}"`);
                                }
                                if (vnMangerMail && !emailRegex.test(vnMangerMail)) {
                                    rowErrors[rowIndex].push(`Invalid Vendor Manager email: "${vnMangerMail}"`);
                                }
                                if (mobile && !phoneRegex.test(mobile)) {
                                    rowErrors[rowIndex].push(`Invalid mobile: "${mobile}"`);
                                }
                                if (personalMail && !emailRegex.test(personalMail)) {
                                    rowErrors[rowIndex].push(`Invalid Personal Mail: "${personalMail}"`);
                                }
                                if (yearOfExp){
                                    if (!numbericRegex.test(yearOfExp)) {
                                    rowErrors[rowIndex].push(`Invalid Years of Exprience: "${yearOfExp}"`);
                                    }
                                    else if(!(yearOfExp > 0 && yearOfExp <= 60 )){
                                        rowErrors[rowIndex].push(`Years of Exprience must be between 0 to 60 Years : "${yearOfExp}"`);
                                    }
                                }
                                if (sioCode) {
                                    if (sioCode && !alphaNumbericRegex.test(sioCode)) {
                                        rowErrors[rowIndex].push(`Invalid SIO code: "${sioCode}"`);
                                    }
                                    else {
                                        sioCode = sioCode.toUpperCase()
                                    }
                                }

                                if (gender) {
                                    if (genderRegex.test(gender)) {
                                        gender = gender.charAt(0).toUpperCase() + gender.slice(1).toLowerCase();
                                    }
                                    else {
                                        rowErrors[rowIndex].push(`Invalid Gender: "${gender}"`);
                                    }
                                }

                                if (project && !this.handleFindMaster(this.state.project, project)) {
                                    rowErrors[rowIndex].push(`Invalid Project: "${project}"`)
                                }
                                if (tmlLocation && !this.handleFindMaster(this.state.tmlLocationData, tmlLocation)) {
                                    rowErrors[rowIndex].push(`Invalid TML Location: "${tmlLocation}"`)
                                }
                                if (tmlRegion && !this.handleFindMaster(this.state.tmlRegionData, tmlRegion)) {
                                    rowErrors[rowIndex].push(`Invalid TML Region: "${tmlRegion}"`)
                                }
                                if (skill) {
                                    updateSkill = this.handleSkills(skill)

                                    if (!updateSkill.allEmpSkillsValid) {
                                        rowErrors[rowIndex].push(`Invalid Skill: "${updateSkill.missingSkills}"`)
                                    }
                                    else {
                                        skill = updateSkill.foundSkills
                                    }
                                }

                                if (designation && !this.handleFindMaster(this.state.designationList, designation)) {
                                    rowErrors[rowIndex].push(`Invalid Department: "${designation}"`)
                                }
                                if (employeeType) {
                                    if (empTypeRegex.test(employeeType)) {
                                        employeeType = employeeType.toUpperCase();
                                        if (rnManager) {
                                            await this.checkManagerDetails(rnManager, employeeType).then((check_manager)=>{
                                                console.log("check_manager ", check_manager)
                                                if (!check_manager) {
                                                    rowErrors[rowIndex].push(`TML Manager details not found: "${rnManager}"`)
                                                }
                                            })
                                        }
                                        if (department) {
                                            await this.handleDepartment(department, employeeType).then((check_department)=>{
                                                if (!check_department) {
                                                    rowErrors[rowIndex].push(`Invalid Department for Employee type "${employeeType}" for "${email}" : "${department}"`)
                                                }
                                                else{
                                                    department = check_department
                                                }
                                            })
                                        }
                                    }
                                    else {
                                        rowErrors[rowIndex].push(`Invalid Employee Type: "${email}"`)
                                    }
                                }
                                else if ((!employeeType && rnManager) || (!employeeType && department)) {
                                    if (rnManager) {
                                        rowErrors[rowIndex].push(`Employee Type Cannot be Empty to update Reporting manager: "${email}"`)
                                    }
                                    if (department) {
                                        rowErrors[rowIndex].push(`Employee Type Cannot be Empty to update Department: "${email}"`)
                                    }
                                }

                                if (isActive) {
                                    if (isActive && !isActiveRegex.test(isActive)) {
                                        rowErrors[rowIndex].push(`Invalid Active Status: "${isActive}"`)
                                    }
                                    else {
                                        if (isActive == 0) {
                                            isActive = false
                                        }
                                        else {
                                            isActive = true
                                        }
                                    }
                                }
                            }
                            return { email, fname, mname, lname, personalMail, mobile, gender, department, designation, skill, yearOfExp, rnManager, tmlRegion, tmlLocation, project, vnMangerMail, vnMangerName, sioCode, employeeType, validTill, displaySkill, isActive }
                        })
                    );
                    Promise.all(jsonData).then(() => {
                        const filterErrors = Object.entries(rowErrors).filter(([rows, errors]) => errors.length > 0);
                        const invalidEntries = filterErrors.map(([row, errors]) => ({
                            row: parseInt(row),
                            errors
                        }));

                        if (invalidEntries.length > 0) {
                            this.setState({isModelOpen:true})
                            this.showValidationErrors(invalidEntries);
                        } else {
                            this.getEmployeeData(jsonData);
                        }
                    })
                    this.setState({
                        isLoading:false,
                    })
                },
                header: false,
                skipEmptyLines: true
            });
            // this.setState({
            //     isLoading:false,
            // })
        }
    };

    checkManagerDetails = async(manager,employeeType) =>{
        // Commnet for release
        // return true
        let body = {
            "manager": manager.trim(),
            "employeeType": employeeType
        }
        return new Promise((resolve, reject) => { 
            getAPIResponse(config.getManagerDetails, "POST", body)
            .then((response) => {
                if (response.status !== 200){
                    return null
                }
                else {
                    return response.json()
                }
            }).then((data) => {
                if (data !== null && data.status === 1) {
                    resolve(true)
                }
                else {
                    resolve(false)
                }
            })
            .catch((error) => {
                resolve(false)
            })
        })
    }

    getEmployeeData = (data) => {
        const email = data.map(x => x.email);
        return getAPIResponse(config.getNDAEmployeesByDomainIds, 'POST', { email })
            .then((response) => {
                if (response.status === 500 || response.status === 0) {
                    errorToast(INTERNAL_SERVER_ERROR)
                    return null
                } else {
                    return response.json()
                }
            }).then((response) => {
                if (response !== null && response.status == 1) {
                    const not_found = response.data.domain_ids_not_found.map(data => {
                        return {
                            'email': data,
                            'status': 'Not Found'
                        }
                    })
                    const found = response.data.nda_employee_details.map(data => ({
                        fullName: data.DisplayName,
                        email: data.userPrincipalName,
                        domainId: data.samAccountName
                    }))
                    this.showCsvDataModal(data, found, not_found)
                }
            })
    }

    showCsvDataModal = (data = [], dataFound = [], dataNotFound = [], requestAlreadyExist = [], requestInvalid = []) => {
        if (requestAlreadyExist.length === 0) {
            successToast('CSV file imported successfully!');
        }
        this.setState({ isModelOpen: false })
        const modal = requestAlreadyExist.length > 0 ? Modal.error : Modal.info
        modal({
            title: requestAlreadyExist.length > 0 ? 'Update Request Found' : 'CSV Import Data',
            width: 800,
            centered: true,
            content: (
                <>
                    {
                        requestInvalid.length > 0 ?
                        (<>
                            <Title level={5}>{`Employee data rejected!. Please check and reupload the data: (${requestInvalid.length})`}</Title>
                            <Table
                                columns={this.state.employeeIssue}
                                dataSource={requestInvalid}
                                rowKey={(record, index) => index}
                                pagination={{
                                    pageSize: 5,
                                }}
                                scroll={{
                                    y: 240,
                                }}
                            />
                        </>
                        ) : requestAlreadyExist.length === 0 ?
                            (
                                <>
                                    <Divider className='my-1' />
                                    <Title level={5}>{`Employee Found (${dataFound.length})`}</Title>
                                    <Table
                                        columns={this.state.employeeFound}
                                        dataSource={dataFound}
                                        rowKey={(record, index) => index}
                                        pagination={{
                                            pageSize: 10,
                                        }}
                                        scroll={{
                                            y: 240,
                                        }}
                                    />

                                    <Title level={5}>{`Employee data not found for the following Email IDs (${dataNotFound.length})`}</Title>
                                    <Table
                                        columns={this.state.employeeNotFound}
                                        dataSource={dataNotFound}
                                        rowKey={(record, index) => index}
                                        pagination={{
                                            pageSize: 5,
                                        }}
                                        scroll={{
                                            y: 240,
                                        }}
                                    />
                                </>
                            ) : (
                                <>
                                    <Title level={5}>{`Following employees and invalid for update: (${requestAlreadyExist.length})`}</Title>
                                    <Table
                                        columns={this.state.employeeIssue}
                                        dataSource={requestAlreadyExist}
                                        rowKey={(record, index) => index}
                                        pagination={{
                                            pageSize: 5,
                                        }}
                                        scroll={{
                                            y: 240,
                                        }}
                                    />
                                </>
                            )
                    }
                </>
            ),
            ...(requestInvalid.length > 0 ?
                {footer: null}:
                requestAlreadyExist.length === 0 ? {
                footer: (
                    <div className='d-flex mt-3' style={{ justifyContent: 'flex-end' }}>
                        <Button type='primary' onClick={() => {
                            this.setEmployeeData(data, dataFound);
                            Modal.destroyAll();
                        }} className='button-mail' > Import </Button>
                    </div>
                )
            } : { footer: null }),
            maskClosable: true,
            closable: true
        });
    };

    setEmployeeData = (mapping, data) => {
        
        const result = data.map((value, index) => {
            const domain = mapping.find(x => x.email === value.email);
            
            var request_body = value
            if (domain?.fname) {
                request_body['firstName'] = domain?.fname
            }
            if (domain?.mname) {
                request_body['middleName'] = domain?.mname
                if( domain?.mname =='-' || domain?.mname =='NA'){
                    request_body['middleName'] = null
                }
            }
            if (domain?.lname) {
                request_body['lastName'] = domain?.lname
            }
            if (domain?.mobile) {
                request_body['mobile'] = domain?.mobile
                if( domain?.mobile =='-' || domain?.mobile =='NA'){
                    request_body['mobile'] = null
                }
            }
            if (domain?.yearOfExp) {
                request_body['yearsOfExperience'] = domain?.yearOfExp
            }
            if (domain?.gender) {
                request_body['gender'] = domain?.gender
            }
            if (domain?.skill) {
                request_body['skills'] = domain?.skill
            }
            if (domain?.designation) {
                request_body['designation'] = domain?.designation
            }
            if (domain?.rnManager) {
                request_body['tmlManager'] = domain?.rnManager
            }
            if ( domain?.tmlRegion) {
                request_body['tmlRegion'] = domain?.tmlRegion
            }
            if (domain?.tmlLocation) {
                request_body['tmlOffice'] = domain?.tmlLocation
            }
            if (domain?.project) {
                request_body['project'] = domain?.project
            }
            if (domain?.vnMangerName) {
                request_body['vendorManagerName'] = domain?.vnMangerName
            }
            if (domain?.vnMangerMail) {
                request_body['vendorManagerEmail'] = domain?.vnMangerMail
            }
            if (domain?.department) {
                request_body['department'] = domain?.department
            }
            if (domain?.sioCode) {
                request_body['sioCode'] = domain?.sioCode
            }
            if (domain?.validTill) {
                request_body['validTill'] = domain?.validTill
            }
            if (domain?.displaySkill) {
                request_body['displaySkill'] = domain?.displaySkill
            }
            if (domain?.isActive) {
                request_body['isactive'] = domain?.isActive
            }
            if (domain?.employeeType) {
                request_body['employeeType'] = domain?.employeeType
            }
            if (domain?.personalMail) {
                request_body['personalMail'] = domain?.personalMail
            }
            return request_body
        })

        this.setState({
            employeeData: [...this.state.employeeData, ...result],
        })
    }

    render() {
        const isDisable = this.isDisable();
        const { currentPage } = this.state;
        const totalPages = this.getTotalPages();
        const currentItems = this.getCurrentItems();
        return (
            <>
                <div id='bulk_extension_header'>
                    <div className='row'>
                        {/* <div className='col-3 pl-0'>
                            <Form.Label>Employee Detail</Form.Label><sup style={{ color: 'red' }}>*</sup>
                            <AsyncSelect
                                placeholder={"Search By Employee Name"}
                                name='bulkExtensionSelect'
                                instanceId={'bulkExtensionSelect'}
                                className='myclass'
                                loadOptions={this.smartSearchForEmployee}
                                onChange={(e) => { this.handleEmployeeSearch(e) }}
                                onInputChange={(e) => { this.onInputChangeForSmartSearch(e) }}
                                value={this.state.selectedEmployee}
                                noOptionsMessage={() => !this.state.smartSearhText ? "Enter Employee Name" : "No Data Found"}
                                classNames='w-80'
                                styles={{
                                    menu: (provided) => ({
                                        ...provided,
                                        zIndex: 3
                                    })
                                }}
                            />
                        </div>
                        <div className='col-3'>
                            <Form.Label>Extend Validity To</Form.Label><sup style={{ color: 'red' }}>*</sup>
                            <DatePicker
                                style={{ fontFamily: 'sans-serif' }}
                                className="form-control"
                                name="bulkExtensionDate"
                                placeholderText="Extend Valid To"
                                minDate={dayjs().add(1, 'day')}
                                maxDate={dayjs().add(6, 'month').add(1, 'day')}
                                dateFormat="yyyy-MM-dd"
                                value={this.state.validTill}
                                onChange={(e) => { this.handleDate(e) }}
                            />
                        </div>
                        <div className='validate'>
                            <div className='change-cursor' onClick={this.onClear}>
                                <Tooltip title="Clear" color="#FFF" overlayInnerStyle={{ color: "#000000" }}>
                                    <div className="clear-button w-20"><VscClearAll className="clear-icon" /></div>
                                </Tooltip>
                            </div>

                            <div className={`ml-2 button-mail ${isDisable ? 'disabled' : ''}`} onClick={() => { isDisable ? null : this.handledAddEmployee() }}>{"Add Employee"}</div>
                        </div> */}
                        <div className='mt-auto mr-auto'>
                            <div className={`mr-2 button-mail ${this.state.employeeData.length > 0 ? 'disabled' : ''}`} onClick={() => this.state.employeeData.length > 0 ? null : this.setState({ isModelOpen: !this.state.isModelOpen })}>{"Upload CSV"}</div>
                        </div>
                    </div>
                </div>
                <div id='bulk_extension_body' className="overflow-auto mt-4" style={{ height: '71.5vh' }}>
                    <AdminTable
                        headers={this.state.defaultHeaders}
                        currentPageData={currentItems}
                        noDataMessage={"No Data. Please Add an Employee."}
                        handledRemoveEmployee={this.handledRemoveEmployee}
                        handledToggleActiveEmployee={this.handledToggleActiveEmployee}
                        isActiveDisable={true}
                    />
                    <div className='pagination justify-content-between'>
                        <div className='row m-0 p-0'>
                            <div className={`ml-0 ${currentPage !== 1 ? 'button-paginate' : 'button-paginate disabled'}`} onClick={currentPage !== 1 ? () => this.handlePageChange(currentPage - 1) : null}>
                                <FaArrowLeft style={{ display: 'inline-block', alignItems: 'center' }} size={'0.8rem'} />
                                {" Previous"}
                            </div>
                            <div className={'page-label'}>{this.state.currentPage}</div>
                            <div className={currentPage !== totalPages ? 'button-paginate' : 'button-paginate disabled'} onClick={currentPage !== totalPages ? () => this.handlePageChange(currentPage + 1) : null}>
                                {"Next "}
                                <FaArrowRight style={{ display: 'inline-block', alignItems: 'center' }} size={'0.8rem'} />
                            </div>
                        </div>
                        <div className='ml-0 d-flex'>
                            <div className={`${this.state.employeeData.length > 0 ? 'button-paginate' : 'button-paginate disabled'}`} onClick={this.state.employeeData.length > 0 ? () => this.setState({ employeeData: [] }) : null}>
                                {"Clean Table"}
                            </div>
                            <div className={`${this.state.employeeData.length > 0 ? 'button-paginate' : 'button-paginate disabled'}`} onClick={this.state.employeeData.length > 0 ? () => this.handledSubmitRequest() : null}>
                                {"Submit"}
                            </div>
                        </div>
                    </div>
                </div>
                <Modal
                    title="Bulk Update - Add Employee using CSV"
                    open={this.state.isModelOpen}
                    footer={[]}
                    centered
                    onCancel={() => this.setState({ isModelOpen: !this.state.isModelOpen })}
                >
                    <p>Manage your data by downloading a template or uploading a file.</p>

                    <div className='d-flex justify-content-around'>
                        <div id="download" className='w-50 h-50'>
                            <Card
                                className='d-flex flex-column align-items-center justify-content-center card-spacing clickable-card'
                                style={{ textAlign: 'center', height: '200px' }}
                                onClick={() => this.handleCardClick('download')}
                            >
                                <MdOutlineDownload color='#0c3057' size={'2.5rem'} />
                                <Title level={5}>Download Template</Title>
                                <p>Get a CSV template to start importing your data.</p>
                            </Card>
                        </div>
                        <div id='upload' className='w-50 h-50'>
                            <Upload
                                accept='.csv'
                                showUploadList={false}
                                beforeUpload={(file) => {
                                    this.onClear();
                                    this.handleCardClick('upload', file);
                                    return false;
                                }}
                                disabled={this.state.isLoading}
                            >
                                <Card
                                    className='d-flex flex-column align-items-center justify-content-center card-spacing clickable-card'
                                    style={{ textAlign: 'center', height: '200px' }}
                                >
                                    {this.state.isLoading ? (
                                        <>
                                            <MdOutlineUploadFile color='#0c3057' size={'3rem'} />
                                            <Title level={5}>Uploading File Please Wait...</Title>
                                            <Skeleton
                                                active={true}
                                                avatar={false}
                                                title={false}
                                                paragraph={{ rows: 2 }}
                                            />
                                        </>

                                    ) : (
                                        <>
                                            <MdOutlineUploadFile color='#0c3057' size={'3rem'} />
                                            <Title level={4}>Upload File</Title>
                                            <p>Upload your CSV file to import data.</p>
                                        </>

                                    )}
                                </Card>
                            </Upload>
                        </div>
                    </div>
                </Modal>
                {
                    this.state.isLoading ?
                        <div className="loader">
                            <div></div>
                        </div> : null
                }
            </>
            
        )
    }
}

const mapStateToProps = (state) => ({})

const mapDispatchToProps = {}

export default connect(mapStateToProps, mapDispatchToProps)(BulkExtensionTab)