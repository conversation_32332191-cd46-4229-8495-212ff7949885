import { Component } from "react";
import { connect } from "react-redux";
import {decrementCounter,incrementCounter,step1} from "../../redux/actions/counterActions";
import Router from "next/router";
import React, { useState } from "react";
import '../../styles/Home.module.css'
import { ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form, Overlay, Popover , Spinner} from "react-bootstrap";
import Select from "react-select";
import moment from "moment";
import { addDays, addMonths, previousDay } from "date-fns";
import { getAPIResponse } from '../../constants/Utils'
import * as config from '../../constants/config'
import * as constant from '../../constants/constants'
import '../../styles/Home.module.css'
import { login, logOut } from "../../redux/actions/loginActions";
import makeAnimated from 'react-select/animated';
import { faL, fas } from "@fortawesome/free-solid-svg-icons"
import { withRouter } from "next/router";
import { DatePicker,Tooltip } from "antd";
import dayjs, { Dayjs } from "dayjs";
import { ERROR_MESSAGE_FOR_VALID_FROM, ExpiryMonthRange, INTERNAL_SERVER_ERROR, INVALID_PROJECT, MANAGER_DETAILS_NOT_FOUND, MANAGER_REQUESTER_CANNOT_SAME, MANAGER_VENDOR_CANNOT_SAME, PLEASE_ENTER_MANAGER, PLEASE_ENTER_MANAGER_EMAIL, PLEASE_ENTER_PROJECT_NAME, PLEASE_ENTER_REQUIRED_FIELD, PLEASE_ENTER_VALID_EAMIL_ID, PLEASE_SELECT_EMPLOYEE_TYPE, PLEASE_SELECT_VENDOR_COMPANY,PLEASE_ENTER_VALID_MOBILE_NO } from "../../constants/message";
import { successToast, errorToast } from "../../toast"
import { ToastContainer } from "react-toastify";
import { FaInfoCircle } from "react-icons/fa";

class Request extends Component {
  static getInitialProps({ store }) {}

  constructor(props) {
    super(props);
    this.state = {
      personEmail: '',
      personMobile: '',
      vendor: null,
      middleName: '',
      firstName: "",
      lastName: "",
      locationOption: [],
      manager: "",
      emailfag: true,
      positionPerson: "",
      PersNo: "",
      location: null,
      tmlManagerEmail: "",
      CompleteName: "",
      designation: "",
      validityFrom: dayjs(),
      validityTo:dayjs().add(ExpiryMonthRange,'month'),
      validityToNew: "",
      validityFromNew: "",
      accountType: ["Domain"],
      response: [""],
      employeeId: "",
      department: "",
      checked: true,
      city: "",
      gender: constant.Gender,
      genderType: null,
      vendorManager: "",
      vendorManagerEmail: "",
      project: [],
      projectName: null,
      sapID: "",
      designationList : [],
      tmlManagerPerno : "",
      vendorOption: [],
      tmlManagerCompanyCode :"",
      skillData : [], 
      skill : [],
      yearOfExperience : "",
      showDropDown : false,
      target : "",
      tmlLocation:"",
      swipeLocation:null,
      swipeLocationOptions:[],
      isBuDisabled:true,
      buCompanyCode:'',
      buCompanyCodeList:[],
      tmlRegion:"",
      tmlLocationData:[],
      tmlRegionData :[],
      isLoading:false,
      sameManagerField:"",
      tmlManagerTitle:"",
      disable:false,
      tmlManagerData:[],
      email:false,
      internet:false,
      managerLoder : false ,
      managerErrorMessage : "",
      towerName:"",
      towerData:"",
      isTowerShown:false,
      personEmailMessage : "",
      vendorEmailMessage:"",
      source:"",
      employeeType:"",
      employeeTypeErrorMessage:"",
      sioCode:"",
      departmentData : [], 
      edataDepartment:[],
      departmentType: "",
      isDepartmentDisable: true,
      departmentTempData:[],
      projectLoader: false,
      projectDetail: "",
      sameProjectField:"",
      projectErrorMessage : "",
      addNewProject : false,
      projectSuccessMessage:"",
      isL2Editable: false,
      l2ManagerName: "",
      l2ManagerEmail: "",
      l2FieldsEditable: false,
      l2ManagerEmployeeId: "",
      l2ManagerCompCode: "",
    };
    this.handleSubmit.bind(this);
    
  }

  componentDidMount() {
    this.getVendorList()
    this.getSwipeLocationList()
    this.getBuCompanyCodeList()
    this.getDesignationList()
    this.getProjectList(this.props.vendor)
    this.getTmlLocation()
    this.getTmlRegion()
    this.getSkill()
    this.getDepartment()
    if(this.props.vendor === "TCS") this.getTower()
   
  }

  getVendorList = () => {
    getAPIResponse(config.getVendorList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getVendorList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              vendorOption : [...prevState.vendorOption , {value : obj.vendorCode , label : obj.vendorName}]
            }),() => {this.getVendorName()})
          })
      }
      else {
        console.log("getVendorList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getVendorList() in Request",error)
    })

  }

  getSwipeLocationList = () => {
    getAPIResponse(config.getSwipeLocationList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getSwipeLocationList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          const swipeLocationOptions = data.data.map((obj) => ({value : obj.locationCode , label : obj.locationName}))
          const swipeLocation = swipeLocationOptions.find(o=>o.label === 'NA');
          this.setState(prevState => ({
            ...prevState,
            swipeLocationOptions,
            swipeLocation
          }),() => {this.getSwipeLocationName()})
      }
      else {
        console.log("getSwipeLocationList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getSwipeLocationList() in Request",error)
    })
  }

  getSwipeLocationName = () => {
    let LocationName = this.props.swipeLocation
    this.state.swipeLocationOptions.map((obj , index) => {
      if(LocationName === obj.value){
        this.setState({
          vendor:obj
        })
      }
    })
  }

  getBuCompanyCodeList = () => {
    getAPIResponse(config.getBuCompanyCodeList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getBuCompanyCodeList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj) => {
            this.setState(prevState => ({
              buCompanyCodeList : [...prevState.buCompanyCodeList , {value : obj.compCode , label : obj.compName}]
            }))
          })
      }
      else {
        console.log("getBuCompanyCodeList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getBuCompanyCodeList() in Request",error)
    })
  }

  getVendorName = () => {
    let vendorname = this.props.vendor
    this.state.vendorOption.map((obj , index) => {
      if(vendorname === obj.value){
        this.setState({
          vendor:obj
        })
      }
    })
  }

  getDesignationList = () =>{
    getAPIResponse(config.getDesignationList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getDesignationList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              designationList : [...prevState.designationList , {value : obj.designationCode , label : obj.designationName}]
            }))
          })
      }
      else {
        console.log("getDesignationList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getDesignationList() in Request",error)
    })
  }

  getProjectList = (vendor) => {
    if(vendor !== null){
    let queryParams = new URLSearchParams({
      'vendor': vendor
    })
    this.setState({
      project:[]
    })
      getAPIResponse(`${config.getProjectList}?${queryParams.toString()}` , "GET" , {})
      .then((response) => {
        if(!response.ok){
          console.log("getProjectList() in Request",response)
          return null
        }
        else{
          return response.json()
        }
      }).then((data) => {
        if(data !== null){
            data.data.map((obj , index) => {
              this.setState(prevState => ({
                project : [...prevState.project , {value : obj.projectCode , label : obj.projectName}]
              }))
            })
            this.setState(prevState => ({
              project : [...prevState.project , {value : 'OTH' , label : 'Other'}]
            }))
        }
        else {
          console.log("getProjectList() in Request",response)
        }
      })
      .catch((error) => {
        console.log("getProjectList() in Request",error)
      })
    }
  }

  getTmlLocation = () => {
    getAPIResponse(config.getTmlOffice , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getTmlLocation() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              tmlLocationData : [...prevState.tmlLocationData , {value : obj.officeCode , label : obj.officeName}]
            }))
          })
      }
      else {
        console.log("getTmlLocation() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getTmlLocation() in Request",error)
    })
  }

  getTmlRegion = () => {
    getAPIResponse(config.getTmlRegion , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getTmlRegion() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              tmlRegionData : [...prevState.tmlRegionData , {value : obj.regionCode , label : obj.regionName}]
            }))
          })
      }
      else {
        console.log("getTmlRegion() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getTmlRegion() in Request",error)
    })
  }

  getSkillList = () => {
    let skillList = this.state.skill.map((value , index) => value.value)
    return skillList
  }

  getSkill = () => {
    getAPIResponse(config.getSkillList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getSkill() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              skillData : [...prevState.skillData , {value : obj.skillCode , label : obj.skillName}]
            }))
          })
      }
      else {
        console.log("getSkill() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getSkill() in Request",error)
    })
  }

  getTower = () => {
    getAPIResponse(config.getTowerList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getSkill() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              towerData : [...prevState.towerData , {value : obj.towerCode , label : obj.towerName}]
            }))
          })
      }
      else {
        console.log("getTower() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getTower() in Request",error)
    })
  }

  getDepartment = () => {
    getAPIResponse(config.getDepartmentList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getDepartment() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              departmentData : [...prevState.departmentData , {value : obj.departmentCode , label : obj.departmentName,  type: obj.empType}]
            }))
          })
      }
      else {
        console.log("getDepartment() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getDepartment() in Request",error)
    })
  }

  handleSubmit = (event) => {
    // if (!this.state.buCompanyCode || !this.state.buCompanyCode.value) {
    //     errorToast("Please select BU Company Code");
    //     return;
    // }
    // event.preventDefault();
    let flag = this.validateForm(this.state.CompleteName,this.state.employeeId,this.state.firstName,this.state.lastName,this.state.personEmail,this.state.personMobile,this.state.tmlLocation,this.state.buCompanyCode,this.state.swipeLocation,this.state.designation,this.state.validityTo,this.state.city,this.state.department,this.state.genderType,this.state.vendor,this.state.projectName,this.state.vendorManager,this.state.vendorManagerEmail,this.state.manager,this.state.skill,this.state.yearOfExperience,this.state.tmlRegion,this.state.sioCode,this.state.edataDepartment);
    if (flag) {
      this.setState({
        isLoading : true,
        disable:true
      })
      let apiFormatedData = {
        employeeId : this.state.employeeId,
        firstName : (this.state.firstName.trim().charAt(0).toUpperCase() + this.state.firstName.trim().toLowerCase().slice(1)),
        middleName : (this.state.middleName?.trim().charAt(0).toUpperCase() + this.state.middleName?.trim().toLowerCase().slice(1)),
        lastName : (this.state.lastName.trim().charAt(0).toUpperCase() + this.state.lastName.trim().toLowerCase().slice(1)),
        mobile : this.state.personMobile,
        email : this.state.personEmail.toLowerCase().trim(),
        designation : this.state.designation.value,
        gender : this.state.genderType.value,
        vendor : this.state.vendor.value,
        project : this.state.projectName.value,
        vendorManagerName: this.capitalizeEachLetter(this.state.vendorManager.trim()),
        vendorManagerEmail: this.state.vendorManagerEmail.toLowerCase().trim(),
        tmlManagerEmail: this.state.manager.toLowerCase(),
        tmlManagerName:this.state.CompleteName,
        tmlManagerEmployeeId:this.state.tmlManagerPerno,
        tmlManagerCompCode:this.state.tmlManagerCompanyCode,
        createdBy:this.props.loginData.empId,
        validTill:dayjs(this.state.validityTo).format("YYYY-MM-DD"),
        validFrom:dayjs(this.state.validityFrom).format("YYYY-MM-DD"),
        skills : this.getSkillList(),
        tmlRegion:this.state.tmlRegion.value,
        tmlOffice:this.state.tmlLocation.value,
        buCompCode:this.state.buCompanyCode.value,
        swipeLocation:this.state.swipeLocation.value,
        yearsOfExperience:this.state.yearOfExperience,
        tmlManagerTitle:this.state.tmlManagerTitle,
        isEmail:this.state.email,
        isInternet:this.state.internet,
        requestType:'Create',
        tower:this.state.towerName.value,
        source:this.state.source,
        employeeType:this.state.employeeType?.value,
        edataDepartment:this.state.edataDepartment?.label,
        sioCode:this.state.sioCode,
        l2ManagerName: this.state.l2ManagerName,
        l2ManagerEmail: this.state.l2ManagerEmail,
        l2ManagerEmployeeId: this.state.l2ManagerEmployeeId,
        l2ManagerCompCode: this.state.l2ManagerCompCode
      };
      if(this.props.isTmlEmployee !== true &&  this.props.vendor !== 'TCS'){
        delete apiFormatedData.tower
      }
      if (this.state.projectName.value == 'OTH'){
        apiFormatedData={
          ...apiFormatedData,
          createProject : this.state.projectDetail
        }
      }
      console.log(apiFormatedData)
      getAPIResponse(config.createRequest , "POST", apiFormatedData)
      .then((response)=>{
        if(response.status === 500){
          this.setState({
            isLoading:false,
            disable:false
          })
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("handleSubmit() in request",response)
          return null
          }
          else {
           return response.json()
          }
      })
      .then((data)=>{
        if(data !== null){
          if(data.status === 1){
            this.setState({
              isLoading : false,
              isTowerShown:false
            })
            successToast(data.message)
            this.goNext()
          }
          else {
            this.setState({
              isLoading:false,
              disable:false
            })
            errorToast(data.message)
            this.getProjectList(this.props.vendor)
          }
        }
      })
      .catch((error)=>{
        this.setState({
          isLoading:false,
          disable:false
        })
        console.log("handleSubmit() in request",error)
        this.getProjectList(this.props.vendor)
      })
      //  https://stackoverflow.com/questions/43842793/basic-authentication-with-fetch
    }
  };

  resetForm = () => {
    this.setState({
      CompleteName: "",
      employeeId : "",
      firstName : "",
      lastName : "",
      middleName:'',
      personMobile : '',
      personEmail : '',
      designation : "",
      gender : "",
      // vendor : "",
      projectName : "",
      vendorManager : "",
      vendorManagerEmail : "",
      tmlManagerEmail : null,
      location:"",
      department: "",
      genderType:"",
      city:"",
      validityFrom:dayjs(),
      validityTo:null,
      manager:"",
      skill:[],
      yearOfExperience:"",
      tmlLocation:"",
      buCompanyCode:'',
      swipeLocation:"",
      tmlRegion:"",
      towerName:"",
      employeeTypeErrorMessage:"",
      employeeType:"",
      email:false,
      internet:false,
      sioCode:""
    });
  }

  onChangeHandler = (event) => {
    let name = event.target.name; 
    let value = event.target.value;
    if(name === "manager"){
      this.setState({
        sameManagerField :false ,
        CompleteName:"",
        city:"",
        department:"",
        [name]: value  ,
        managerErrorMessage:""
      })
    }
    else if(name === "personEmail"){
      if (this.state.personEmailMessage !== "") this.setState({personEmailMessage : "" , [name] :value})
      else this.setState({[name] :value})
    }
    if(name === "project"){
      this.setState({
        sameProjectField :false ,
        projectDetail: value  ,
        projectErrorMessage:"",
        projectSuccessMessage:"",
        disable:false
      })
    }
    else {
      this.setState({ 
        [name]: value,
        
      });
    }
  }; 

  onMobileNumberChange = ({target:{value}})=>{
    let pattern = /^[0-9\b]+$/;
    if(pattern.test(value) || value == ''){
      this.setState({
        personMobile:value
      })
    }
  }
  
  inputValidation = (value , name) => {
    const regex = /[^A-Za-z0-9\s]+$/;
    const regex_employeeID = /[^A-Za-z0-9]+$/;
    
    if(name === "employeeId"){
      if(!regex_employeeID.test(value)){
        this.setState({
          [name] : value
        })
      }
    }
    else if (!regex.test(value)){
      this.setState({
        [name] : value
      })
    }
  }

  emailValidations = (value , error , name) => {
    const regex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/i;
    // if(value !== '' && !regex.test(value)){
    if(value !== '' && !constant.UpdateEmailRegex.test(value)){
      this.setState({
        [error] : "Please enter a valid email ID",
        [name] : null
      })
    } 
  }

  alphabetValidations = (value, name) => {
    const regex = /[^A-Za-z\s]+$/;
    if(!regex.test(value)){
      this.setState({
        [name] : value
      })
    }
  }

  numberValidation = (value , name , error) => {
    if(this.state.experienceErrorMessage !== "") this.setState({experienceErrorMessage : ""})
    const pattern = /^[0-9\b]+$/;
    // const pattern = /^\d{2}$ /
    if(pattern.test(value)){
      if(parseInt(value) > 60){
        this.setState({ [name] : null , [error]  : "Years of experience not be greater than 60" })
      }
      else {
        this.setState({
          [name] : value
        })
      }
    }
    else if(value == ""){
      this.setState({
        [name] : value
      })
    } 
  }

  onChangeEmailHandler = (value , errorValue , error , name) => {
    if (errorValue !== "") this.setState({[error] : "" , [name] : value})
    else this.setState({[name] : value})
  }

  handleDropdownData = (selectedOption, name) => {
    console.log("hiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiii")
    if(name == 'skill'){
      console.log("skill")
      if (selectedOption.length == 0) {
        this.setState({
          skill: []
        })
      } else {
        let skillArray = selectedOption.map((value , index) =>{
          if (selectedOption.length < this.state.skill.length) {
            this.setState({
              skill: selectedOption
            })
          } else {
            this.setState({
              skill : [...this.state.skill,value]
            })
          }
        })
      }
      this.getSkillList()
    }
    else if(name == 'vendor'){
      console.log("vendor")
      if(selectedOption.value === 'TCS'){
        this.setState({
          isTowerShown : true,
          [name] : selectedOption,
          towerData:[],
          projectName:""
        },()=>{
          this.getProjectList(selectedOption.value) , 
          this.getTower()
        })
      }
      else {
        this.setState({
          isTowerShown : false,
          projectName:"",
          [name] : selectedOption
        },()=>{this.getProjectList(selectedOption.value)});
      }
      
    }
    else if(name == 'employeeType'){
      console.log("employeeType")
      this.setState({
        [name]:selectedOption,
        employeeTypeErrorMessage:""
      })
    }
    
    else {
      console.log("else")
      this.setState({ [name] : selectedOption});
    }
    
  };

  handleDate = (value, name) => {
    this.setState({ [name]:value});
  };

  handleCheck = (event) => {
    const { value, name, checked } = event.target;
    const accountType = this.state.accountType;
    if(value === 'Email'){
      if(checked === true){
        this.setState({
          email:true
        })
      }
      else {
        this.setState({
          email:false
        })
      }
    }
    else if(value === 'Internet'){
      if(checked === true){
        this.setState({
          internet:true
        })
      }
      else{
        this.setState({
          internet:false
        })
      }
    }
  };

  validateForm = (CompleteName,employeeId,firstName,lastName,personEmail,personMobile,tmlLocation,buCompanyCode,swipeLocation,designation,validityTo,city,department,gender,vendor,projectName,vendorManager,vendorManagerEmail,tmlManagerEmail,skills,yearOfExperience,tmlRegion,ssiCode,edataDepartment) => {
      // if ( employeeId == "" || firstName == "" || lastName == "" || personEmail == "" || tmlLocation == "" || designation == "" || validityTo == "" || gender == null || vendor == null || projectName == "" || vendorManager == "" || vendorManagerEmail == "" || tmlManagerEmail == null || skills == [].length || yearOfExperience == "" || tmlRegion == "" || ssiCode == "" ) {
      if ( employeeId == "" || firstName == "" || lastName == "" || personEmail == "" || tmlLocation == "" || buCompanyCode == "" || swipeLocation == "" || designation == "" || validityTo == "" || gender == null || vendor == null || projectName == "" || vendorManager == "" || vendorManagerEmail == "" || tmlManagerEmail == null || skills == [].length || yearOfExperience == "" || tmlRegion == "" || edataDepartment == "") {
          errorToast(PLEASE_ENTER_REQUIRED_FIELD)
          return false;
      }
      else if(personMobile && personMobile.length !== 10){
        errorToast(PLEASE_ENTER_VALID_MOBILE_NO)
          return false;
      }
      else if(!constant.UpdateEmailRegex.test(personEmail) || !constant.UpdateEmailRegex.test(vendorManagerEmail)){
        errorToast(PLEASE_ENTER_VALID_EAMIL_ID);
        return false
      }
      else if (this.state.employeeType?.value === "IT" && (city == "" || department == "")){
        errorToast(PLEASE_ENTER_REQUIRED_FIELD);
        return false
      }
      else if (this.state.vendor.value == 'TCS' && this.state.towerName == ""){
          errorToast(PLEASE_ENTER_REQUIRED_FIELD);
          return false
      }
      else if (this.state.manager.toLowerCase() === this.props.loginData.userEmail.toLowerCase()){
        errorToast(MANAGER_REQUESTER_CANNOT_SAME)
        return false
      } 
      else if(this.state.vendorManagerEmail.toLocaleLowerCase() === this.state.manager.toLocaleLowerCase()){
        errorToast(MANAGER_VENDOR_CANNOT_SAME)
        return false
      }
      else if (CompleteName == null || CompleteName == "" || this.state.manager == "") {
        errorToast(MANAGER_DETAILS_NOT_FOUND)
        return false;
      }
      else if (this.state.validityFrom > this.state.validityTo){
        errorToast(ERROR_MESSAGE_FOR_VALID_FROM)
        return false
      }
      else if (this.state.projectName.value == 'OTH' && this.state.projectDetail == ''){
        errorToast(PLEASE_ENTER_PROJECT_NAME)
        return false
      }
      return true
  };

  capitalizeEachLetter = (vendorName) => {
    return vendorName
    .split(' ')
    .map(word => word.split('').map((letter, index) => index === 0 ? letter.toUpperCase() : letter).join(''))
    .join(' ');
  }

  managerDetails = () => {
    if(this.state.manager == "") {
      this.setState({
        managerErrorMessage : PLEASE_ENTER_MANAGER_EMAIL
      })
    }
    else if(this.state.employeeType == ""){
      this.setState({
        employeeTypeErrorMessage : PLEASE_SELECT_EMPLOYEE_TYPE
      })
    }
    else 
    {
      let body = {
        "manager" : this.state.manager.trim(),
        "employeeType" : this.state.employeeType?.value  
      }
      this.setState({
        managerLoder : true
      })
  getAPIResponse(config.getManagerDetails_v2 , "POST" , body)
      .then((response)=>{
        this.setState({
          managerLoder : false
        })
        if(response.status === 500){
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("managerDetails() in Request",response)
          return null
        }
        else {
          return response.json()
        }
      })
      .then((data) => {
        if(data !== null && data.status === 1){
          const l1 = data.data.l1_manager_details;
          const l2 = data.data.l2_manager_details || {};
          const source = l1.source;

          const hasL2Data = l2.fullName && l2.email;

        let l2Editable = false;
      if(source === "SAP"){
        if (l2?.error){
          errorToast(l2.error);
          l2Editable = true;
          l2.fullName = "";
          l2.email = "";
        }
        else if (!l2?.fullName || !l2?.email){
          if (!l2?.fullName) {
            errorToast("L2 Manager Name not found in response.");
          }
          if (!l2?.email) {
            errorToast("L2 Manager Email not found in response.");
          }

        }
      } 
      if (source === "AD") {
        alert("Enter the Manager Name and Email")
        l2Editable = true;
        // l2.fullName = "";
        // l2.email = "";
      }
      else {
        
        l2Editable = false; 
      }
            this.setState({
              city: l1.citytown,
              department: l1.department,
              tmlManagerTitle: l1.title,
              CompleteName: l1.fullName,
              tmlManagerPerno: l1.employeeId,
              tmlManagerCompanyCode: l1.compCode,
              source: source,
              l2ManagerName: l2.fullName || "",
              l2ManagerEmail: l2.email || "",
              l2FieldsEditable: l2Editable,
              l2ManagerEmployeeId: l2.employeeId || "",
              l2ManagerCompCode: l2.compCode || "",

            },()=>this.handleBuCode())
        }
        else {
          // this.setState({
          //   managerErrorMessage:data.message
          // })
          errorToast(data.message);
          console.log("managerDetails() in Request",data)
        }
      })
      .catch((error) => {
        console.log("managerDetails() in Request",error)
      })
    }
    
  }




fetchL2ManagerDetails = async () => {
  const email = this.state.l2ManagerEmail;
  if (this.state.l2ManagerEmail == "") {
    errorToast("Please Enter Manager Email")
  }
  else if (this.state.employeeType === "") {
    errorToast("Please Select Employee Type")
    }
  else{
    let body = {
        "manager" : this.state.l2ManagerEmail.trim(),
        "employeeType" : this.state.employeeType?.value  
      }
      getAPIResponse(config.getManagerDetails , "POST", body)
      .then((response)=>{
        console.log("Raw Response:", response);
        this.setState({
          managerLoder : false
        })
        if(response.status === 500){
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("managerDetails() in Request",response)
          return null
        }
        else {
          return response.json()
        }
      })
      .then((data) => {
        if (data.data.source === "AD") {
        errorToast("L2 Manager details not found. Please enter manually.");
        return;
      }
        if(data !== null && data.status === 1){
          this.setState({
          l2ManagerName: data.data.fullName || "",
          l2ManagerEmployeeId: data.data.employeeId || "",
          tmlManagerCompanyCode: data.data.compCode || "",
        },()=>this.handleBuCode())
        }
        else {
          errorToast(data.message);
          console.log("managerDetails() in Request",data)
        }
      })
      .catch((error) => {
        console.log("managerDetails() in Request",error)
      })
    }
  }

  handleBuCode = ()=>{
    if(this.state.source === 'SharePoint' || this.state.source === 'SAP'){
      const buCompanyCode = this.state.buCompanyCodeList.find(obj=>String(obj.value)===String(this.state.tmlManagerCompanyCode));
      this.setState(prevState=>({...prevState,buCompanyCode,isBuDisabled:true}))
    }
    if(this.state.source === 'AD'){
      const buCompanyCode = this.state.buCompanyCodeList.find(obj=>String(obj.value)===String(this.state.tmlManagerCompanyCode));
      this.setState(prevState=>({...prevState,buCompanyCode,isBuDisabled:true}))
      // this.setState(prevState=>({...prevState,buCompanyCode:"",isBuDisabled:false}))
    }
    
  }

  handleDatalist = (event) => {
    this.setState({
      showDropDown:true,
      target: event.target
    })
  }

  handleDataFroManager = async (name) => {
    if(name.length >= 3){
      await fetch('https://sharepointapp.tatamotors.com/SAP/EmpService.svc/GetEmpDataBySearch/'+ name +'/539FAF43-E69F-42E3-9652-361D8657082E')
      .then((response)=>{
        return response.json()
      })
      .then((data)=>{
        data.map((object , index)=>{
          if(index>4){
            this.setState({
              tmlManagerData : [...this.state.tmlManagerData , { value : object.PersNo , label : object.CompleteName}],
              // break
            })
          }
        })
      })
    }
  }

  handleCloseDropdown = (event) => {
    let value = event.target.value
    if(value.length > 5){

    }
    this.setState({
      showDropDown:false
    })
  }

  validateInput = (event) => {
    input = event.target.value.replace(/\D/g, ''); // Remove non-numeric characters
    if (input.length > 4) {
      input.value = input.value.slice(0, 4); // Limit to 4 digits
    }
  }

  setValidTill = (value) => {
    const validityFrom = this.state.validityFrom
    this.setState({
      validityTo : value.setMonth(value.getMonth() + 6)
    })
  }

  handleEmployeeType = (e) => {
    this.setState({
      employeeType : e,
      manager:"",
      employeeTypeErrorMessage:"",
      CompleteName:"",
      city:"",
      department:"",
      isDepartmentDisable:false
    })
    console.log("e:",e.value)
    if (e.value == 'IT'){
      let selectedDepartment = this.state.departmentData.find(obj => obj.value === 'IT');
      let department = this.state.departmentData.filter(obj => obj.type !== 'NONIT');
      // console.log("department:",selectedDepartment)
      this.setState({
        isDepartmentDisable:true,
        // edataDepartment: selectedDepartment,
        edataDepartment: selectedDepartment,
        departmentTempData: department,
      })
    }
    else{
      let department = this.state.departmentData.filter(obj => obj.type !== 'IT');
      let selectedDepartment = this.state.departmentData.find(obj => obj.value === "NONIT");
      console.log("selectedDepartment:",selectedDepartment )
      this.setState({
        isDepartmentDisable:false,
        departmentTempData: department,
        edataDepartment: selectedDepartment,
        // edataDepartment: selectedDepartment,
      })
    }
  }

  goNext = () => {
    Router.push({
      pathname: "/Dashboard",
    });
  };

  checkProjectDetails = () => {
    const regex = /^[A-Za-z0-9\s\-.,_\/\\&]+$/;
    if(this.state.projectDetail == "") {
      this.setState({
        projectErrorMessage : PLEASE_ENTER_PROJECT_NAME,
        disable:true
      })
    }
    else if(this.state.vendor && this.state.vendor?.value == "") {
      this.setState({
        projectErrorMessage : PLEASE_SELECT_VENDOR_COMPANY,
        disable:true
      })
    }
    else if(!regex.test(this.state.projectDetail)){
      this.setState({
        projectErrorMessage : INVALID_PROJECT,
        disable:true
      })
    }
    else 
    {
      let body = {
        "projectName" : this.state.projectDetail.trim(),
        "vendor" : this.state.vendor?.value,
      }
      this.setState({
        projectLoader : true
      })
      getAPIResponse(config.checkProjectDetails , "POST" , body)
      .then((response)=>{
        this.setState({
          projectLoader : false
        })
        if(response.status === 500){
          errorToast(INTERNAL_SERVER_ERROR)
          return null
        }
        else {
          return response.json()
        }
      })
      .then((data) => {
        if(data !== null && data.status === 1){
          if(!data.data.projectExist){
            this.setState({
              addNewProject: true,
              projectSuccessMessage: data.message,
              disable:false
            })
          }
          else{
            this.setState({
              projectErrorMessage:data.message,
              disable:true
            })
            console.log("checkProjectDetails() in Request",data)
          }
        }
        else {
          this.setState({
            projectErrorMessage:data.message,
            disable:true
          })
          console.log("checkProjectDetails() in Request",data)
        }
      })
      .catch((error) => {
        console.log("checkProjectDetails() in Request",error)
      })
    }
  }

  render() {
    console.log("this.state:",this.state)
    return (
      <>
      {/* <ToastContainer/> */}
      <div>
        <div className="row">
          <div className="col-12">
            <Form autoComplete="off">
              <div className="card card-primary card-outline">
                <div className="card-header with-border">
                  <h2 className="card-title"> Create New Request </h2>
                </div>
                <div className="card-body">

                  {/* ------------------------------ Validation part ---------------------------   */}
                  
                  {/* ------------------------------ Person Details ----------------------------- */}
                  <div className="form-section">
                    <span>Personal Details</span>
                  </div>

                  <Row className="form-grid">
                    <Col md="3">
                      <Form.Group controlId="Firstname">
                        <Form.Label> First Name </Form.Label><sup style={{color:'red'}}>*</sup>
                        <InputGroup>
                          <Form.Control
                            // required
                            type="text"
                            name="firstName"
                            value={this.state.firstName}
                            // onChange={this.onChangeHandler}
                            onChange={(e)=>{this.alphabetValidations(e.target.value , "firstName")}}
                            placeholder=" Enter First Name"
                            maxLength="20"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="middleName">
                        <Form.Label> Middle Name </Form.Label>
                        <InputGroup>
                          <Form.Control
                            type="text"
                            name="middleName"
                            value={this.state.middleName}
                            onChange={(e)=>{this.alphabetValidations(e.target.value , "middleName")}}
                            placeholder=" Enter Middle Name"
                            maxLength="20"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="lastName">
                        <Form.Label> Last Name </Form.Label><sup style={{color:'red'}}>*</sup>
                        <InputGroup>
                          <Form.Control
                            type="text"
                            // required
                            name="lastName"
                            value={this.state.lastName}
                            onChange={(e)=>{this.alphabetValidations(e.target.value , "lastName")}}
                            placeholder=" Enter Last Name"
                            maxLength="20"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="Contact">
                        <Form.Label> Mobile No. </Form.Label>
                        <InputGroup>
                          <Form.Control
                            pattern="[1-9]{1}[0-9]{9}"
                            value={this.state.personMobile}
                            // required
                            type="text"
                            name="personMobile"
                            onChange={this.onMobileNumberChange}
                            placeholder=" Enter Contact Number"
                            maxLength="10"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>
                  

                    <Col md="3">
                      <Form.Group controlId="Email">
                        <div className="d-flex align-items-center justify-content-between">
                        <div>
                        <Form.Label> Vendor Partner Email ID </Form.Label><sup style={{color:'red'}}>*</sup>
                        </div>
                        <Tooltip placement="top" title={`Enter the official email ID provided by the vendor for onboarding.`} arrow >
                        <FaInfoCircle/>
                        </Tooltip>
                        </div>
                        <Form.Control
                          type="email"
                          name="personEmail"
                          value={this.state.personEmail}
                          onChange={(e) => {this.onChangeEmailHandler(e.target.value , this.state.personEmailMessage , "personEmailMessage" , 'personEmail')}}
                          placeholder="Enter Vendor Partner Email"
                          onBlur={()=>{this.emailValidations(this.state.personEmail , "personEmailMessage" , "personEmail")}}
                        />
                        { this.state.personEmailMessage !== "" && this.state.personEmailMessage ? 
                              <span style={{color:"red" , fontSize:"12px"}}>{this.state.personEmailMessage}</span> : null
                        }
                      </Form.Group>
                    </Col>
                    
                    <Col md="3">
                        <Form.Group controlId="Gender">
                          <Form.Label>Gender</Form.Label><sup style={{color:'red'}}>*</sup>
                          <Select
                            options={this.state.gender}
                            value={this.state.genderType}
                            onChange={(e) => {this.handleDropdownData(e, "genderType")}}
                            isSearchable
                            name="genderType"
                            placeholder="Select Gender"
                            className="myclass"
                            noOptionsMessage={({ inputValue }) =>
                              "No results found"
                            }
                            // required
                          />
                        </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="yearOfExperience">
                        <Form.Label>Years of Experience</Form.Label><sup style={{color:'red'}}>*</sup>
                        <div style={{position:'relative'}}>
                        <FormControl
                            name="yearOfExperience"
                            type="text"
                            placeholder="Enter Years Of Experience"
                            className="input-field-apperence"
                            maxLength="2  "
                            value={this.state.yearOfExperience}
                            onChange={(e) => {this.numberValidation(e.target.value, "yearOfExperience" , "experienceErrorMessage")}}
                            // onChange={this.handleCloseDropdown}
                            // oninput={this.validateInput}
                          />
                           { this.state.experienceErrorMessage !== "" && this.state.experienceErrorMessage ? 
                              <span style={{color:"red" , fontSize:"12px"}}>{this.state.experienceErrorMessage}</span> : null
                          }
                        </div>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="skills">
                        <Form.Label>Skills</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.skillData}
                          value={this.state.skill}
                          onChange={(e) => {this.handleDropdownData(e, "skill")}}
                          isSearchable
                          name="skill"
                          placeholder="Select Skills"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isMulti
                          // components={animatedComponents}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="designation">
                        <Form.Label> Designation </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"designation"}
                          options={this.state.designationList}
                          value={this.state.designation}
                          onChange={(e) => {this.handleDropdownData(e, "designation");}}
                          isSearchable
                          isFocused={false}
                          placeholder="Select Designation"
                          isOptionDisabled={(option) => option.label == "a"}
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="employeeId">
                        <Form.Label>Employee ID</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                          type="text"
                          name="employeeId"
                          value={this.state.employeeId}
                          onChange={(e)=>{this.inputValidation(e.target.value , "employeeId")}}
                          placeholder="Enter Employee ID"
                          maxLength="20"
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="employeeType">
                        <Form.Label> Employee Type </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"employeeType"}
                          options={constant.EmployeeType}
                          value={this.state.employeeType}
                          // onChange={(e) => {this.handleDropdownData(e, "employeeType");}}
                          onChange={this.handleEmployeeType}
                          isSearchable
                          isFocused={false}
                          placeholder="Select Employee Type"
                          isOptionDisabled={(option) => option.label == "a"}
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          // required
                        />
                        { this.state.employeeTypeErrorMessage !== "" && this.state.employeeTypeErrorMessage ? 
                            <span style={{color:"red" , fontSize:"12px"}}>{this.state.employeeTypeErrorMessage}</span> : null
                        }
                      </Form.Group>
                    </Col>
                    <Col md="3">
                      <Form.Group controlId="department">
                        <Form.Label>Department</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          
                          
                          options={this.state.departmentTempData.length > 0 ? this.state.departmentTempData :this.state.departmentData}
                        
                          value={this.state.edataDepartment}

                          onChange={(e) => {this.handleDropdownData(e, "edataDepartment")}}
                          isSearchable
                          name="department"
                          placeholder="Select Department"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // isDisabled={this.state.isDepartmentDisable}
                          // isMulti
                          // components={animatedComponents}
                          // required
                        />
                      </Form.Group>
                    </Col>

                  </Row>

                  {/* --------------------------------- TML company Details ------------------------------ */}
                  <div className="form-section">
                    <span>Reporting Manager And Company Details</span>
                  </div>

                  <Row className="form-grid">  

                    <Col md="3">
                        <Form.Group controlId="manager">
                          <Form.Label> Reporting Manager&apos;s Email </Form.Label><sup style={{color:'red'}}>*</sup>
                          <InputGroup className={this.state.sameManagerField ?"error-input mb-3 flex-direction" : "mb-3 flex-direction"}>
                            <InputGroup className="manager-loader">
                            <FormControl
                              type="text"
                              name="manager"
                              placeholder="Enter TML Email"
                              aria-label="Recipient's username"
                              aria-describedby="basic-addon2"
                              onChange={this.onChangeHandler}
                              disabled={this.state.managerLoder}
                              value={this.state.manager}
                              onBlur={this.managerDetails}
                              style={{width:'100%'}}
                            />
                            { this.state.managerLoder ? 
                              <div style={{paddingLeft:'5px'}}>
                                <Spinner size="sm" animation="border" role="status" variant="primary"/>
                              </div> : null
                            }
                            </InputGroup>
                          { this.state.CompleteName ? 
                              <span style={{color:"green" , fontSize:"12px" , position:"absolute" , top:'2.3rem'}}>{this.state.CompleteName}</span> : 
                              this.state.managerErrorMessage ? 
                              <span style={{color:"red" , fontSize:"12px" , position:"absolute" , top:'2.3rem'}}>{this.state.managerErrorMessage}</span> : null
                          }
                          </InputGroup>
                        </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="city To">
                        <Form.Label> City </Form.Label>{this.state.employeeType?.value === 'NONIT' ? null : <sup style={{color:'red'}}>*</sup> }
                        <Form.Control
                        type="text"
                        name="city"
                        disabled={this.state.employeeType?.value === "NONIT" ? false : true}
                        value={this.state.city}
                        onChange={(e)=>{this.alphabetValidations(e.target.value , "city")}}
                        placeholder=" Enter City"
                      />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="Department No">
                        <Form.Label> Department </Form.Label>{this.state.employeeType?.value === 'NONIT' ? null : <sup style={{color:'red'}}>*</sup> }
                        <Form.Control
                        type="text"
                        name="department"
                        disabled={this.state.employeeType?.value === "NONIT" ? false : true}
                        value={this.state.department}
                        onChange={(e)=>{this.alphabetValidations(e.target.value , "department")}}
                        placeholder=" Enter Department"
                      />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="tmlRegion">
                        <Form.Label> TML Region </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"tmlRegion"}
                          options={this.state.tmlRegionData}
                          value={this.state.tmlRegion}
                          onChange={(e) => {this.handleDropdownData(e, "tmlRegion");}}
                          isSearchable
                          isFocused={false}
                          name="tmlRegion"
                          placeholder="Select TML Region"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="tmlLocation">
                        <Form.Label> TML Location </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"tmlLocation"}
                          options={this.state.tmlLocationData}
                          value={this.state.tmlLocation}
                          onChange={(e) => {this.handleDropdownData(e, "tmlLocation");}}
                          isSearchable
                          isFocused={false}
                          name="tmlLocation"
                          placeholder="Select TML Location"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="buCompanyCode">
                        <Form.Label> BU Company Code </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"buCompanyCode"}
                          options={this.state.buCompanyCodeList}
                          value={this.state.buCompanyCode}
                          isDisabled={this.state.isBuDisabled}
                          onChange={(e) => {this.handleDropdownData(e, "buCompanyCode");}}
                          isSearchable
                          isFocused={false}
                          name="buCompanyCode"
                          placeholder="Select BU Company Code"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="swipeLocation">
                        <Form.Label> Swipe Location </Form.Label>
                        <Select
                          instanceId={"swipeLocation"}
                          options={this.state.swipeLocationOptions}
                          value={this.state.swipeLocation}
                          onChange={(e) => {this.handleDropdownData(e, "swipeLocation");}}
                          isSearchable
                          isFocused={false}
                          name="swipeLocation"
                          placeholder="Select Swipe Location"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          // required
                        />
                      </Form.Group>
                    </Col>
                    
                    <Col md="3">
                      <Form.Group controlId="SIO">
                        <Form.Label> SIO Code / BC Field</Form.Label>
                        <Form.Control
                        type="text"
                        name="sioCode"
                        value={this.state.sioCode}
                        onChange={(e)=>{this.inputValidation(e.target.value , "sioCode")}}
                        placeholder=" Enter SIO Code / BC Field"
                      />
                      </Form.Group>
                    </Col> 
                    <Col md="3">
  <Form.Group controlId="l2ManagerEmail">
    <Form.Label> L2 Manager Email </Form.Label><sup style={{color:'red'}}>*</sup>
    {/* <Form.Control
      type="email"
      name="l2ManagerEmail"
      value={this.state.l2ManagerEmail}
      onChange={(e) => this.setState({ l2ManagerEmail: e.target.value })}
      disabled={!this.state.l2FieldsEditable ? true : false}
      placeholder="Enter L2 Manager Email"
    /> */}
    <Form.Control
  type="email"
  name="l2ManagerEmail"
  value={this.state.l2ManagerEmail}
  onChange={(e) => this.setState({ l2ManagerEmail: e.target.value })}
  onBlur={this.state.source === "AD" ? this.fetchL2ManagerDetails : undefined}
  disabled={!this.state.l2FieldsEditable}
  placeholder="Enter L2 Manager Email"
/>

  </Form.Group>
</Col> 
                    <Col md="3">
  <Form.Group controlId="l2ManagerName">
    <Form.Label> L2 Manager Name </Form.Label><sup style={{color:'red'}}>*</sup>
    <Form.Control
      type="text"
      name="l2ManagerName"
      value={this.state.l2ManagerName}
      onChange={(e) => this.setState({ l2ManagerName: e.target.value })}
      disabled={!this.state.l2FieldsEditable ? true : false}
      placeholder="Enter L2 Manager Name"
    />
  </Form.Group>
</Col>

                   
                  </Row>

                  {/* ------------------------------------ Vendor Details ---------------------------------------- */}
                  <div className="form-section">
                    <span>Vendor Details</span>
                  </div>

                  <Row className="form-grid">

                  <Col md="3">
                        <Form.Group controlId="Vendor">
                          <Form.Label> Vendor / Company Name </Form.Label><sup style={{color:'red'}}>*</sup>
                          <Select
                            instanceId={"Vendor"}
                            options={this.state.vendorOption}
                            value={this.state.vendor}
                            onChange={(e) => {this.handleDropdownData(e, "vendor")}}
                            isSearchable
                            isFocused={false}
                            name="vendor"
                            placeholder="Select Vendor Company"
                            className="myclass"
                            noOptionsMessage={({ inputValue }) =>
                              "No results found"
                            }
                            isDisabled={this.props.isTmlEmployee === true ? false : true}
                            // required
                          />
                        </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="projectName">
                        <Form.Label>Project Name</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.project}
                          value={this.state.projectName}
                          onChange={(e) => {this.handleDropdownData(e, "projectName")}}
                          isSearchable
                          name="projectName"
                          placeholder="Select Project Name"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          // required
                        />
                      </Form.Group>
                    </Col>
                    {this.state.projectName?.value === 'OTH' ?
                      <Col md="3">
                        <Form.Group controlId="manager">
                          <Form.Label> Create New Project </Form.Label><sup style={{ color: 'red' }}>*</sup>
                          <InputGroup className={this.state.sameProjectField ? "error-input mb-3 flex-direction" : "mb-3 flex-direction"}>
                            <InputGroup className="manager-loader">
                              <FormControl
                                type="text"
                                name="project"
                                placeholder="Enter Project name"
                                aria-label="Recipient's username"
                                aria-describedby="basic-addon2"
                                onChange={this.onChangeHandler}
                                disabled={this.state.projectLoader}
                                value={this.state.projectDetail}
                                onBlur={this.checkProjectDetails}
                                style={{ width: '100%' }}
                              />
                              {this.state.projectLoader ?
                                <div style={{ paddingLeft: '5px' }}>
                                  <Spinner size="sm" animation="border" role="status" variant="primary" />
                                </div> : null
                              }
                            </InputGroup>
                            {
                              this.state.projectSuccessMessage ?
                                <span style={{ color: "green", fontSize: "12px", position: "absolute", top: '2.3rem' }}>{this.state.projectSuccessMessage}</span> :
                                this.state.projectErrorMessage ?
                                  <span style={{ color: "red", fontSize: "12px", position: "absolute", top: '2.3rem' }}>{this.state.projectErrorMessage}</span> : null
                            }
                          </InputGroup>
                        </Form.Group>
                      </Col>
                    : null}

                    <Col md="3">
                      <Form.Group controlId="vendorManagerEmail">
                        <Form.Label>Vendor Manager&apos;s Email</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                          type="email"
                          name="vendorManagerEmail"
                          value={this.state.vendorManagerEmail}
                          onChange={(e) => {this.onChangeEmailHandler(e.target.value , this.state.vendorEmailMessage , "vendorEmailMessage" , 'vendorManagerEmail')}}
                          onBlur={()=>{this.emailValidations(this.state.vendorManagerEmail , "vendorEmailMessage" , "vendorManagerEmail")}}
                          placeholder="Enter Vendor Manager Email"
                          // required
                        />
                         { this.state.vendorEmailMessage !== "" && this.state.vendorEmailMessage ? 
                            <span style={{color:"red" , fontSize:"12px"}}>{this.state.vendorEmailMessage}</span> : null
                          }
                      </Form.Group>
                    </Col>
                  
                    <Col md="3">
                      <Form.Group controlId="vendorManager">
                        <Form.Label>Vendor Manager&apos;s Name</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                          type="text"
                          name="vendorManager"
                          value={this.state.vendorManager}
                          onChange={(e)=>{this.alphabetValidations(e.target.value , "vendorManager")}}
                          placeholder="Enter Vendor Manager Name"
                          // required
                        />
                      </Form.Group>
                    </Col>

                    {
                      this.props.vendor === 'TCS' || this.state.isTowerShown ? 
                      <Col md="3">
                      <Form.Group controlId="towerName">
                        <Form.Label>Tower Name</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.towerData}
                          value={this.state.towerName}
                          onChange={(e) => {this.handleDropdownData(e, "towerName")}}
                          isSearchable
                          name="towerName"
                          placeholder="Select Tower Name"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          // required
                        />
                      </Form.Group>
                    </Col> : null
                    
                    }
                    
                  </Row> 

                  {/* ----------------------------------- Account Type --------------------------------- */}
                  <div className="form-section">
                    <span>Account Type</span>
                  </div>

                  <Row className="form-grid">   
                  <Col md="3">
                      <Form.Group controlId="validityFrom">
                        <Form.Label> Valid From </Form.Label><sup style={{color:'red'}}>*</sup>
                        <DatePicker
                          defaultValue={this.state.validityFrom}
                          onChange={(e) => {this.handleDate(e, "validityFrom")}}
                          value={this.state.validityFrom}
                          name="validityFrom"
                          dateFormat="yyyy-MM-dd"
                          placeholderText="Select Valid From"
                          className="form-control display-block"
                          maxDate={this.state.validityTo}
                          minDate={this.state.validityTo ? this.state.validityTo.subtract(ExpiryMonthRange , 'month') : null}
                          style={{fontFamily : 'sans-serif'}}
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="validityTo">
                        <Form.Label> Valid till </Form.Label><sup style={{color:'red'}}>*</sup>
                        <DatePicker
                          defaultValue={this.state.validityTo ? this.state.validityTo : null}
                          onChange={(e) => {this.handleDate(e, "validityTo")}}
                          value={this.state.validityTo}
                          name="validityTo"
                          dateFormat="yyyy-MM-dd"
                          placeholderText="Select Valid To"
                          className="form-control"  
                          minDate={this.state.validityFrom}
                          maxDate={this.state.validityFrom?.add(ExpiryMonthRange,'month')}
                          style={{fontFamily : 'sans-serif'}}
                          // minDate={this.state.validityFrom}
                        />
                      </Form.Group>
                    </Col>

                    <Col md="6">
                      <Form.Group controlId="accountType">
                        <Form.Label>Account Type</Form.Label>
                        {["checkbox"].map((type) => (
                          <div key={`inline-${type}`} className="">
                            <Form.Check
                              type={type}
                              label="Domain ID"
                              id="Domain"
                              name="accountType"
                              defaultChecked={this.state.checked}
                              inline
                              value="Domain"
                              onChange={this.handleCheck}
                              disabled={true}
                            />
                            <Form.Check
                              inline
                              type={type}
                              label="Email Account"
                              name="accountType"
                              id="emailAccount"
                              value="Email"
                              onChange={this.handleCheck}
                              checked={this.state.email}
                            />
                            <Form.Check
                              inline
                              type={type}
                              label="Internet"
                              id="Internet"
                              name="accountType"
                              value="Internet"
                              onChange={this.handleCheck}
                              checked={this.state.internet}
                            />
                          </div>
                        ))}
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row className="form-grid">
                    <Col md="12">
                      <Form.Group className="text-center" controlId="Submitbtn">
                        <Button disabled={this.state.disable} className="primary-button" onClick={()=>{this.handleSubmit()}}>Create</Button>
                        <Button className="reset-button" onClick={()=>{this.resetForm()}}>Reset</Button>
                      </Form.Group>
                    </Col>
                  </Row>
                  <h2 className="card-title row" style={{color:'red',fontSize:15}}>Disclaimer : Kindly refrain from onboarding the international partner employee until security clearances are obtained. </h2>
                </div>
              </div>
            </Form>
            {/* </CardBox> */}
          </div>
        </div>
      </div>

      {
        this.state.isLoading ?
        <div className="loader">
          <div></div>
        </div> : null
      }
      </>
    );
  }
}
const mapStateToProps = (state) => ({
  counter: state.counter.value,
  baseUrl: state.counter.baseUrl,
  maxDay: state.counter.maxDay,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
  vendor : state.loginInfo.login.vendorName,
  isTmlEmployee : state.loginInfo.login.isTmlEmployee,
  requestType : state.counter.requestType
});

const mapDispatchToProps = {
  incrementCounter: incrementCounter,
  decrementCounter: decrementCounter,
  step1: step1,
  login:login
};
export default withRouter(connect(mapStateToProps, mapDispatchToProps)(Request));
