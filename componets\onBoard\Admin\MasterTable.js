import { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { Tabs, Tab, ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form } from 'react-bootstrap';
import { login, logOut } from "../../../redux/actions/loginActions";
import BootstrapTable from 'react-bootstrap-table-next';
import { ReactNotifications, Store } from "react-notifications-component";
import { MasterModalType, MasterType, RequestType, sizePerPage,admin_module,admin_action, admin_master_list,admin_master,admin_add_master,admin_update_master } from "../../../constants/constants";
import paginationFactory from 'react-bootstrap-table2-paginator';
import ToolkitProvider, { Search } from "react-bootstrap-table2-toolkit";
import { getAPIResponse } from '../../../constants/Utils';
import * as config from '../../../constants/config'
import AdminTable from './AdminTable';
import { Select,Switch } from 'antd'
import { FaRegEdit,FaArrowLeft,FaArrowRight,FaPlus  } from "react-icons/fa";
import { AiOutlineClose } from "react-icons/ai";
import { successToast, errorToast } from "../../../toast"
import { PLEASE_ENTER_REQUIRED_FIELD, SOMETHING_WENT_WRONG,PLEASE_SELECT_VENDOR_COMPANY, PLEASE_SELECT_REGION } from '../../../constants/message';
const { SearchBar } = Search;


class MasterTable extends Component {

  static getInitialProps({ store }) { }
  constructor(props) {
    super(props);
    this.state = {
      activeKey: "Indents",
      defaultHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "vendorCode", text: "Vendor ID", sort: true },
        { dataField: "vendorName", text: "Vendor Name", sort: true },
        { dataField: "isGroupNDASigned", text: "Group NDA", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      vendorHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "vendorCode", text: "Vendor ID", sort: true },
        { dataField: "vendorName", text: "Vendor Name", sort: true },
        {dataField: "isGroupNDASigned", text: "Group NDA", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      designationHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "designationCode", text: "Designation ID", sort: true },
        { dataField: "designationName", text: "Designation Name", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      skillHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "skillCode", text: "Skill ID", sort: true },
        { dataField: "skillName", text: "Skill Name", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      towerHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "towerCode", text: "Tower ID", sort: true },
        { dataField: "towerName", text: "Tower Name", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      projectHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "vendor", text: "Vendor", sort: true },
        { dataField: "projectCode", text: "Project ID", sort: true },
        { dataField: "projectName", text: "Project Name", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      tmlRegionHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "regionCode", text: "Region ID", sort: true },
        { dataField: "regionName", text: "Region Name", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      tmlOfficeHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "region", text: "Region Name", sort: true },
        { dataField: "officeCode", text: "Office ID", sort: true },
        { dataField: "officeName", text: "Office Name", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      departmentHeaders: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "departmentCode", text: "Department ID", sort: true },
        { dataField: "departmentName", text: "Department Name", sort: true },
        { dataField: "empType", text: "Department Type", sort: true },
        { dataField: "isActive", text: "Status", sort: true },
        { dataField: "Action", text: "Action", formatter: this.linkFollow, sort: true }
      ],
      currentPage: 0,
      currentPageData: [],
      sizePerPage: sizePerPage,
      totalSize: 0,
      searchText: '',
      selectOptions: MasterType,
      requestType: "Vendor",
      selectedValue: "vendor",
      vendorOption: [],
      designationList: [],
      project: [],
      tmlLocationData: [],
      tmlRegionData: [],
      skillData: [],
      towerData: "",
      vendor: null,
      prev: null,
      next: null,
      pages: 1,
      totalRecords: 0,
      showModal: false,
      selectedModalValue: { label: 'Vendor', value: 'vendor' },
      selectModalOptions: MasterModalType,
      isMasterEnable: true,
      masterModalName:'',
      masterModalCode:'',
      masterModalVendor:'',
      masterModalType:'add',
      vendorMasterList:[],
      selectedVendor:{},
      regionMasterList:[],
      selectedRegion:{},
      isMasterSelectionDisabled:false,
      invalidAddButton:[],
      prevMasterData:{},
      isGroupNDASigned:false,
      masterNameErrorMessage:"",
      masterCodeErrorMessage:"",
      projectErrorMessage:"",
      isLoading:false,
      saveDisabled:false,
      isEnable:['vendor','region','office'],
      departmentType:[
        {
          value: 'IT',
          label: 'IT'
        },
        {
          value: 'NONIT',
          label: 'Non IT'
        }
      ],
      selectedDepartment:'IT'
    }
    this.updateMaster.bind(this); 
  }

  componentDidMount() {
    this.getMasterList()
    this.getMasterList({"master": "vendor","action": admin_master_list,"module": admin_master })
    this.getMasterList({"master": "tmlRegion","action": admin_master_list,"module": admin_master })
  }

  setDropDownList=(state,defaultSelect,masterData) =>{
    let masterList = []
    if (masterData.length > 0){
      masterData.forEach((element) => {
        if (element['isActive']) {
          if (state == 'vendorMasterList'){
            masterList.push({
              value: element.vendorCode,
              label: element.vendorName
            })
          }
          else{
            masterList.push({
              value: element.regionCode,
              label: element.regionName
            })
          }
        }
      })
      this.setState({ [state]: masterList, [defaultSelect]: masterList[0].value })
    }
  }
  getMasterList = (req_body={}) => {
    this.setState({
      isLoading : true
    })
    let body = {
      "master": this.state.selectedValue,
      "search_text": this.state.searchText,
      "page": this.state.pages,
      "action": admin_master_list,
      "module": admin_master 
    }
    if (Object.keys(req_body).length > 0) body = req_body
    getAPIResponse(config.adminMasterList, "POST", body)
      .then((response) => {
        if (!response.ok) {
          console.log("getMasterList() in Request", response)
          return null
        }
        else {
          return response.json()
        }
      }).then((data) => {
        if (data !== null) {
          let masterData = data.data.results
          if (Object.keys(req_body).length > 0 && (body['master'] == 'vendor' || body['master'] == 'tmlRegion')){
            // console.log("data45:",data.data)
            masterData = data.data
            if (body['master'] == 'vendor'){
              this.setDropDownList('vendorMasterList','selectedVendor',masterData)
            }
            else{
              this.setDropDownList('regionMasterList','selectedRegion',masterData)
            }
          }
          else{
            masterData.forEach((element, index) => {
              let isMasterActive = element['isActive']
              element["isGroupNDASigned"] = element['isGroupNDASigned'] ? "Yes" : "No"
              element['srno'] = (this.state.pages - 1) * 10 + index + 1
              if (!this.state.invalidAddButton.includes(this.state.selectedValue)) {
                element['isActive'] = <Switch disabled checked={element['isActive']} />
                element['Action'] = <FaRegEdit onClick={() => { this.updateMaster(element, this.state.selectedValue, isMasterActive) }} style={{ display: 'flex', alignItems: 'center', cursor:'pointer'}} size={'1.5rem'} fill='#6392c5' />
              }
            });
            this.setState({
              currentPageData: masterData,
              prev: data.data.previous != null,
              next: data.data.next != null,
              totalRecords:data.data.count
            })
          }
        }

        else {
          this.setState({
            currentPageData: [],
            prev: null,
            next: null,
            totalRecords:0
          })
          console.log("getMasterList() in Request", response)
        }
      })
      .catch((error) => {
        console.log("getMasterList() in Request", error)
      })
      this.setState({
        isLoading : false
      })
  }

  handleInputChange = (event) => {
    this.setState({
      searchText: event.target.value,
      pages:1
    },()=>{
      if(this.state.searchText.length >= 3){
        this.getMasterList()
      }
      else if(this.state.searchText.length <= 0){
        this.getMasterList()
      }
    });
    
  };

  clearInput = () => {
    this.setState({ searchText: '', pages: 1 });
    this.getMasterList()
  };


  updateMaster = (value,master,isEnable) => {
    if (master === 'designations') master = 'designation'    
    if (master === 'tmlOffice') master = 'office'
    if (master === 'tmlRegion') master = 'region'
    let masterName = master + 'Name'
    let masterCode = master + 'Code'

    this.setState({
      isMasterEnable: isEnable,
      masterModalCode:value[masterCode],
      masterModalName:value[masterName],
      isMasterSelectionDisabled:true,
      showModal:true,
      masterModalType:'update',
      selectedModalValue:{ label: master.charAt(0).toUpperCase() + master.slice(1), value: master },
      isGroupNDASigned:value?.isGroupNDASigned === "Yes" ? true : false,
    })

    let masterPrevActive = isEnable
    let masterPrevName = value[masterName]
    let masterPrevCode = value[masterCode]

    this.setState({prevMasterData:
      {
        "isActive": masterPrevActive,
      [masterName]: masterPrevName,
      [masterCode]: masterPrevCode
    }
    },()=>{
      if (master === 'vendor') {
        this.setState({
          prevMasterData:{
            ...this.state.prevMasterData,
            "vendor":this.state.selectedVendor?.value,
            "isGroupNDASigned": value?.isGroupNDASigned === "Yes" ? true : false,
          }
        })
      }
      if (master === 'office') {
        this.setState({
          selectedRegion: value['region'],
          prevMasterData:{
            ...this.state.prevMasterData,
            "region_id":this.state.selectedRegion?.value
          }
        })
      }
      if (master === 'department') {
        this.setState({
          selectedDepartment: value['empType'],
          prevMasterData:{
            ...this.state.prevMasterData,
            "empType":this.state.selectedDepartment
          }
        })
      }
      if (master === 'project') {
        this.setState({
          selectedVendor: value['vendor'],
        })
      }
    })
    
  }

  addMaster = (master) => {
    this.setState({
      showModal:true,
      masterModalType:'add',
      selectedModalValue:{ label: master.charAt(0).toUpperCase() + master.slice(1), value: master }
    })
  }

  updateState = (key, value) => {
    this.setState({ [key]: value });
  };

  // Handle input change
  handleModalInput = (e) => {
    const { name, value } = e.target;
    this.updateState(name, value.trimStart());
  };

  handleModalClosure(){
    this.setState({
      isMasterEnable: true,
      masterModalCode:'',
      masterModalName:'',
      isMasterSelectionDisabled:false,
      showModal:false,
      masterModalType:'add',
      selectedModalValue:{ label: 'Vendor', value: 'vendor' },
      isGroupNDASigned:false,
      masterCodeErrorMessage:"",
      masterNameErrorMessage:"",
      prevMasterData:{}
    })
  }

  isInvalid = (obj) => obj == undefined || obj == null || obj == 'NA' || obj == '-'

  CheckEmptyString = (str = "") => !this.isInvalid(str) && str != "" && str.trim() !== "" ? true : false

  handleAddUpdateMaster(master){
    // const regex = /^[a-zA-Z0-9]+$/;
    const regex = /^[A-Za-z0-9\s\-.,_\/\\&]+$/;
    this.setState({
      isLoading : true,
      saveDisabled:true
    })
    if (!this.CheckEmptyString(this.state.masterModalName) || !this.CheckEmptyString(this.state.masterModalCode)){
      errorToast(PLEASE_ENTER_REQUIRED_FIELD)
    }
    else if(master.toLowerCase() == 'project' && !this.CheckEmptyString(this.state.selectedVendor)){
      errorToast(PLEASE_SELECT_VENDOR_COMPANY)
    }
    else if(master.toLowerCase() == 'tmlOffice' && !this.CheckEmptyString(this.state.selectedRegion)){
      errorToast(PLEASE_SELECT_REGION)
    }
    else{
      let isValidName = regex.test(this.state.masterModalName)
      let isValidCode = regex.test(this.state.masterModalCode)
      if(!isValidName || !isValidCode){
        if(!isValidName) this.setState({masterNameErrorMessage:"Please enter valid master name"}) 
        if(!isValidCode) this.setState({masterCodeErrorMessage:"Please enter valid master code"}) 
        this.setState({
          isLoading : false,
          saveDisabled:false
        })
      }
      else{
        if (master === 'designations') master = 'designation'
        if (master === 'tmlOffice') master = 'office'
        if (master === 'tmlRegion') master = 'region'
        let masterName = master + 'Name'
        let masterCode = master + 'Code'
        let body = {
          "updateMaster":{
            "isActive": master === 'vendor' && this.state.masterModalType === 'add' ? true : this.state.isMasterEnable ,
            [masterName]: this.state.masterModalName,
            [masterCode]: this.state.masterModalCode.toUpperCase(),
            "vendor":this.state.selectedVendor,
            "region":this.state.selectedRegion,
            "empType":this.state.selectedDepartment,
            isGroupNDASigned:this.state.isGroupNDASigned
          },
          "previousMaster":this.state.prevMasterData,
          "operation": this.state.masterModalType,
          "action": this.state.masterModalType === 'add' ? admin_add_master : admin_update_master,
          "module": admin_master 
        }
        if(master !== "project"){
          delete body.updateMaster.vendor
          delete body.previousMaster.vendor
        }
        if(master !== "vendor"){
          delete body.updateMaster.isGroupNDASigned
          delete body.previousMaster.isGroupNDASigned
        }
        if(master !== "tmlOffice"){
          delete body.updateMaster.region_id
          delete body.previousMaster.region_id
        }
        if(master !== "department"){
          delete body.updateMaster.empType
          delete body.previousMaster.empType
        }
        let apiUrl = config.addUpdateVendor
        switch(this.state.selectedValue){
          case "designations":
            apiUrl = config.addUpdateDesignation
            break;
          case "skill":
            apiUrl = config.addUpdateSkills
            break;
          case "project":
            apiUrl = config.addUpdateProject
            break;
          case "tower":
            apiUrl = config.addUpdateTower
            break;
          case "tmlOffice":
            apiUrl = config.addUpdatetmlOffice
            break;
          case "tmlRegion":
            apiUrl = config.addUpdatetmlRegion
            break;
          case "department":
            apiUrl = config.addUpdatedepartment
                break;
          default:
            apiUrl = config.addUpdateVendor
            break;
        }
        getAPIResponse(apiUrl, "POST", body)
        .then((response) => {
          if (response.status===500) {
            console.log("handleAddUpdateMaster() in Request", response)
            return null
          }
          else {
            return response.json()
          }
        }).then((data) => {
          if (data.status === 1) {
            this.setState({
              showModal:false,
              isLoading : false,
              saveDisabled:false
            },()=>{this.getMasterList()})
            successToast(data.message)
            this.handleModalClosure()
          }
          else {
            errorToast(data.message)
          }
        })
        .catch((error) => {
          console.log("handleAddUpdateMaster() in error", error)
          errorToast(SOMETHING_WENT_WRONG)
        })
        // this.handleModalClosure()
      }
    }
    this.setState({
      isLoading : false,
      saveDisabled:false
    })
  }

  goNext(row) {
    this.props.setId({ requestId: row.id });
    Router.push({
      pathname: "/UpdateIdPage",
    });
  }

  linkFollow = (cell, row, rowIndex, formatExtraData) => {
    return (
      <div>
        <a onClick={() => { this.goNext(row); }} style={{ cursor: "pointer", marginRight: "2px" }} className="btn-info btn-xs" target="_blank"><i className="fa fa-eye"></i>
        </a>
      </div>
    )
  }

  setHeader = () =>{
    switch(this.state.selectedValue){
      case "designations":
        this.setState({ defaultHeaders: this.state.designationHeaders })
        break;
      case "tmlRegion":
        this.setState({ defaultHeaders: this.state.tmlRegionHeaders })
        break;
      case "tmlOffice":
        this.setState({ defaultHeaders: this.state.tmlOfficeHeaders })
        this.getMasterList({"master": "tmlRegion","action": admin_master_list,"module": admin_master })
        break;
      case "skill":
        this.setState({ defaultHeaders: this.state.skillHeaders })
        break;
      case "project":
        this.setState({ defaultHeaders: this.state.projectHeaders })
        this.getMasterList({"master": "vendor","action": admin_master_list,"module": admin_master })
        break;
      case "tower":
        this.setState({ defaultHeaders: this.state.towerHeaders })
        break;
      case "department":
        this.setState({ defaultHeaders: this.state.departmentHeaders })
          break;
      default:
        this.setState({defaultHeaders:this.state.vendorHeaders})
        break;
    }
    this.getMasterList()
  }
  // updateBodyClass() {
  //   if (this.state.showModal) {
  //     document.body.classList.add("overflow-y-hidden");
  //   } else {
  //     document.body.classList.remove("overflow-y-hidden");
  //   }
  // }
  // componentDidUpdate(prevProps, prevState) {
  //   if (prevState.showModal !== this.state.showModal) {
  //     this.updateBodyClass();
  //   }
  // }
  render() {
    console.log("this.state:",this.state)
    return (
      <Fragment>
        <ReactNotifications />
        <div className="border content-wrapper justify-space-between">
          <div className="text-right text-space-between">
              <Select
                options={this.state.selectOptions}
                value={this.state.selectedValue}
                onChange={(e) => { this.setState({ requestType: e, selectedValue: e, pages: 1 }, () => { this.setHeader() }) }}
                isSearchable
                name="requestType"
                placeholder="Select Master Table"
                className="select-option"
                style={{height:"42.5px"}}
              />
              <div className='add-search-vendor'>
                <div className="search-bar">
                  <input 
                    type="text" 
                    placeholder={`Search by ${MasterType.find(o=>o.value === this.state.selectedValue)?.label} Name`}
                    value={this.state.searchText} 
                    onChange={(event)=>{this.handleInputChange(event)}} 
                  />
                  {this.state.searchText && (
                    <span className="clear-iconV2" onClick={this.clearInput}>x</span>
                  )}
                </div>
              
                {!this.state.invalidAddButton.includes(this.state.selectedValue)?
                  <div className='button-paginate' onClick={()=>{this.addMaster(this.state.selectedValue)}}>
                  <FaPlus style= {{display:'inline-block', alignItems:'center'}} size={'0.8rem'}/> 
                    {" Add"}
                </div>
                :null}
              </div>
              
            </div>

            <AdminTable headers={this.state.defaultHeaders} currentPageData={this.state.currentPageData} />

          <div className='fixed-width'>
            <div className="paginator-space">
              <div className='flex-d' >
                <div className='page-label'>
                  {`Total Record: ${this.state.totalRecords}`}
                </div>
              </div>
              <div className='pagination'>
              <div className={this.state.prev === true  ? 'button-paginate' : 'button-paginate  disabled'} onClick={this.state.prev === true ? () => { this.setState({ pages: this.state.pages - 1, isLoading: true }, () => { this.getMasterList() })} : null}>
                <FaArrowLeft style= {{display:'inline-block', alignItems:'center'}} size={'0.8rem'}/> 
                  {" Previous"}
              </div>
                <div className={'page-label'}>{this.state.pages}</div>
                <div className={this.state.next === true  ? 'button-paginate' : 'button-paginate disabled'} onClick={this.state.next === true ? () => { this.setState({ pages: this.state.pages + 1, isLoading: true }, () => { this.getMasterList() })} : null}>
                  {"Next "}
                  <FaArrowRight style= {{display:'inline-block', alignItems:'center'}} size={'0.8rem'}/> 
              </div>
              </div>
            </div>
          </div>
        </div>

        {/* Add Update Modal */}
        {this.state.showModal?
          <>
            <div className="popup-wrapper"></div>
            <div className="popup-center">
              <div className="popup-container">
                <div style={{ width: "100%" }}>
                  <div className="close-popup" onClick={()=>{this.handleModalClosure()}}>
                    <AiOutlineClose />
                  </div>
                  <h3 style={{ textAlign: "center", fontWeight: "bolder" }}>{this.state.masterModalType == 'add'? "Add Records" : "Update Records"}</h3>

                </div>
                <div className="popup-form">
                  <div className="popup-input">
                    <label className="popup-lable">Master Table Name</label>
                    <div className="input-box">
                      <Select
                        options={this.state.selectModalOptions}
                        value={this.state.selectedModalValue}
                        onChange={(e) => { this.setState({selectedModalValue: e}) }}
                        isSearchable
                        name="requestType"
                        placeholder="Select Master Table"
                        disabled={true}
                        style={{width:"100%",height:"42.5px"}}
                      />
                    </div>
                  </div>
                </div>

                { this.state.selectedModalValue.value =='project'?
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">Vendor<span style={{color:'red', fontWeight:'900'}}>*</span>
                      </label>
                        <div className="input-box">
                          <Select
                            options={this.state.vendorMasterList}
                            value={this.state.selectedVendor}
                            onChange={(e) => { this.setState({selectedVendor: e}) }}
                            isSearchable
                            name="requestType"
                            placeholder="Select Vendor"
                            style={{height:"42.5px",width:"100%"}}
                          />
                        </div>
                        {this.state.projectErrorMessage ? <span className="error-message">{this.state.projectErrorMessage}</span> : null}
                    </div>
                  </div>
                  :null}
                <div className='display-flex-column' style={{width:"100%"}}>
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">Name<span style={{color:'red', fontWeight:'900'}}>*</span>  
                      </label>
                      <div className="input-box">
                        <input 
                          name="masterModalName"
                          placeholder="Please Enter Name"
                          value={this.state.masterModalName}
                          onChange={(e)=>{this.handleModalInput(e)}} 
                          maxLength={100} 
                        />
                      </div>
                      {this.state.masterNameErrorMessage ? <span className="error-message">{this.state.masterNameErrorMessage}</span> : null}
                    </div>
                  </div>
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">Code/ID<span style={{color:'red', fontWeight:'900'}}>*</span>  
                      </label>
                      <div className="input-box">
                        <input 
                          name="masterModalCode"
                          placeholder="Please Enter Code"
                          value={this.state.masterModalCode}
                          onChange={(e)=>{this.handleModalInput(e)}} 
                          disabled={this.state.masterModalType==='update'}
                        />
                      </div>
                      {this.state.masterCodeErrorMessage ? <span className="error-message">{this.state.masterCodeErrorMessage}</span> : null}
                    </div>
                  </div>
                </div>
                <div className='display-flex-column' style={{width:"100%"}}>
                  {this.state.selectedModalValue.value == "vendor" ? 
                    <div className="popup-form">
                      <div className="popup-input">
                        <label className="popup-lable">Group NDA Signed</label>
                        <div className="input-box">
                          <Switch defaultChecked={this.state.isGroupNDASigned} onChange={(e)=>{this.setState({isGroupNDASigned:e})}}/>
                        </div>
                      </div>
                  </div> : null
                  }
                  { this.state.selectedModalValue.value == 'tmlOffice' || this.state.selectedModalValue.value == "office"?
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">Region<span style={{color:'red', fontWeight:'900'}}>*</span>
                      </label>
                        <div className="input-box">
                          <Select
                            options={this.state.regionMasterList}
                            value={this.state.selectedRegion}
                            onChange={(e) => { this.setState({selectedRegion: e}) }}
                            isSearchable
                            name="requestType"
                            placeholder="Select Region"
                            style={{height:"42.5px",width:"100%"}}
                          />
                        </div>
                        {this.state.projectErrorMessage ? <span className="error-message">{this.state.projectErrorMessage}</span> : null}
                    </div>
                  </div>
                  :null}

                  {this.state.selectedModalValue.value == "department" ?
                    <div className="popup-form">
                      <div className="popup-input">
                        <label className="popup-lable">Department Type<span style={{color:'red', fontWeight:'900'}}>*</span>
                        </label>
                        <div className="input-box">
                          <Select
                            options={this.state.departmentType}
                            value={this.state.selectedDepartment}
                            onChange={(e) => { this.setState({ selectedDepartment: e }) }}
                            isSearchable
                            name="requestType"
                            placeholder="Select Region"
                            style={{ height: "42.5px", width: "100%" }}
                          />
                        </div>
                        {/* {this.state.projectErrorMessage ? <span className="error-message">{this.state.projectErrorMessage}</span> : null} */}
                      </div>
                    </div>
                    : null}
                  <div className="popup-form">
                    <div className="popup-input">
                      <label className="popup-lable">Enable</label>
                      <div className="input-box">
                        {
                          // this.state.selectedModalValue.value == "vendor" ?
                          this.state.isEnable.includes(this.state.selectedModalValue.value)?
                            this.state.masterModalType === 'add' ?
                              <Switch disabled={true} defaultChecked={true} onChange={(e) => { this.setState({ isMasterEnable: e }) }} />
                              :
                              <Switch disabled={true} defaultChecked={this.state.isMasterEnable} onChange={(e) => { this.setState({ isMasterEnable: e }) }} />
                            :
                            <Switch defaultChecked={this.state.isMasterEnable} onChange={(e) => { this.setState({ isMasterEnable: e }) }} />
                        }
                      </div>
                    </div>
                  </div>
                </div>
                <div className="save">
                  <button className="save-button" disabled={this.state.saveDisabled} onClick={()=>{this.handleAddUpdateMaster(this.state.selectedModalValue.value)}}>{this.state.masterModalType == 'add'? "Add" : "Update"}</button>
                </div>
              </div>
            </div>
          </>
        :null
        }
        {
          this.state.isLoading ?
            <div className="loader">
              <div></div>
            </div> : null
        }
      </Fragment>
    )
  }
}


const mapStateToProps = state => ({
  counter: state.counter.value,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login
});

const mapDispatchToProps = {
  login: login
};

export default connect(mapStateToProps, mapDispatchToProps)(MasterTable);
