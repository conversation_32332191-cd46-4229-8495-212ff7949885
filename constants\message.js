//  -------------------------------------------- SEARCH PARAMETERS --------------------------------------

export const INVITE             = "invites"
export const ASSIGNEDREQUEST    = "assigned"
export const FULLFILLEDREQUEST  = "fulfilled"
export const REJECTEDREQUEST    = "rejected"
export const CANCELLEDREQUEST   = "cancelled"
export const ExpiryMonthRange   =  6
export const CANCELREQUEST      = "Cancel"
export const ACCEPTREQUEST      = "Accept"
export const REJECTREQUEST      = "Reject"
export const APPROVEDREQUEST    = "approved"
export const PENDING            = "pending"
export const Disable            = "disable"
export const EXTENSION          = "extension"

//  ------------------------------------------------- API MESSAGES -------------------------------------------

export const INTERNAL_SERVER_ERROR = 'Internal Server Error'
export const SOMETHING_WENT_WRONG  = "Something Went Wrong"
export const NDA_FORM_REJECTED_SUCCESSFULLY =  "NDA Form Rejected Successfully"


//  ------------------------------------------------- MESSAGE ------------------------------------------------

export const SPOC_REJECT_NOTE                   = 'SPOC Reject Note'
export const ADMIN_REJECT_NOTE                   = 'Admin Reject Note'
export const EMPLOYEE_REJECT_NOTE               = 'Employee Reject Note'
export const MANAGER_REJECT_NOTE                = 'Manager Reject Note'
export const SEARCH                             = 'search'
export const PAGINATIONS                        = 'pagination'
export const PLEASE_ENTER_VALID_EAMIL_ID        = "Please enter a valid email ID"
export const PLEASE_ENTER_VALID_MOBILE_NO       = "Please enter a valid mobile number"
export const YEARS_OF_EXPERIENCE_ERROR          = "Years of experience not be greater than 60"
export const NDA_NOT_SUBMITTED                  = "NDA Form Not Submitted"
export const PLEASE_ENTER_USERNAME              = "Please enter a username"
export const PLEASE_ENTER_PASSWORD              = "Please enter a password"
export const INVALID_USERNAME_AND_PASSWORD      = "Invalid Username and Password"
export const UNAUTHORIZED_USER                  = 'Unauthorized User'
export const LOGIN_SUCCESSFULLY                 = 'Login Successfully'
export const VERIFIED_SUCCESSFULLY              = "Verified Successfully"
export const PLEASE_ENTER_REQUIRED_FIELD        = "Please Enter Required Fields"
export const MANAGER_REQUESTER_CANNOT_SAME      = "Manager And Requestor Can Not Be Same"
export const MANAGER_VENDOR_CANNOT_SAME         = "TML Manager And Vendor Manager Can Not Be Same"
export const MANAGER_DETAILS_NOT_FOUND          = "Manager Details Not Found"
export const PERMISSION_DENIED_FOR_DELETE_OPERATION = "Permission denied for delete operation"
export const PLEASE_SELECT_VENDOR_COMPANY       = "Please select vendor company"
export const PLEASE_SELECT_REGION               = "Please select region"
export const PLEASE_ENTER_EMPLOYEE_DETAILS      = "Please enter employee details"
export const ERROR_MESSAGE_FOR_VALID_FROM       = "Valid From Date is greater than Valid Till Date"
export const PLEASE_ENTER_SAMACCOUNT_NAME       = "Please enter a SamAccount name"
export const PLEASE_ENTER_MANAGER               = "Please enter manager details"
export const PLEASE_VALIDATE_DOMAIN_ID          = "Please validate Domain ID"
export const FOR_IT_EMPLOYEES_MANAGR_SHOULD_BE_TML_MANAGER = "IT employees Manager should be TML Manager"
export const PLEASE_ENTER_MANAGER_EMAIL         = "Please Enter Manager Email"
export const PLEASE_SELECT_EMPLOYEE_TYPE        = "Please Select Employee Type"
export const SPOC_DATE_RANGE_DISCLAIMER         = "Date Range cannot be more then 3 Months"
export const MINDEFAULTVALUE                    = "2024-06-05"

// --------------------------------------------- NDA FORM MESSAGE ------------------------------------------------
export const DISCLAMIER                 = "I have read, understood and hereby, agree to the terms of the Confidentiality Agreement and hereby give consent for my personal data like firstname/lastname mobile number and employement details to be processed for the purposes and procedures"
export const LINK_EXPIRED_ERROR_MESSAGE = "Link has been expired kindly connect to requestor"
export const LOGIN_WITH_EMAILID_AND_OTP = "Login with EmailID and OTP to access a page."
export const MANAGER_DISCLAMIER         = "I have read, understood and hereby, agree to the terms of the Confidentiality Agreement"

// --------------------------------------------- NDA FORM MESSAGE ------------------------------------------------
export const PLEASE_ENTER_PROJECT_NAME         = "Please Enter Project Name"
export const INVALID_PROJECT                    = "Invalid Project Details"

// --------------------------------------------- Validate OTP ------------------------------------------------
export const SEND_EMAIL_OTP         = "OTP Send Over Email"
export const INVALID_EMAIL_OTP         = "Invalid Login OTP"
