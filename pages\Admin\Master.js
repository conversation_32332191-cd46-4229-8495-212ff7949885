import { Component } from 'react';
import Sidebar from '../../componets/Sidebar';
import Footer from '../../componets/Footer';
import Header from '../../componets/Header';
import AllTabs from '../../componets/onBoard/AllTabs';
import Router from 'next/router'
import Login from '../login';
import { connect } from 'react-redux';  
import { Tabs, Tab, ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form } from 'react-bootstrap';
import { login, logOut } from "../../redux/actions/loginActions";
import * as constants from '../../constants/constants'
import { pageAccessWithRole } from '../../constants/navigations';
import MasterTable from '../../componets/onBoard/Admin/MasterTable';


class Master extends Component {
    static getInitialProps({ store }) { }
    constructor(props) {
        super(props);
        this.state = {
            activeKey: "Indents"
        }
        //this.handleSubmit.bind(this); 
    }
    
    componentDidMount(){    
        let role = localStorage.getItem("role")
        if(!(role === 'isSuperUser')){
            window.location.replace(constants.FRONTEND_URL + '/login');
        }
    }


    render() {
        const isLoggedIn = this.props.isLogin;
        const createdBy=this.props.loginData.userRole
        let { router } = this.props;
        if (!isLoggedIn) {
            window.location.replace(constants.FRONTEND_URL + '/login');
            return null 
        } 
        else {
        return (
        //    <AllTabs/>
        <MasterTable/>
        // <h1>Master console</h1>
        )
    }
}
}

const mapStateToProps = state => ({
    counter: state.counter.value,
    baseUrl: state.counter.baseUrl, 
    isLogin: state.loginInfo.isLogin,
    loginData: state.loginInfo.login
});

const mapDispatchToProps = { 
    login:login
};
export default connect(mapStateToProps, mapDispatchToProps)(Master);
