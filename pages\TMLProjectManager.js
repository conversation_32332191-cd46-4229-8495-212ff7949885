import { Component } from "react";
import Sidebar from "../componets/Sidebar";
import Footer from "../componets/Footer";
import Header from "../componets/Header";
import { connect } from "react-redux";
import Router from "next/router";
import { decrementCounter, incrementCounter, step1, step2, step3, step4, step5, } from "../redux/actions/counterActions";
import Request from "../componets/onBoard/Request";

class TMLProjectManager extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      activeKey: "Indents",
    };
  }

  handleShow = (e) => {
    this.setState({
      activeKey: e,
    });
  };

  goTo = () => {
    this.props.step1({
      taskId: 0,
      processId: 0,
      step: 1,
      step1Validate: false,
      step1Data: {
        prvDoc: [],
        recallDescription: "",
        recallTitle: "",
        sorDescription: "",
      },
    });

    this.props.step1({
      taskId: 0,
      processId: 0,
      step: 1,
      step1Validate: false,
      step1Data: {
        prvDoc: [],
        recallDescription: "",
        recallTitle: "",
        sorDescription: "",
      },
    });

    this.props.step2({
      taskId: 0,
      processId: 0,
      step: 1,
      step2Validate: false,
      step2Data: {
        prvDoc: [],
        recallCccDescription: "",
      },
    });

    this.props.step3({
      taskId: 0,
      processId: 0,
      step: 1,
      step3Validate: false,
      step3Data: {
        prvDoc: [],
        recallCccDescription: "",
      },
    });
    
    this.props.step4({
      taskId: 0,
      processId: 0,
      step: 1,
      step4Validate: false,
      step4Data: {
        prvDoc: [],
        recallDecision: "",
        recallNote: "",
        contentBase64: {},
      },
    });

    this.props.step5({
      taskId: 0,
      processId: 0,
      step: 1,
      step5Validate: false,
      step5Data: {
        prvDoc: [],
        recComment: "",
      },
    });

    Router.push({
      pathname: "/RecallApp",
    });
  };

  render() {
    return (
      <Request baseUrl={this.props.baseUrl} />
    );
  }
}

const mapStateToProps = (state) => ({
  counter: state.counter.value,
  baseUrl: state.counter.baseUrl,
  Step1Taskid: state.counter.step1.taskIstanceIdStep1,
  Step1processId: state.counter.step1.processIstanceIdStep1,
});

const mapDispatchToProps = {
  incrementCounter: incrementCounter,
  decrementCounter: decrementCounter,
  step1: step1,
  step2: step2,
  step3: step3,
  step4: step4,
  step5: step5,
};
export default connect(mapStateToProps, mapDispatchToProps)(TMLProjectManager);
