import { Component } from "react";
import Sidebar from "../componets/Sidebar";
import Footer from "../componets/Footer";
import Header from "../componets/Header";
import { connect } from "react-redux";
import Request from "../componets/onBoard/Request";
import Link from "next/link";
import { encode } from "base-64";
import Router from "next/router";
import Login from "./login";
import { decrementCounter, incrementCounter, step1, step2, step3, step4, step5, } from "../redux/actions/counterActions";
//import { decrementCounter, incrementCounter, step1 } from '../redux/actions/counterActions';
import { Tabs, Tab, ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form, } from "react-bootstrap";
import { login, logOut } from "../redux/actions/loginActions";
import * as constants from '../constants/constants'
import { setRequestType } from "../redux/actions/counterActions";

class OnBoarding extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      activeKey: "Indents",
    };
    //this.handleSubmit.bind(this);
  }

  componentDidMount(){
    let role = localStorage.getItem("role")
    if(!(role === 'isNonTmlSPOC' || role === 'isTmlManager' || role === "isSpoc")){
        window.location.replace(constants.FRONTEND_URL + '/login');
    }
  }


  handleShow = (e) => {
    this.setState({
      activeKey: e,
    });
  };

  render() {
    const isLoggedIn = this.props.loginData.isLogin;
    if (!isLoggedIn) {
      window.location.replace(constants.FRONTEND_URL + '/login');
      return null 
    } 
    else {
      return (
        <Request baseUrl={this.props.baseUrl} />
      );
    }
  }
}

const mapStateToProps = (state) => ({
  counter: state.counter.value,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
});

const mapDispatchToProps = {
  login:login,
  setRequestType:setRequestType
};
export default connect(mapStateToProps, mapDispatchToProps)(OnBoarding);
