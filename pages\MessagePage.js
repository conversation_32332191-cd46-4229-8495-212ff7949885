import { Component } from "react";
import { Spinner, ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form, } from "react-bootstrap";
import Image from "next/image";
import logo from '../public/img/tata_motor_logo.png'
import done from '../public/img/done-icon.svg'
import tick from '../public/img/tick.png'
import footer_logo from '../public/img/footer_logo.png'
import cancel from '../public/img/cancel.png'
import { connect } from "react-redux";
import { login } from "../redux/actions/loginActions";

class SuccessPage extends Component {
  static getInitialProps({ store }) {}

  constructor(props) {
    super(props);
    this.state = {
      otp: "",
      message:"",
      tmlManager:"",
      spoc:""
    };
  }
  componentDidMount() {

  }

  render() {
    if(this.props.message === 'Success'){
      return (
        <div className="hold-transition login-page">
          <Row>
            <Col md="12">
              <form onSubmit={(e) => this.handleSubmit(e)}>
                <div className="login-box text-primary" style={{ width: "100%" }}>
                  <div className="card card-primary card-outline">
                    <div className="card-body">
                      <div className="card-header-success">
                          <div className="title">Partner Onboarding</div>
                          <div className="company-logo">
                            <Image 
                              src={logo}
                              alt="logo-images"
                              width={150}
                              height={50}
                            />
                          </div>
                      </div>
                      <div className="card-body-message">
                        <div className="card-images">
                          <h3>Thank you for accepting the NDA with TML</h3>
                          <div>
                          <Image
                          src={tick}
                          alt="done-images"
                          width={200}
                          height={200}
                          />
                          </div>
                        </div>
                        <div className="card-information">
                          We will now proceed to share your credentials with your Tata Motors Manager and onboarding SPOC.<br></br>
                          Please do reach out to your onboarding SPOC - {this.props.spoc} or {this.props.tmlManager} for more information.<br></br>
                          You will also receive an email, confirming your NDA acceptance. Thank you for your co-operation.<br></br>
                          You can close the page/screen.
                        </div>
                        <div className="card-header-footer">
                          <div className="company-logo">
                            <Image 
                              src={footer_logo}
                              alt="logo-images"
                              width={150}
                              height={50}
                            />
                          </div>
                      </div>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </Col>
          </Row>
        </div>
      );
    }
    else if(this.props.message === 'Reject'){
      return (
        <div className="hold-transition login-page">
          <Row>
            <Col md="12">
              <form onSubmit={(e) => this.handleSubmit(e)}>
                <div className="login-box text-primary" style={{ width: "100%" }}>
                  <div className="card card-primary card-outline">
                    <div className="card-body">
                      <div className="card-header-success">
                          <div className="title">Partner Onboarding</div>
                          <div className="company-logo">
                            <Image 
                              src={logo}
                              alt="logo-images"
                              width={150}
                              height={50}
                            />
                          </div>
                      </div>
                      <div className="card-body-message">
                        <div className="card-images">
                          <h3>Your request for Domain ID Creation is rejected.</h3>
                          <div style={{marginTop:"1rem"}}>
                          <Image
                          src={cancel}
                          alt="done-images"
                          width={200}
                          height={200}
                          />
                          </div>
                        </div>
                        <div className="card-information">
                          Please do reach out to your onboarding SPOC - {this.props.spoc} for more information.<br></br>
                          You can close the page/screen.
                        </div>
                        <div className="card-header-footer">
                          <div className="company-logo">
                            <Image 
                              src={footer_logo}
                              alt="logo-images"
                              width={150}
                              height={45}
                            />
                          </div>
                      </div>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </Col>
          </Row>
        </div>
      );
    }
    else {
      return null
    }
  
  }
}

const mapStateToProps = (state) => ({
  spoc: state.loginInfo.login.spoc,
  tmlManager: state.loginInfo.login.tmlmanager,
  message : state.loginInfo.login.message
});

const mapDispatchToProps = {
  login:login
};

export default connect(mapStateToProps , mapDispatchToProps)(SuccessPage);

