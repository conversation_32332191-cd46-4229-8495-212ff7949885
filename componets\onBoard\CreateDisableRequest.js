import { Component } from "react";
import { ToastContainer, toast } from "react-toastify";
import { connect } from "react-redux";
import {decrementCounter,incrementCounter,step1} from "../../redux/actions/counterActions";
import Router from "next/router";
import { DatePicker } from "antd";
import React, { useState } from "react";
import '../../styles/Home.module.css'
import { FormControl, InputGroup,Col, Row, Button, Form, Spinner, Modal} from "react-bootstrap";
import Select from "react-select";
import AsyncSelect from "react-select/async"
import moment from "moment";
import { addDays } from "date-fns";
import { getAPIResponse } from '../../constants/Utils'
import * as config from '../../constants/config'
import '../../styles/Home.module.css'
import { login, logOut } from "../../redux/actions/loginActions";
import makeAnimated from 'react-select/animated';
import { withRouter } from "next/router";
import Image from "next/image";
import { VscClearAll } from "react-icons/vsc";
import { EmployeeType, Gender, UpdateEmailRegex } from "../../constants/constants";
import dayjs from "dayjs";
import { ExpiryMonthRange, INTERNAL_SERVER_ERROR, MANAGER_DETAILS_NOT_FOUND, MANAGER_VENDOR_CANNOT_SAME, PLEASE_ENTER_EMPLOYEE_DETAILS, PLEASE_ENTER_REQUIRED_FIELD, PLEASE_ENTER_VALID_EAMIL_ID, PLEASE_SELECT_VENDOR_COMPANY, SOMETHING_WENT_WRONG, YEARS_OF_EXPERIENCE_ERROR } from "../../constants/message";
import { successToast, errorToast } from "../../toast"
import { Tooltip } from 'antd';
import { MdCancel } from "react-icons/md";
import { FaEye, FaEyeSlash,FaInfoCircle } from "react-icons/fa";

const animatedComponents = makeAnimated();
class CreateDisableRequest extends Component {
  static getInitialProps({ store }) {}

  constructor(props) {
    super(props);
    this.state = {
      personEmail: null,
      personMobile: null,
      vendor: null,
      middleName: null,
      firstName: "",
      lastName: "",
      locationOption: [],
      manager: "",
      emailfag: true,
      positionPerson: "",
      PersNo: "",
      location: null,
      tmlManagerEmail: "",
      CompleteName: "",
      designation: "",
      validityFrom: "",
      validityTo:"",
      validityToNew: "",
      validityFromNew: "",
      accountType: ["Domain"],
      response: [],
      employeeId: "",
      department: "",
      domainChecked: true,
      city: "",
      gender: Gender,
      genderType: null,
      vendorManager: "",
      vendorManagerEmail: "",
      project: [],
      projectName: "",
      sapID: "",
      designationList : [],
      tmlManagerPerno : "",
      vendorOption: [],
      tmlManagerCompanyCode :"",
      skillData : [], 
      skill : [],
      yearOfExperience : "",
      showDropDown : false,
      target : "",
      tmlLocation:"",
      swipeLocation:"",
      swipeLocationOptions:[],
      buCompanyCode:'',
      buCompanyCodeList:[],
      tmlRegion:"",
      tmlRegion:"",
      tmlLocationData:[],
      tmlRegionData :[],
      isLoading:false,
      sameManagerField:"",
      tmlManagerTitle:"",
      disable:false,
      tmlManagerData:[],
      email:false,
      internet:false,
      managerLoder : false ,
      managerErrorMessage : "",
      towerName:"",
      towerData:"",
      isTowerShown:false,
      employeeDetailsData : [],
      employeeUpdate : "",
      isEnable:true,
      errorMessageForValidation : "",
      isErrorShown:false,
      loader:false,
      personEmailMessage:"",
      experienceErrorMessage:"",
      vendorEmailMessage:"",
      emailCheck:false,
      internetCheck:false,
      employeeObject : {},
      smartSearhText: "",
      smartSearchEnable:false,
      isButtonEnable:true,
      employeeType:"",
      employeeTypeErrorMessage:"",
      sioCode:"",
      isConfirmationPopup:false,
      departmentData : [], 
      edataDepartment:[],
      departmentType: "",
      isDepartmentDisable: true,
      departmentTempData:[],
      showMaskedContent:false,
      isL2Editable:false,
      l2ManagerEmail:"",
      l2ManagerName:"",
      l2ManagerEmployeeId: "",
      source:"",
      l2ManagerCompCode:""
      // test:[{ "request": "R1710252678", "fullName": "Roahn Desai", "vendor": "Sankey Business Solutions", "email": "<EMAIL>", "employeeId": "3454" }, { "request": "R1710252679", "fullName": "bahvesh rathod", "vendor": "Sankey Business Solutions", "email": "<EMAIL>", "employeeId": "3454" }, { "request": "R1710252610", "fullName": "Ajay dange", "vendor": "Sankey Business Solutions", "email": "<EMAIL>", "employeeId": "3454" }, { "request": "R1710252123", "fullName": "Pankaj Pal", "vendor": "Sankey Business Solutions", "email": "<EMAIL>", "employeeId": "3454" },{ "request": "R1710252600", "fullName": "Anand vishwkarma", "vendor": "Sankey Business Solutions", "email": "<EMAIL>", "employeeId": "3454" }]
    };
    this.disableUser.bind(this);
    this.onInputChangeForSmartSearch.bind(this);
  }

  componentDidMount() {
    
    this.getVendorList()
    this.getDesignationList()
    this.getProjectList(this.props.vendor)
    this.getTmlLocation()
    this.getSwipeLocationList()
    this.getBuCompanyCodeList()
    this.getTmlRegion()
    this.getSkill()
    this.getDepartment()
    if(this.props.vendor === "TCS") this.getTower()
   
  }

  getVendorList = () => {
    getAPIResponse(config.getVendorList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getVendorList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
        this.setState({
          vendorOption : data.data
        },()=>{this.getVendorName()})
      }
      else {
        console.log("getVendorList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getVendorList() in Request",error)
    })

  }

  getVendorName = () => {
    let vendorname = this.props.vendor
    this.state.vendorOption.map((obj , index) => {
      if(vendorname === obj.vendorCode){
        this.setState({
          vendor:obj.vendorName
        })
      }
    })
  }

  getDesignationList = () =>{
    getAPIResponse(config.getDesignationList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getDesignationList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState =>({
              designationList : [...prevState.designationList , {value : obj.designationCode , label : obj.designationName}]
            }))
          })
      }
      else {
        console.log("getDesignationList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getDesignationList() in Request",error)
    })
  }

  getProjectList = (vendor) => {
    if(vendor !== null){
    let queryParams = new URLSearchParams({
      'vendor': vendor
    })
    this.setState({
      project:[]
    })
      getAPIResponse(`${config.getProjectList}?${queryParams.toString()}` , "GET" , {})
      .then((response) => {
        if(!response.ok){
          console.log("getProjectList() in Request",response)
          return null
        }
        else{
          return response.json()
        }
      }).then((data) => {
        if(data !== null){
            data.data.map((obj , index) => {
              this.setState(prevState => ({
                project : [...prevState.project , {value : obj.projectCode , label : obj.projectName}]
              }))
            })
        }
        else {
          console.log("getProjectList() in Request",response)
        }
      })
      .catch((error) => {
        console.log("getProjectList() in Request",error)
      })
    }
  }

  getTmlLocation = () => {
    getAPIResponse(config.getTmlOffice , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getTmlLocation() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              tmlLocationData : [...prevState.tmlLocationData , {value : obj.officeCode , label : obj.officeName}]
            }))
          })
      }
      else {
        console.log("getTmlLocation() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getTmlLocation() in Request",error)
    })
  }

  getSwipeLocationList = () => {
    getAPIResponse(config.getSwipeLocationList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getSwipeLocationList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
        const swipeLocationOptions = data.data.map((obj) => ({value : obj.locationCode , label : obj.locationName}))
        this.setState(prevState => ({
          ...prevState,
          swipeLocationOptions
        }),() => {this.getSwipeLocationName()})
      }
      else {
        console.log("getSwipeLocationList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getSwipeLocationList() in Request",error)
    })
  }

  getSwipeLocationName = () => {
    let LocationName = this.props.swipeLocation
    this.state.swipeLocationOptions.map((obj , index) => {
      if(LocationName === obj.value){
        this.setState({
          vendor:obj
        })
      }
    })
  }

  getBuCompanyCodeList = () => {
    getAPIResponse(config.getBuCompanyCodeList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getBuCompanyCodeList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj) => {
            this.setState(prevState => ({
              buCompanyCodeList : [...prevState.buCompanyCodeList , {value : obj.compCode , label : obj.compName}]
            }))
          })
      }
      else {
        console.log("getBuCompanyCodeList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getBuCompanyCodeList() in Request",error)
    })
  }

  getTmlRegion = () => {
    getAPIResponse(config.getTmlRegion , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getTmlRegion() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              tmlRegionData : [...prevState.tmlRegionData , {value : obj.regionCode , label : obj.regionName}]
            }))
          })
      }
      else {
        console.log("getTmlRegion() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getTmlRegion() in Request",error)
    })
  }

  getSkill = () => {
    getAPIResponse(config.getSkillList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getSkill() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              skillData : [...prevState.skillData , {value : obj.skillCode , label : obj.skillName}]
            }))
          })
      }
      else {
        console.log("getSkill() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getSkill() in Request",error)
    })
  }

  getTower =  () => {
    getAPIResponse(config.getTowerList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getSkill() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
        data.data.map((obj)=>{
          this.setState(prevState => ({
            towerData : [...prevState.towerData,{value:obj.towerCode , label:obj.towerName}]
          }),()=>{this.getTowerName()})
        })
        
      }
      else {
        console.log("getTower() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getTower() in Request",error)
    })
  }

  getTowerName = () => {
    let towerName = this.state.towerName
    this.state.towerData.map((obj , index) => {
      if(towerName === obj.value){
        this.setState({
          towerName:obj
        })
      }
    })
  }

  getDepartment = () => {
    getAPIResponse(config.getDepartmentList , "GET" , {})
    .then((response) => {
      if(!response.ok){
        console.log("getDepartment() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null){
          data.data.map((obj , index) => {
            this.setState(prevState => ({
              departmentData : [...prevState.departmentData , {value : obj.departmentCode , label : obj.departmentName}]
            }))
          })
      }
      else {
        console.log("getDepartment() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getDepartment() in Request",error)
    })
  }

  getVendorEmployeeDetails = (vendor) => {
    let searchParams = new URLSearchParams({
        'vendor':vendor,
        "only_disable_accounts": true
    })
    getAPIResponse(`${config.getVendorEmployeeDeatils}?${searchParams.toString()}` , "GET", {})
    .then((resposne) => {
        if(resposne.status === 500){
            errorToast(INTERNAL_SERVER_ERROR)
            console.log("getVendorEmployeeDetails() in CreateUpdateRequest",resposne)
            return null
        }
        else{
            resposne.json()
        }
    })
    .then((data)=>{
        if(data !== null && data.status === 1){
        }
        else {
            console.log("getVendorEmployeeDetails() in CreateUpdateRequest",data)
        }
    })
    .catch((error)=>{
        console.log("getVendorEmployeeDetails() in CreateUpdateRequest",error)
        errorToast(SOMETHING_WENT_WRONG)
    })
  }

  resetForm = () => {
    this.setState({
      CompleteName: "",
      employeeId : "",
      firstName : "",
      lastName : "",
      middleName:null,
      personMobile : null,
      personEmail : null,
      designation : "",
      gender : "",
      projectName : "",
      vendorManager : "",
      vendorManagerEmail : "",
      tmlManagerEmail : null,
      location:"",
      department: "",
      genderType:"",
      city:"",
      validityFrom: "",
      validityTo: "",
      validityToNew: "",
      validityFromNew: "",
      manager:"",
      skill:"",
      yearOfExperience:"",
      tmlLocation:"",
      swipeLocation:"",
      buCompanyCode:"",
      tmlRegion:"",
      domainChecked:false,
      emailCheck:false,
      internetCheck:false
    });
  }

  onChangeHandler = (event) => {
    let name = event.target.name; 
    let value = event.target.value;
    if(name === "manager"){
      this.setState({
        sameManagerField :false ,
        CompleteName:"",
        city:"",
        department:"",
        [name]: value  ,
        managerErrorMessage:""
      })
    }
    else if(name === "personMobile"){
      let pattern = /^[0-9\b]+$/;
      if(pattern.test(value) || value == ''){
        this.setState({
          personMobile:value
        })
      }
    }
    else {
      this.setState({ [name]: value });
    }
  };

  handleDropdownData = (selectedOption, name) => {
    if(name == 'skill'){
      if (selectedOption.length == 0) {
        this.setState({
          skill: []
        })
      } else {
        let skillArray = selectedOption.map((value , index) =>{
          if (selectedOption.length < this.state.skill.length) {
            this.setState({
              skill: selectedOption
            })
          } else {
            this.setState(prevState => ({
              skill : [...prevState.skill,value]
            }))
          }
        })
      }
      this.getSkillList()
    }
    else if(name == 'vendor'){
      if(selectedOption.value === 'TCS'){
        this.setState({
          isTowerShown : true,
          [name] : selectedOption,
        },()=>{
            this.getProjectList(selectedOption.value) , 
            this.getTower()
        })
      }
      else {
        this.setState({
          isTowerShown : false,
          [name] : selectedOption
        },()=>{
            this.getProjectList(selectedOption.value)
        });
      }
      
    }
    else {
      this.setState({ [name] : selectedOption});
    }
    
  };

  handleDate = (value, name) => {
    let datevalue = value
    let date1 = moment(datevalue).format("YYYY-MM-DD")
    if (name == "validityTo") {
      this.setState({ [name]: moment(date1).toDate()});
    } 
    else {
      this.setState(
        { [name]: moment(date1).toDate()
      });
      
    }
  };

  handleCheck = (event) => {
    const { value, name, checked } = event.target;
    if (value === 'Domain' && checked === true){
      this.setState({
        email:true
      })
    }

    if(value === 'emailAccess' && checked === true){
      this.setState({
        email:true,
        emailCheck:true
      })
    }
    else if(value === 'emailAccess' && checked ===  false){
      this.setState({
        emailCheck : false
      })
    }

    if(value === 'Internet' && checked === true){
      this.setState({
        internet:true,
        internetCheck : true
      })
    }
    else if(value === 'Internet' && checked ===  false){
      this.setState({
        internetCheck : false
      })
    }
  };

  validateForm = (CompleteName,employeeId,firstName,lastName,personEmail,tmlLocation,buCompanyCode,designation,validityTo,city,department,gender,vendor,projectName,vendorManager,vendorManagerEmail,tmlManagerEmail,skills,yearOfExperience,tmlRegion) => {

      if ( employeeId == "" || firstName == "" || lastName == "" || personEmail == "" || tmlLocation == "" || buCompanyCode == "" || designation == "" || validityTo == "" || city == null || department == null || gender == null || vendor == null || projectName == "" || vendorManager == "" || vendorManagerEmail == "" || tmlManagerEmail == null || skills == [].length || yearOfExperience == "" || tmlRegion == "") {
          errorToast(PLEASE_ENTER_REQUIRED_FIELD)
          return false;
      }
      else if (this.state.vendor?.value == "TCS" && this.state.towerName == "") {
          errorToast(PLEASE_ENTER_REQUIRED_FIELD)
          return false   
      }
      else if(this.state.vendorManagerEmail.toLocaleLowerCase() === this.state.manager.toLocaleLowerCase()){
        errorToast(MANAGER_VENDOR_CANNOT_SAME)
        return false
      }
      else if(CompleteName == "" && this.state.manager == ""){
        errorToast(MANAGER_DETAILS_NOT_FOUND)
        return false
      }
      else {
        return true;
      }
    
  };

  getSkillList = () => {
    let skillList = this.state.skill.map((value , index) => value.value)
    return skillList
  }

  capitalizeEachLetter = (vendorName) => {
    return vendorName
    .split(' ')
    .map(word => word.split('').map((letter, index) => index === 0 ? letter.toUpperCase() : letter).join(''))
    .join(' ');
  }

  getSkillDropdownData = (list) => {
    let skillList = list.map(value =>({
      value : value.skillCode,
      label : value.skillName
    }))
    return skillList
  }

  customLabel = (value, index) => {
    const employeeName = value.request
    this.setState(prevState => ({
      employeeObject : {
        ...prevState.employeeObject,
        [employeeName]: [value.fullName , value.vendor.vendorName , value.vendor.vendorCode]
      }
    }))
    
    return (
      <div key={`employee-details-${index}`} className="search-conatiner">
        <b><span key="fullName">{value.fullName}</span></b>
        <br key="br1"/>
        <span key="employeeId" className="smartsearch">{value.employeeId}</span>
        <span key="separator2" className="smartsearch"> |  </span>
        <span key="vendor" className="smartsearch">{value.vendor.vendorName}</span>
        <br key="br2"/>
        <span key="email" className="smartsearch">{value.email}</span>
      </div>
    )
  }

  smartSearchForEmployee = () => {
    if(this.state.smartSearhText !== ""){
      this.setState({
        loader:true
      })
      let searchParams = new URLSearchParams({
        "search_text":this.state.smartSearhText,
        "include_disable_accounts": false
      })
      return getAPIResponse(`${config.getVendorEmployeeDeatils}?${searchParams.toString()}` , 'GET' , {})
      .then((response)=>{
        if(response.status === 500){
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("smartSearchForEmployee() in CreateUpdateRequest",response)
          return null
        }
        else {
          return response.json()
        }
      }).then((data)=>{
        if(data !== null && data.status == 1){
          let options = []
          options = data.data.map((value, index) => {   return {value: value.request, label: this.customLabel(value, index)}})
          return options
        } 
        else {
          this.setState({
            loader:false,
          })
          console.log("smartSearchForEmployee() in CreateUpdateRequest",data)
        }
      })
      .catch((error)=>{
        console.log("smartSearchForEmployee() in CreateUpdateRequest",error)
        errorToast(SOMETHING_WENT_WRONG)
      })
    }
    
  }


  validateUser = () => {
    this.setState({
      errorMessageForValidation:""
    })
    if(this.state.vendor === null || this.state.vendor === ""){
        errorToast(PLEASE_SELECT_VENDOR_COMPANY)
    }
    else if(this.state.employeeUpdate === null || this.state.employeeUpdate === ""){
        errorToast(PLEASE_ENTER_EMPLOYEE_DETAILS)
    }
    else {
        this.setState({
          loader:true,
        })
        getAPIResponse(`${config.getDetailsByRequestId}/${this.state.employeeUpdate?.value}` , "GET" , {})
        .then((response) => {
            if(response.status === 500){
                errorToast(INTERNAL_SERVER_ERROR)
                console.log("validateUser() in createUpdateRequest",response)
                return null
            }
            else {
                return response.json()
            }
        })
        .then((data)=>{
            if(data !== null){
                if(data.status === 1){
                  let itdepartment = this.state.departmentData.find(obj => obj.value === 'IT');
                  let nonItDepartment = this.state.departmentData.find(obj => obj.value === "NONIT");
                  let empDepartment = data.data?.ndaEmployeeDetails?.edata?.department ? this.state.departmentData.find(obj => obj.label === data.data.ndaEmployeeDetails.edata?.department): {}
                  let swipeLocation = data.data?.ndaEmployeeDetails?.swipeLocation ? this.state.swipeLocationOptions.find(o=>o.value === data.data?.ndaEmployeeDetails?.swipeLocation?.locationCode):this.state.swipeLocationOptions.find(o=>o.label==='Others');
                  let buDetails = data.data?.ndaEmployeeDetails?.buDetails ? this.state.buCompanyCodeList.find(o=>String(o.value)===String(data?.data?.ndaEmployeeDetails?.buDetails?.compCode)) : this.state.buCompanyCodeList.find(o=>String(o.value)===String(data?.data?.ndaEmployeeDetails?.tmlManager?.compCode));
                  let l2 = data.data.l2Manager;                    
                  this.setState({
                        isButtonEnable:false,
                        firstName:data.data.ndaEmployeeDetails.firstName,
                        middleName:data.data.ndaEmployeeDetails.middleName,
                        lastName:data.data.ndaEmployeeDetails.lastName,
                        personMobile:data.data.ndaEmployeeDetails.mobile,
                        personEmail:data.data.ndaEmployeeDetails.email,
                        genderType:{value:data.data.ndaEmployeeDetails.gender,label:data.data.ndaEmployeeDetails.gender},
                        yearOfExperience:data.data.ndaEmployeeDetails.yearsOfExperience,
                        skill:this.getSkillDropdownData(data.data.ndaEmployeeDetails.skills),
                        designation:{value:data.data.ndaEmployeeDetails.designation?.designationCode,label:data.data.ndaEmployeeDetails.designation?.designationName},
                        employeeId:data.data.ndaEmployeeDetails.employeeId,
                        manager:data.data.ndaEmployeeDetails.tmlManager.email,
                        city:data.data.ndaEmployeeDetails.tmlManager.city,
                        department:data.data.ndaEmployeeDetails.tmlManager.functionText,
                        tmlRegion:{value:data.data.ndaEmployeeDetails.tmlRegion?.regionCode,label:data.data.ndaEmployeeDetails.tmlRegion?.regionName},
                        tmlLocation:{value:data.data.ndaEmployeeDetails.tmlOffice?.officeCode,label:data.data.ndaEmployeeDetails.tmlOffice?.officeName},
                        swipeLocation,
                        buCompanyCode:buDetails,
                        vendorManager:data.data.ndaEmployeeDetails.vendorManagerName,
                        vendorManagerEmail:data.data.ndaEmployeeDetails.vendorManagerEmail,
                        projectName:{value:data.data.ndaEmployeeDetails.project?.projectCode,label:data.data.ndaEmployeeDetails.project?.projectName},
                        towerName:data.data.ndaEmployeeDetails.edata?.tower,
                        validityTo:dayjs(data.data.ndaEmployeeDetails.validTill),
                        validityFrom:dayjs(data.data.ndaEmployeeDetails.validFrom),
                        internetCheck:data.data.ndaEmployeeDetails.isInternet,
                        emailCheck:data.data.ndaEmployeeDetails.isEmail,
                        tmlManagerPerno:data.data.ndaEmployeeDetails.tmlManager.employeeId,
                        tmlManagerCompanyCode:data.data.ndaEmployeeDetails.tmlManager.compCode,
                        loader:false,
                        smartSearchEnable:true,
                        employeeType:{value:data.data.ndaEmployeeDetails.employeeType,label:data.data.ndaEmployeeDetails.employeeType == "IT" ? "IT" : "Non IT"},
                        sioCode:data.data.ndaEmployeeDetails.edata?.sioCode,
                        edataDepartment: typeof empDepartment !== 'undefined' && Object.keys(empDepartment).length > 0 ? empDepartment :  data.data.ndaEmployeeDetails.employeeType == "IT" ? itdepartment : nonItDepartment,
                        l2ManagerName: l2.name,
                        l2ManagerEmail: l2.email,
                        l2ManagerEmployeeId:data.data.ndaEmployeeDetails.tmlManager.l2_manager,
                        // edataDepartment: Object.keys(empDepartment).length > 0 ? empDepartment :  data.data.ndaEmployeeDetails.employeeType == "IT" ? itdepartment : nonItDepartment,
                    },()=>{if(this.state.towerName !== "") this.getTower()})
                }
                else{
                    this.setState({
                        isErrorShown:true,
                        errorMessageForValidation:data.message,
                        loader:false
                    })
                }
            }
        })
        .catch((error)=>{
            console.log("validateUser() in createUpdateRequest",error)
            errorToast(SOMETHING_WENT_WRONG)
        })
    }
  }

  disableUser = (event) => {
    // if (!this.state.buCompanyCode || !this.state.buCompanyCode.value) {
    //     errorToast("Please select BU Company Code");
    //     return;
    // }
    // let flag = this.validateForm(this.state.CompleteName,this.state.employeeId,this.state.firstName,this.state.lastName,this.state.personEmail,this.state.tmlLocation,this.state.designation,this.state.validityTo,this.state.city,this.state.department,this.state.genderType,this.state.vendor,this.state.projectName,this.state.vendorManager,this.state.vendorManagerEmail,this.state.tmlManagerEmail,this.state.skill,this.state.yearOfExperience,this.state.tmlRegion);
    if (true) {
      this.setState({
        isLoading : true,
        disable:true,
        isConfirmationPopup:false
      })
      let apiFormatedData = {
        employeeId : this.state.employeeId,
        firstName : (this.state.firstName.charAt(0).toUpperCase() + this.state.firstName.toLowerCase().slice(1)).trim(),
        middleName : this.state.middleName?.trim().charAt(0).toUpperCase() + this.state.middleName?.trim().toLowerCase().slice(1),
        lastName : (this.state.lastName.charAt(0).toUpperCase() + this.state.lastName.toLowerCase().slice(1)).trim(),
        mobile : this.state.personMobile,
        email : this.state.personEmail.toLowerCase().trim(),
        designation : this.state.designation.value,
        gender : this.state.genderType.value,
        vendor : this.state.vendor.value,
        project : this.state.projectName.value,
        vendorManagerName: this.capitalizeEachLetter(this.state.vendorManager.trim()),
        vendorManagerEmail: this.state.vendorManagerEmail.toLowerCase().trim() !== 'na' ? this.state.vendorManagerEmail.toLowerCase().trim() : "<EMAIL>",
        tmlManagerEmployeeId:this.state.tmlManagerPerno,
        tmlManagerCompCode:this.state.tmlManagerCompanyCode,
        createdBy:this.props.loginData.empId,
        validTill:dayjs().format("YYYY-MM-DD"),
        validFrom:dayjs(this.state.validityFrom).format("YYYY-MM-DD"),
        skills : this.getSkillList(),
        tmlRegion:this.state.tmlRegion.value,
        tmlOffice:this.state.tmlLocation.value,
        swipeLocation:this.state.swipeLocation.value,
        buCompCode:this.state.buCompanyCode?.value,
        yearsOfExperience:this.state.yearOfExperience,
        isEmail:this.state.emailCheck,
        isInternet:this.state.internetCheck,
        requestType:'Disable',
        old_request_id:this.state.employeeUpdate?.value,
        is_active:false,
        tmlManagerEmail:this.state.manager,
        employeeType:this.state.employeeType?.value,
        edata:{tower:this.state.towerName?.value,sioCode:this.state.sioCode},
        l2ManagerEmail:this.state.l2ManagerEmail,
        l2ManagerName:this.state.l2ManagerName,
        l2ManagerEmployeeId:this.state.l2ManagerEmployeeId,


      };
      if(this.props.isTmlEmployee !== true &&  this.props.vendor !== 'TCS'){
        delete apiFormatedData.edata.tower
      }
      getAPIResponse(config.disableRequest   , "POST", apiFormatedData)
      .then((response)=>{
        if(response.status === 500){
          this.setState({
            isLoading:false,
            disable:false
          },() => { errorToast(INTERNAL_SERVER_ERROR) })
          console.log("handleSubmit() in request",response)
          return null
          }
          else {
           return response.json()
          }
      })
      .then((data)=>{
        if(data !== null){
          if(data.status === 1){
            this.setState({
              isLoading : false,
              isTowerShown:false
            })
            successToast(data.message)
            this.goNext()
          }
          else {
            this.setState({
              isLoading:false,
              disable:false
            })
            errorToast(data.message)
          }
        }
      })
      .catch((error)=>{
        this.setState({
          isLoading:false,
          disable:false
        })
        console.log("handleSubmit() in request",error)
      })

      //  https://stackoverflow.com/questions/43842793/basic-authentication-with-fetch
    }
  };

  // managerDetails = () => {
  //   if(this.state.manager !== ""){
  //     this.setState({
  //       managerLoder : true
  //     })
  //     getAPIResponse(`${config.getManagerDetails}/${this.state.manager}` , "GET" , {})
  //     .then((response)=>{
  //       this.setState({
  //         managerLoder : false
  //       })
  //       if(response.status === 500){
  //         errorToast(INTERNAL_SERVER_ERROR)
  //         console.log("managerDetails() in Request",response)
  //         return null
  //       }
  //       else {
  //         return response.json()
  //       }
  //     })
  //     .then((data) => {
  //       if(data !== null && data.status === 1){
  //           this.setState({
  //             city:data.data.citytown,
  //             department:data.data.department,
  //             tmlManagerTitle:data.data.title,
  //             CompleteName:data.data.fullName,
  //             tmlManagerPerno:data.data.employeeId,
  //             tmlManagerCompanyCode:data.data.compCode
  //           })
  //       }
  //       else {
  //         // toast.error(data.message,{ position: "top-center", autoClose: 1500})
  //         this.setState({
  //           managerErrorMessage:data.message
  //         })
  //         console.log("managerDetails() in Request",data)
  //       }
  //     })
  //     .catch((error) => {
  //       console.log("managerDetails() in Request",error)
  //     })
  //   }
    
  // }


    //   managerDetails = () => {
    //   if(this.state.manager == "") {
    //     this.setState({
    //       managerErrorMessage : PLEASE_ENTER_MANAGER_EMAIL
    //     })
    //   }
    //   else if(this.state.employeeType == ""){
    //     this.setState({
    //       employeeTypeErrorMessage : PLEASE_SELECT_EMPLOYEE_TYPE
    //     })
    //   }
    //   else 
    //   {
    //     let body = {
    //       "manager" : this.state.manager.trim(),
    //       "employeeType" : this.state.employeeType?.value  
    //     }
    //     this.setState({
    //       managerLoder : true
    //     })
    // getAPIResponse(config.getManagerDetails_v2 , "POST" , body)
    //     .then((response)=>{
    //       this.setState({
    //         managerLoder : false
    //       })
    //       if(response.status === 500){
    //         errorToast(INTERNAL_SERVER_ERROR)
    //         console.log("managerDetails() in Request",response)
    //         return null
    //       }
    //       else {
    //         return response.json()
    //       }
    //     })
    //     .then((data) => {
    //       if(data !== null && data.status === 1){
    //         let l1 = data.data.l1_manager_details;
    //         let l2 = data.data.l2_manager_details || {};
    //         // const source = l1.source;
  
    //         const hasL2Data = l2.fullName && l2.email;
  
    //         let l2Editable = false;
    //     // if(source === "SAP"){
    //     //   if (l2?.error){
    //     //     errorToast(l2.error);
    //     //  l2Editable = true;
    //     //  l2.fullName = "";
    //     //  l2.email = "";
    //     //   }
    //     //   else if (!l2?.fullName || !l2?.email){
    //     //     if (!l2?.fullName) {
    //     //     errorToast("L2 Manager Name not found in response.");
    //     //   }
    //     //   if (!l2?.email) {
    //     //     errorToast("L2 Manager Email not found in response.");
    //     //   }
  
    //     //   }
    //     // } 
    //     // if (source === "AD") {
    //     //   alert("Enter the Manager Name and Email")
    //     //   l2Editable = true;
    //     //   // l2.fullName = "";
    //     //   // l2.email = "";
    //     // }
    //     //   else {
          
    //     //   l2Editable = false; 
    //     // }
    //           this.setState({
    //             city: l1.citytown,
    //             department: l1.department,
    //             tmlManagerTitle: l1.title,
    //             CompleteName: l1.fullName,
    //             tmlManagerPerno: l1.employeeId,
    //             tmlManagerCompanyCode: l1.compCode,
    //             source: source,
    //             isTmlManagerChange:false,
    //             l2ManagerName: l2.fullName || "",
    //             l2ManagerEmail: l2.email || "",
    //             l2ManagerEmployeeId: l2.employeeId || "",
    //             l2ManagerCompCode: l2.compCode || "",
  
    //           },()=>this.handleBuCode())
    //       }
    //       else {
    //         this.setState({
    //           managerErrorMessage:data.message
    //         })
    //         console.log("managerDetails() in Request",data)
    //       }
    //     })
    //     .catch((error) => {
    //       console.log("managerDetails() in Request",error)
    //     })
    //   }
      
    // }

  handleDatalist = (event) => {
    this.setState({
      showDropDown:true,
      target: event.target
    })
  }

  handleCloseDropdown = (event) => {
    let value = event.target.value
    if(value.length > 5){
    }
    this.setState({
      showDropDown:false
    })
  }

  handleEmployeeSearch = (event) => {
    if(this.state.employeeObject[event.value][2] == "TCS"){
      this.setState({
          isTowerShown:true,
          vendor:{value:this.state.employeeObject[event.value][2],label:this.state.employeeObject[event.value][1]},
          employeeUpdate : {value:event.value,label:this.state.employeeObject[event.value][0]}
      },()=>{this.getProjectList(this.state.employeeObject[event.value][2]) , this.validateUser()})
    }
    else {
      this.setState({
        vendor:{value:this.state.employeeObject[event.value][2],label:this.state.employeeObject[event.value][1]},
        employeeUpdate : {value:event.value,label:this.state.employeeObject[event.value][0]}
      },()=>{this.getProjectList(this.state.employeeObject[event.value][2]) , this.validateUser()})
    }
  }


  validateInput = (event) => {
    input = event.target.value.replace(/\D/g, ''); // Remove non-numeric characters
    if (input.length > 4) {
      input.value = input.value.slice(0, 4); // Limit to 4 digits
    }
  }

  setValidTill = (value) => {
    const validityFrom = this.state.validityFrom
    this.setState({
      validityTo : value.setMonth(value.getMonth() + 6)
    })
  }

  onChangeEmailHandler = (value , errorValue , error , name) => {
    if (errorValue !== "") this.setState({[error] : "" , [name] : value})
    else this.setState({[name] : value})
  }

  inputValidation = (value , name) => {
    const regex = /[^A-Za-z0-9\s]+$/;
    const regex_employeeID = /[^A-Za-z0-9]+$/;
    if(name === "employeeId"){
      if(!regex_employeeID.test(value)){
        this.setState({
          [name] : value
        })
      }
    }
    else if (!regex.test(value)){
      this.setState({
        [name] : value
      })
    }
  }

  emailValidations = (value , error , name) => {
    const regex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/i;
    // if(value !== '' && !regex.test(value)){
    if(value !== '' && !UpdateEmailRegex.test(value)){
      this.setState({
        [error] : PLEASE_ENTER_VALID_EAMIL_ID,
        [name] : null
      })
    } 
  }

  numberValidation = (value , name , error) => {
    if(this.state.experienceErrorMessage !== "") this.setState({experienceErrorMessage : ""})
    const pattern = /^[0-9\b]+$/;
    if(pattern.test(value)){
      if(parseInt(value) > 60){
        this.setState({ [name] : null , [error]  : YEARS_OF_EXPERIENCE_ERROR })
      }
      else {
        this.setState({
          [name] : value
        })
      }
    }
    else if(value == ""){
      this.setState({
        [name] : value
      })
    } 
  }

  onInputChangeForSmartSearch = (value) => {
      this.setState({
        smartSearhText : value,
      })
  }

  clearSearch = () => {
    this.setState({
      employeeUpdate:"",
      isEnable:true,
      smartSearchEnable:false,
      isButtonEnable:true
    },() => {this.resetForm()})
  }

  closeConfirmationPopup = () => {
    this.setState({
      isConfirmationPopup : false
    })
  }

  openConfirmationPopup = () => {
    this.setState({
      isConfirmationPopup : true
    })
  }


  goNext = () => {
    Router.push({
      pathname: "/Dashboard",
    });
  };

  onInputChange2 = (value) => {
    console.log("Input Value:", value);
    this.setState({ smartSearhText: value });
    return value; // Returning value prevents unnecessary re-renders in AsyncSelect
  };

  smartSearch = async (inputValue) => {
    if (!inputValue) return [];

    this.setState({ loader: true });

    let searchParams = new URLSearchParams({
      "search_text":inputValue,
      "include_disable_accounts": false
    })

    try {
      const response = await getAPIResponse(`${config.getVendorEmployeeDeatils}?${searchParams.toString()}` , 'GET' , {})
      if (response.status === 500) {
        errorToast(SOMETHING_WENT_WRONG);
        console.error("smartSearchForEmployee() Error:", response);
        return [];
      }

      const data = await response.json();

      if (data && data.status === 1) {
        let options = data.data.map((value, index) => ({
          value: value.request,
          label: this.customLabel(value, index),
        }));
        console.log("Fetched Options:", options);
        return options;
      } else {
        console.log("No valid data:", data);
        return [];
      }
    } catch (error) {
      console.error("API Call Error:", error);
      errorToast(SOMETHING_WENT_WRONG);
      return [];
    } finally {
      this.setState({ loader: false });
    }
  }

  maskEmail = (email) => {
    if(!email) return null;
    if(email === null) return null;
    if (!email || !email.includes("@")) return "****@****.***";
  
    const [name, domain] = email.split("@");
    const maskedName = name[0] + "*".repeat(Math.max(0, name.length - 2)) + name.slice(-1);
    
    return maskedName + "@" + domain;
  };

  maskMobileNumber = (number) => {
    if(!number) return null;
    if (number.length < 4) return "****"; 
    return number.replace(/\d(?=\d{4})/g, "*");
  };

  render() {
    return (
      <>
      <div>
        <div className="row">
          <div className="col-12">
            <Form onSubmit={this.disableUser} autoComplete="off">
              <div className="card card-primary card-outline">
                <div className="card-header with-border">
                  <h2 className="card-title">Disable Request</h2>
                </div>
                <div className="card-body">

                  {/* ------------------------------ Validation part ---------------------------   */}
                    <div className="validate-group">
                        <Col md="3">
                            <Form.Group controlId="Vendor">
                            <Form.Label> Vendor Name </Form.Label><sup style={{color:'red'}}>*</sup>
                            <Select
                                instanceId={"Vendor"}
                                options={this.state.vendorOption}
                                value={this.state.vendor}
                                onChange={(e) => {this.handleDropdownData(e, "vendor")}}
                                isSearchable
                                isFocused={false}
                                name="vendor"
                                placeholder="Vendor Company Name"
                                className="myclass"
                                noOptionsMessage={({ inputValue }) =>
                                "No results found"
                                }
                                isDisabled={true}
                            />
                            </Form.Group>
                        </Col>

                        <Col md="3">
                            <Form.Group controlId="employeeDetails">
                            <Form.Label>Employee Details </Form.Label><sup style={{color:'red'}}>*</sup>
                                <AsyncSelect
                                    instanceId={"employeeDetails"}
                                    value={this.state.employeeUpdate}
                                    onChange={(e) => {this.handleEmployeeSearch(e)}}
                                    // onInputChange={(e)=>{this.onInputChangeForSmartSearch(e)}} 
                                    // loadOptions={this.smartSearchForEmployee}
                                    onInputChange={this.onInputChange2}
                                    loadOptions={this.smartSearch}
                                    name="employeeUpdate"
                                    placeholder="Search By Employee Details"
                                    className="myclass"
                                    isDisabled={this.state.smartSearchEnable}
                                />
                                
                                {this.state.isErrorShown? <span className="validation-error">{this.state.errorMessageForValidation}</span> : null}
                            </Form.Group>
                        </Col>

                        <Col md='5' className="d-flex justify-content-between">
                        <div className="validate" onClick={this.clearSearch}>
                              <Tooltip title="Clear" color="#FFF" overlayInnerStyle={{color:"#000000"}}>
                                <div className="clear-button"><VscClearAll className="clear-icon"/></div> 
                              </Tooltip>  
                              
                        </div>
                        <div className="validate" onClick={()=>{this.setState(prevState=>({...prevState,showMaskedContent:!prevState.showMaskedContent}))}}>
                        <Tooltip title={this.state.showMaskedContent ? 'Hide':'Show'} color="#FFF" overlayInnerStyle={{color:"#000000"}}>
                                <div className="clear-button">{this.state.showMaskedContent ? <FaEyeSlash className="clear-icon"/>:<FaEye className="clear-icon"/>}</div> 
                              </Tooltip> 
                        </div>
                        </Col>

                       
                         
                    </div> 
                  {/* ------------------------------ Person Details ----------------------------- */}
                  <div className="form-section">
                    <span>Personal Details</span>
                  </div>

                  <Row className="form-grid">
                    <Col md="3">
                      <Form.Group controlId="Firstname">
                        <Form.Label> First Name  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <InputGroup>
                          <Form.Control
                            value={this.state.firstName}
                            type="text"
                            name="firstName"
                            onChange={(e)=>{this.inputValidation(e.target.value , "firstName")}}
                            placeholder=" Enter First Name"
                            disabled={this.state.isEnable}
                            maxLength="20"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="middleName">
                        <Form.Label> Middle Name </Form.Label>
                        <InputGroup>
                          <Form.Control
                            value={this.state.middleName}
                            type="text"
                            name="middleName"
                            onChange={(e)=>{this.inputValidation(e.target.value , "middleName")}}
                            placeholder=" Enter Middle Name"
                            disabled={this.state.isEnable}
                            maxLength="20"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="lastName">
                        <Form.Label> Last Name  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <InputGroup>
                          <Form.Control
                            value={this.state.lastName}
                            type="text"
                            // required
                            name="lastName"
                            onChange={(e)=>{this.inputValidation(e.target.value , "lastName")}}
                            placeholder=" Enter Last Name"
                            disabled={this.state.isEnable}
                            maxLength="20"
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col>

                    {/* <Col md="3">
                      <Form.Group controlId="Contact">
                        <Form.Label> Mobile No. </Form.Label>
                        <InputGroup>
                          <Form.Control
                            pattern="[1-9]{1}[0-9]{9}"
                            value={this.state.personMobile}
                            // required
                            type="text"
                            name="personMobile"
                            onChange={this.onChangeHandler}
                            placeholder=" Enter Contact Number"
                            maxLength="10"
                            disabled={this.state.isEnable}
                          />
                        </InputGroup>
                      </Form.Group>
                    </Col> */}                      

                      <Col md="3" className="detail-view-flex">
                        <Form.Group controlId="Contact">
                          <Form.Label> Mobile No. </Form.Label>
                            <Form.Control
                              pattern="[1-9]{1}[0-9]{9}"
                              type="text"
                              name="personMobile"
                              readOnly={this.state.isEnable} // Makes the field read-only if isEnable is true
                              value={this.state.showMaskedContent ? this.state.personMobile : this.maskMobileNumber(this.state.personMobile)}
                              placeholder="Enter Contact Number"
                              maxLength="10"
                              disabled={this.state.isEnable} // Disables the field if isEnable is true
                            />
                        </Form.Group>
                    </Col>




                    <Col md="3">
                      <Form.Group controlId="Email">
                        <div className="d-flex align-items-center justify-content-between">
                        <div>
                        <Form.Label> Vendor Partner Email ID </Form.Label><sup style={{color:'red'}}>*</sup>
                        </div>
                        <Tooltip placement="top" title={`Enter the official email ID provided by the vendor for onboarding.`} arrow >
                        <FaInfoCircle/>
                        </Tooltip>
                        </div>
                        <Form.Control
                          type="email"
                          name="personEmail"
                          value={this.state.showMaskedContent ? this.state.personEmail : this.maskEmail(this.state.personEmail)}
                          onChange={(e) => {this.onChangeEmailHandler(e.target.value , this.state.personEmailMessage , "personEmailMessage" , 'personEmail')}}
                          placeholder="Enter Vendor Partner Email"
                          onBlur={()=>{this.emailValidations(this.state.personEmail , "personEmailMessage" , "personEmail")}}
                          disabled={this.state.isEnable}
                        />
                        { this.state.personEmailMessage !== "" && this.state.personEmailMessage ? 
                              <span style={{color:"red" , fontSize:"12px"}}>{this.state.personEmailMessage}</span> : null
                        }
                      </Form.Group>
                    </Col>
                    
                    <Col md="3">
                        <Form.Group controlId="Gender">
                          <Form.Label>Gender</Form.Label><sup style={{color:'red'}}>*</sup>
                          <Select
                            options={this.state.gender}
                            value={this.state.genderType}
                            onChange={(e) => {this.handleDropdownData(e, "genderType")}}
                            isSearchable
                            name="genderType"
                            placeholder="Select Gender"
                            className="myclass"
                            noOptionsMessage={({ inputValue }) =>
                              "No results found"
                            }
                            isDisabled={this.state.isEnable}
                            // required
                          />
                        </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="yearOfExperience">
                        <Form.Label>Years of Experience</Form.Label><sup style={{color:'red'}}>*</sup>
                        <div style={{position:'relative'}}>
                        <FormControl
                            name="yearOfExperience"
                            type="text"
                            placeholder="Enter Years of Experience"
                            className="input-field-apperence"
                            maxLength="2"
                            value={this.state.yearOfExperience}
                            onChange={(e) => {this.numberValidation(e.target.value, "yearOfExperience" , "experienceErrorMessage")}}
                            disabled={this.state.isEnable}
                          />
                           { this.state.experienceErrorMessage !== "" && this.state.experienceErrorMessage ? 
                              <span style={{color:"red" , fontSize:"12px"}}>{this.state.experienceErrorMessage}</span> : null
                          }
                        </div>
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="skills">
                        <Form.Label>Skills</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.skillData}
                          value={this.state.skill}
                          onChange={(e) => {this.handleDropdownData(e, "skill")}}
                          isSearchable
                          name="skill"
                          placeholder="Select Skills"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isMulti
                          isDisabled={this.state.isEnable}
                          // components={animatedComponents}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="designation">
                        <Form.Label> Designation  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"designation"}
                          options={this.state.designationList}
                          value={this.state.designation}
                          onChange={(e) => {this.handleDropdownData(e, "designation");}}
                          isSearchable
                          isFocused={false}
                          placeholder="Select Designation"
                          isOptionDisabled={(option) => option.label == "a"}
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="employeeId">
                        <Form.Label>Employee ID</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                          type="text"
                          name="employeeId"
                          onChange={(e)=>{this.inputValidation(e.target.value , "employeeId")}}
                          placeholder="Enter Employee ID"
                          disabled={true}
                          value={this.state.employeeId}
                          maxLength="20"
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="employeeType">
                        <Form.Label> Employee Type </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"employeeType"}
                          options={EmployeeType}
                          value={this.state.employeeType}
                          onChange={(e) => {this.handleDropdownData(e, "employeeType");}}
                          isSearchable
                          isFocused={false}
                          placeholder="Select Employee Type"
                          isOptionDisabled={(option) => option.label == "a"}
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                        { this.state.employeeTypeErrorMessage !== "" && this.state.employeeTypeErrorMessage ? 
                            <span style={{color:"red" , fontSize:"12px"}}>{this.state.employeeTypeErrorMessage}</span> : null
                        }
                      </Form.Group>
                    </Col>
                    <Col md="3">
                      <Form.Group controlId="department">
                        <Form.Label>Department</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.departmentData}
                          value={this.state.edataDepartment}
                          onChange={(e) => {this.handleDropdownData(e, "edataDepartment")}}
                          isSearchable
                          name="department"
                          placeholder="Select Department"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // isDisabled={this.state.isDepartmentDisable}
                          // required
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  {/* --------------------------------- TML company Details ------------------------------ */}
                  <div className="form-section">
                    <span>Reporting Manager And Company Details</span>
                  </div>

                  <Row className="form-grid">  

                    <Col md="3">
                        <Form.Group controlId="manager">
                          <Form.Label> Reporting Manager&apos;s Email </Form.Label><sup style={{color:'red'}}>*</sup>
                          <InputGroup className={this.state.sameManagerField ?"error-input mb-3 flex-direction" : "mb-3 flex-direction"}>
                            <InputGroup className="manager-loader">
                            <FormControl
                              type="text"
                              name="manager"
                              placeholder="Enter TML Email"
                              aria-label="Recipient's username"
                              aria-describedby="basic-addon2"
                              onChange={this.onChangeHandler}
                              // required
                              value={this.state.manager}
                              // onBlur={this.managerDetails}
                              style={{width:'100%'}}
                              disabled={this.state.isEnanble}
                            />
                            { this.state.managerLoder ? 
                              <div style={{paddingLeft:'5px'}}>
                                <Spinner size="sm" animation="border" role="status" variant="primary"/>
                              </div> : null
                            }
                            </InputGroup>
                          { this.state.CompleteName ? 
                              <span style={{color:"green" , fontSize:"15px" , position:"absolute" , top:'2.3rem'}}>{this.state.CompleteName}</span> : 
                              this.state.managerErrorMessage ? 
                              <span style={{color:"red" , fontSize:"15px" , position:"absolute" , top:'2.3rem'}}>{this.state.managerErrorMessage}</span> : null
                          }
                          </InputGroup>
                        </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="city To">
                        <Form.Label> City  </Form.Label>{this.state.employeeType?.value === 'NONIT' ? null : <sup style={{color:'red'}}>*</sup> }
                        <Form.Control
                        type="text"
                        name=""
                        readOnly
                        value={this.state.city}
                        // onChange={this.onChangeHandler}
                        placeholder=" Enter City"
                        disabled={this.state.isEnable}
                      />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="Department No">
                        <Form.Label> Department  </Form.Label>{this.state.employeeType?.value === 'NONIT' ? null : <sup style={{color:'red'}}>*</sup> }
                        <Form.Control
                        type="text"
                        name=""
                        readOnly
                        value={this.state.department}
                        // onChange={this.onChangeHandler}
                        placeholder=" Enter Department"
                        disabled={this.state.isEnable}
                      />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="tmlRegion">
                        <Form.Label> TML Region  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"tmlRegion"}
                          options={this.state.tmlRegionData}
                          value={this.state.tmlRegion}
                          onChange={(e) => {this.handleDropdownData(e, "tmlRegion");}}
                          isSearchable
                          isFocused={false}
                          name="tmlRegion"
                          placeholder="Select TML Region"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="tmlLocation">
                        <Form.Label> TML Location  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"tmlLocation"}
                          options={this.state.tmlLocationData}
                          value={this.state.tmlLocation}
                          onChange={(e) => {this.handleDropdownData(e, "tmlLocation");}}
                          isSearchable
                          isFocused={false}
                          name="tmlLocation"
                          placeholder="Select TML Location"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="buCompanyCode">
                        <Form.Label> BU Company Code </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          instanceId={"buCompanyCode"}
                          options={this.state.buCompanyCodeList}
                          value={this.state.buCompanyCode}
                          isDisabled={this.state.isEnable}
                          onChange={(e) => {this.handleDropdownData(e, "buCompanyCode");}}
                          isSearchable
                          isFocused={false}
                          name="buCompanyCode"
                          placeholder="Select BU Company Code"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="swipeLocation">
                        <Form.Label> Swipe Location  </Form.Label>
                        <Select
                          instanceId={"swipeLocation"}
                          options={this.state.swipeLocationOptions}
                          value={this.state.swipeLocation}
                          onChange={(e) => {this.handleDropdownData(e, "swipeLocation");}}
                          isSearchable
                          isFocused={false}
                          name="swipeLocation"
                          placeholder="Select Swipe Location"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="SIO">
                        <Form.Label>SIO Code / BC Field</Form.Label>
                        <Form.Control
                        type="text"
                        name="sioCode"
                        value={this.state.sioCode}
                        onChange={(e)=>{this.inputValidation(e.target.value , "sioCode")}}
                        placeholder=" Enter SIO Code / BC Field"
                        disabled={this.state.isEnable}
                      />
                      </Form.Group>
                    </Col>
                    {/* <Col md="3">
                      <Form.Group controlId="l2ManagerEmail">
                          <Form.Label> L2 Manager Email </Form.Label><sup style={{color:'red'}}>*</sup>
                          <Form.Control
                          type="email"
                          name="l2ManagerEmail"
                          value={this.state.l2ManagerEmail}
                          onChange={(e) => this.setState({ l2ManagerEmail: e.target.value })}
                          // onBlur={this.state.source === "AD" ? this.fetchL2ManagerDetails : undefined}
                          disabled={!this.state.l2FieldsEditable}
                          placeholder="Enter L2 Manager Email"
                        />

                        </Form.Group>
                    </Col> 
                    <Col md="3">
                      <Form.Group controlId="l2ManagerName">
                        <Form.Label> L2 Manager Name </Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                        type="text"
                        name="l2ManagerName"
                        value={this.state.l2ManagerName}
                        onChange={(e) => this.setState({ l2ManagerName: e.target.value })}
                        disabled={!this.state.l2FieldsEditable ? true : false}
                        placeholder="Enter L2 Manager Name"
                        />
                         </Form.Group>
                    </Col> */}
                    <Col md="3">
                      <Form.Group controlId="l2ManagerEmail">
                        <Form.Label>L2 Manager Email</Form.Label>
                        <Form.Control
                        type="text"
                        name="l2ManagerEmail"
                        value={this.state.l2ManagerEmail}
                        // onChange={(e)=>{this.inputValidation(e.target.value , "sioCode")}}
                        placeholder="Enter L2 Manager Email"
                        disabled={this.state.isEnable}
                      />
                      </Form.Group>
                    </Col>
                    <Col md="3">
                      <Form.Group controlId="l2ManagerName">
                        <Form.Label>L2 Manager Name</Form.Label>
                        <Form.Control
                        type="text"
                        name="l2ManagerName"
                        value={this.state.l2ManagerName}
                        // onChange={(e)=>{this.inputValidation(e.target.value , "sioCode")}}
                        placeholder=" Enter L2 Manager Name"
                        disabled={this.state.isEnable}
                      />
                      </Form.Group>
                    </Col>

                  </Row>

                  {/* ------------------------------------ Vendor Details ---------------------------------------- */}
                  <div className="form-section">
                    <span>Vendor Details</span>
                  </div>

                  <Row className="form-grid">

                  <Col md="3">
                      <Form.Group controlId="projectName">
                        <Form.Label>Project Name</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.project}
                          value={this.state.projectName}
                          onChange={(e) => {this.handleDropdownData(e, "projectName")}}
                          isSearchable
                          name="projectName"
                          placeholder="Select Project Name"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col>

                    {
                      this.props.vendor === 'TCS' || this.state.isTowerShown ? 
                      <Col md="3">
                      <Form.Group controlId="towerName">
                        <Form.Label>Tower Name</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Select
                          options={this.state.towerData}
                          value={this.state.towerName}
                          onChange={(e) => {this.handleDropdownData(e, "towerName")}}
                          isSearchable
                          name="towerName"
                          placeholder="Select Tower Name"
                          className="myclass"
                          noOptionsMessage={({ inputValue }) =>
                            "No results found"
                          }
                          isDisabled={this.state.isEnable}
                          // required
                        />
                      </Form.Group>
                    </Col> : null
                    }
                    
                    <Col md="3">
                      <Form.Group controlId="vendorManager">
                        <Form.Label>Vendor Manager&apos;s Name</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                          type="text"
                          name="vendorManager"
                          onChange={(e)=>{this.inputValidation(e.target.value , "vendorManager")}}
                          placeholder="Enter Vendor Manager Name"
                          disabled={this.state.isEnable}
                          value={this.state.vendorManager}
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="vendorManagerEmail">
                        <Form.Label>Vendor Manager&apos;s Email</Form.Label><sup style={{color:'red'}}>*</sup>
                        <Form.Control
                          type="email"
                          name="vendorManagerEmail"
                          value={this.state.showMaskedContent ? this.state.vendorManagerEmail : this.maskEmail(this.state.vendorManagerEmail)}
                          onChange={(e) => {this.onChangeEmailHandler(e.target.value , this.state.vendorEmailMessage , "vendorEmailMessage" , 'vendorManagerEmail')}}
                          onBlur={()=>{this.emailValidations(this.state.vendorManagerEmail , "vendorEmailMessage" , "vendorManagerEmail")}}
                          placeholder="Enter Vendor Manager Email"
                          disabled={this.state.isEnable}
                          // required
                        />
                         { this.state.vendorEmailMessage !== "" && this.state.vendorEmailMessage ? 
                            <span style={{color:"red" , fontSize:"12px"}}>{this.state.vendorEmailMessage}</span> : null
                          }
                      </Form.Group>
                    </Col>

                  </Row> 

                  {/* ----------------------------------- Account Type --------------------------------- */}
                  <div className="form-section">
                    <span>Account Type</span>
                  </div>

                  <Row className="form-grid">   
                  <Col md="3">
                      <Form.Group controlId="validityFrom">
                        <Form.Label> Valid From  </Form.Label><sup style={{color:'red'}}>*</sup>
                        <DatePicker
                           value={this.state.validityFrom}
                           onChange={(e) => {this.handleDate(e, "validityFrom")}}
                           name="validityFrom"
                           dateFormat="yyyy-MM-dd"
                           placeholderText="Select Valid From"
                           className="form-control display-block"
                           disabled={this.state.isEnable}
                           style={{fontFamily : 'sans-serif'}}
                        />
                      </Form.Group>
                    </Col>

                    <Col md="3">
                      <Form.Group controlId="validityTo">
                        <Form.Label> Valid till </Form.Label><sup style={{color:'red'}}>*</sup>
                        <DatePicker
                           value={this.state.validityTo}
                           onChange={(e) => {this.handleDate(e, "validityTo")}}
                           name="validityTo"
                           dateFormat="yyyy-MM-dd"
                           placeholderText="Select Valid To" 
                           className="form-control"
                           showMonthDropdown
                           minDate={dayjs(this.state.validityFrom)}
                           maxDate={dayjs(this.state.validityFrom).add(ExpiryMonthRange,'month')}
                           disabled={this.state.isEnable}
                           style={{fontFamily : 'sans-serif'}}
                        />
                      </Form.Group>
                    </Col>

                    <Col md="6">
                      <Form.Group controlId="accountType">
                        <Form.Label>Account Type</Form.Label>
                        {["checkbox"].map((type) => (
                          <div key={`inline-${type}`} className="">
                            <Form.Check
                              type={type}
                              label="Domain ID"
                              id="Domain"
                              name="accountType"
                              defaultChecked={this.state.domainChecked}
                              inline
                              value="Domain"
                              onChange={this.handleCheck}
                              disabled
                            />
                            <Form.Check
                              inline
                              type={type}
                              label="Email Account"
                              name="emailType"
                              id="emailAccess"
                              value="emailAccess"
                              onChange={this.handleCheck}
                              disabled={this.state.isEnable ? true : this.state.emailCheck ? true : false}
                              checked={this.state.emailCheck}
                            />
                            <Form.Check
                              inline  
                              type={type}
                              label="Internet"
                              id="Internet"
                              name="InternetType"
                              value="Internet"
                              onChange={this.handleCheck}
                              disabled={this.state.isEnable}
                              checked={this.state.internetCheck}
                            />
                          </div>
                        ))}
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row className="form-grid">
                    <Col md="12">
                      <Form.Group className="text-center" controlId="Submitbtn">
                        <Button disabled={this.state.isButtonEnable} className="primary-button" onClick={this.openConfirmationPopup}>Disable</Button>
                        {/* <Button  className="reset-button" onClick={this.resetForm}>Reset</Button> */}
                      </Form.Group>
                    </Col>
                  </Row>
                </div>
              </div>
            </Form>
            {/* </CardBox> */}
          </div>
        </div>
      </div>

      {/* --------------------------------------------------- confirmation popup ------------------------------------------- */}

        <Modal
            show={this.state.isConfirmationPopup}
            onHide={this.closeConfirmationPopup}
            aria-labelledby="contained-modal-title-vcenter"
            centered
        >
          <Modal.Header className="display-justify-end">
            <div style={{cursor:"pointer"}} onClick={this.closeConfirmationPopup}>
              <MdCancel />  
            </div>
          </Modal.Header>
          <Modal.Body className="group-nda-message">
            Are you sure you want to disable the request ?
          </Modal.Body>
          <Modal.Footer>
            <Button variant="primary" onClick={this.disableUser}>Disable</Button>
            <Button variant="danger" onClick={this.closeConfirmationPopup}>Cancel</Button>
          </Modal.Footer>
        </Modal>

      {
        this.state.isLoading ?
        <div className="loader">
          <div></div>
        </div> : null
      }
      </>
    );
  }
}
const mapStateToProps = (state) => ({
  counter: state.counter.value,
  baseUrl: state.counter.baseUrl,
  maxDay: state.counter.maxDay,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
  vendor : state.loginInfo.login.vendorName,
  isTmlEmployee : state.loginInfo.login.isTmlEmployee,
  requestType : state.counter.requestType
});

const mapDispatchToProps = {
  incrementCounter: incrementCounter,
  decrementCounter: decrementCounter,
  step1: step1,
  login:login
};
export default withRouter(connect(mapStateToProps, mapDispatchToProps)(CreateDisableRequest));
