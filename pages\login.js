import { Component } from "react";
import Router from "next/router";
import { <PERSON><PERSON>, ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form, } from "react-bootstrap";
import { connect } from "react-redux";
import { login, logOut } from "../redux/actions/loginActions";
import {getAPIResponse} from '../constants/Utils'
import * as config from '../constants/config'
import { jwtDecode } from "jwt-decode";
import Image from "next/image";
import logo from '../public/img/Tata-Motors-Logo-Vector.svg'
import { clearCounter } from "../redux/actions/counterActions";
import "react-toastify/dist/ReactToastify.css";
import { INVALID_USERNAME_AND_PASSWORD, LOGIN_SUCCESSFULLY, PLEASE_ENTER_PASSWORD, PLEASE_ENTER_USERNAME, PLEASE_ENTER_VALID_EAMIL_ID, SEND_EMAIL_OTP, SOMETHING_WENT_WRONG, UNAUTHORIZED_USER } from "../constants/message";
import { successToast, errorToast } from "../toast"
import { routerAndRole } from "../constants/navigations";
import { FaBookOpen } from "react-icons/fa";

class Login extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      Email: "",
      password: "",
      role: "",
      disable:false,
      isPassword:true,
      usernameError : "",
      passwordError : ""
    };
  }

  componentDidMount() {
    this.props.logOut()
    this.props.clearCounter()
    localStorage.clear()
  }

  handleSubmit(event) {
    event.preventDefault();
    if (this.state.Email === "" || this.state.Email === null){
      this.setState({ usernameError : PLEASE_ENTER_USERNAME})
    }
    else if (this.state.password === "" || this.state.password === null){
      this.setState({ passwordError  : PLEASE_ENTER_PASSWORD})
    }
    else if (this.state.usernameError === PLEASE_ENTER_VALID_EAMIL_ID){
      this.setState({ disable:false })
    }
    else {
      this.setState({
        disable:true
      })
      let body = {
        // "email":this.state.Email,
        "username":this.state.Email,
        "password":this.state.password,
      }
      getAPIResponse(config.getTokenV2, "POST", body)
      .then((response) => {
        if (response.status == 401) {
          this.setState({
            disable:false
          })
          errorToast(INVALID_USERNAME_AND_PASSWORD)
          return null
        } 
        else if (response.status == 404) {
          this.setState({
            disable:false
          })
          errorToast(INVALID_USERNAME_AND_PASSWORD)
          return null
        } 
        else if (response.status == 500) {
          this.setState({
            disable:false
          })
          errorToast(INVALID_USERNAME_AND_PASSWORD)
          return null
        } 
        else {
          return response.json()
        }
      })
      .then((response) => {
        if (response != null) {
          console.log("response:",response.data)
          let response_data = response.data
          let token = response_data.token
          console.log("response_data:",response_data)
          console.log("token:",token)
          
          localStorage.setItem('accessToken', token.access_token)
          localStorage.setItem('refreshToken', token.refresh_token)
          localStorage.setItem('user_id', response_data.username)
          console.log("usier:",localStorage.getItem('user_id'))
          const decoded = jwtDecode(token.access_token);
          // if(this.state.role === 'tmlManger' && response_data.isTmlEmployee === "False"){
          //   errorToast(UNAUTHORIZED_USER)
          // }
          // else {
            successToast(SEND_EMAIL_OTP)
            this.props.login({
              userName: response_data.user,
              userEmail: response_data.email,
              profileImg: "",
              empId: "",
              isLogin: true,
              vendorName:response_data.vendor_id,
              isTmlEmployee:response_data.isTmlManager,
              isSPOC:response_data.isSPOC,
              isNonTmlManager:response_data.isNonTmlManager,
              vendorCompanyName:response_data.vendorCompanyName,
              isSuperUser:response_data.is_superuser,
              isSidebarShown:false
            });
            this.gonext()
            // this.goNext(response_data.isTmlManager,response_data.isSPOC,response_data.isNonTmlManager,response_data.is_superuser)
          // }
          
        }
      })
      .catch((error) => {
        console.log("handleSubmit() in login",error)
        this.setState({
          disable:false
        })
      })
    }  
  }

  handleChange(event , inputValue) {
    let name = event.target.name;
    let value = event.target.value;
    if(this.state.usernameError !== "" && inputValue == 'Email') this.setState({usernameError : ""})
    else if(this.state.passwordError !== "" && inputValue == 'password') this.setState({passwordError : ""})
    this.setState({ 
      [name]: value,
      disable:false
    }); 
    
  }

  emailValidations = (value , error , name) => {
    const regex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i;
    if(value !== '' && !regex.test(value)){
      this.setState({
        [error] : PLEASE_ENTER_VALID_EAMIL_ID,
      })
    } 
  }

  gonext = () =>{
    Router.push({
      pathname: '/ValidateLoginOTP',
    });
  }
  // goNext = (isTmlManager,isSPOC,isNonTmlManager,isSuperUser) => {
  //   console.log("goNext:",isTmlManager,isSPOC,isNonTmlManager,isSuperUser)

  //   let role = routerAndRole(isTmlManager,isSPOC,isNonTmlManager,isSuperUser)
  //   console.log("role:",role)
  //   Router.push({
  //     pathname: role.defaultRoute,
  //   });
  // };

  isPasswordShow = () => {
    this.setState({
      isPassword:!this.state.isPassword
    })
  }

  render() {
    return (
      <div className="hold-transition login-page">
        <Row>
          <Col md="12">
            <form onSubmit={(e) => this.handleSubmit(e)} autoComplete="off">
              <div className="login-box">
                <div className="login-logo">
                    
                </div>
                <div className="card">
                  <div className="card-body login-card-body">
                    <div className="login-logo">
                      <Image 
                        src={logo} 
                        alt="logo-images"
                        width={230}
                        height={100}
                      />
                    </div>
                    <div className="login-form-box">
                      <div className="input-group">
                        <div className="username-box">
                            <input
                              onChange={(e) => this.handleChange(e , "Email")}
                              type="email"
                              value={this.state.Email}
                              name="Email"
                              className="form-control"
                              placeholder=" Enter Email"
                              onBlur={() => (this.emailValidations(this.state.Email , "usernameError" ,"Email"))}
                            />
                            <div className="input-group-append">
                              <div className="input-group-text">
                                <span className="fas fa-user"></span>
                              </div>
                            </div>
                        </div>
                        {  this.state.usernameError !== "" ? 
                          <span style={{color:"red" , fontSize:"12px"}}>{this.state.usernameError }</span> : null
                        } 
                      </div>
                        
                      <div className="input-group">
                          <div className="username-box">
                          <input
                            type={this.state.isPassword ? "password" : "text"}
                            onChange={(e) => this.handleChange(e , "password")}
                            className="form-control"
                            value={this.state.password}
                            name="password"
                            placeholder="Password"
                          />
                        {
                          this.state.isPassword ? 
                          <div className="input-group-append">
                            <div className="input-group-text" onClick={this.isPasswordShow}>
                              <span className="fas fa-eye-slash"></span>
                            </div>
                          </div> : 
                          <div className="input-group-append">
                          <div className="input-group-text" onClick={this.isPasswordShow}>
                            <span className="fas fa-eye"></span>
                          </div>
                        </div>
                        }
                          </div>
                            {  this.state.passwordError !== "" ? 
                            <span style={{color:"red" , fontSize:"12px"}}>{this.state.passwordError  }</span> : null
                            } 
                      </div>
                    </div>
                    <div className="row justify-content-center">
                       <div className="login-button">
                        <button type="submit" className="btn btn-primary btn-block" disabled={this.state.disable}>{this.state.disable ? <div className="loader-small"></div> : 'Send OTP'}</button>
                      </div>
                    </div>
                    <div className="row justify-content-end">
                      <a style={{gap:7}} className="row" href={"/documents/Partner-Onboarding-Help-and-Support.pdf"} target="_blank">
                        <div className="justify-content-center">
                          <FaBookOpen style={{color:"#007bff"}}/>
                        </div>
                        User Manual
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </Col>
        </Row>
      </div>
    );
  }
}
const mapStateToProps = (state) => ({
  isLogin: state.loginInfo.isLogin,
  loginState: state.loginInfo.login
});

const mapDispatchToProps = {
  login: login,
  logOut: logOut, 
  clearCounter:clearCounter
};
export default connect(mapStateToProps, mapDispatchToProps)(Login);
