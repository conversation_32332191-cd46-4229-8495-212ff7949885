import React from "react";
import { connect } from "react-redux";
import { login, logOut } from "../redux/actions/loginActions";
import { Container, Navbar, Nav, NavDropdown, Form, FormControl, Button, } from "react-bootstrap";
import Link from "next/link";
import { getAPIResponse } from "../constants/Utils";
import * as config from '../constants/config'
import { toast } from "react-toastify";
import { useRouter } from "next/router";
import { FRONTEND_URL } from "../constants/constants";
import { routerAndRole } from "../constants/navigations";
import { FaRegUser } from "react-icons/fa";

class Header extends React.Component {
  static getInitialProps({ store }) {}
  
  constructor(props) {
    super(props);
    this.state = {
      showDropdown: false,
      role : "",
      isSidebarShown: false
    }
    
  }

  componentDidMount(){
    let roleName = routerAndRole(this.props.isTmlEmployee, this.props.isSPOC, this.props.isNonTmlManager,this.props.isSuperUser)
    this.setState({
      role : roleName.name
    })
  }

  logOut = () => {
    let body  = {
      "user_id":localStorage.getItem('user_id'),
      "access_token": localStorage.getItem('accessToken'),
      "refresh_token": localStorage.getItem('refreshToken'),
    }
    getAPIResponse(config.userLogout , "POST" , body)
    .then((response)=> {
      if(response.status === 500){
        return null
      }
      else {
        return response.json()
      }
    })
    .then((data)=>{
      if(data !== null){
        if(data.status == 1){
          console.log(data)
        }
        else {
          console.log("logOut() in header",data)
        }
      }
    })
    .catch((error) => {
      console.log("logOut() in header",error)
    })
    window.location.href = FRONTEND_URL + '/login'
  };

  handleSidebar = () => {
    this.props.login({
      userName: this.props.userInfo.userName,
      userEmail: this.props.userInfo.userEmail,
      profileImg: "",
      empId: "",
      isLogin: true,
      vendorName:this.props.userInfo.vendorName,
      isTmlEmployee:this.props.userInfo.isTmlEmployee,
      isSPOC:this.props.userInfo.isSPOC,
      isNonTmlManager:this.props.userInfo.isNonTmlManager,
      vendorCompanyName:this.props.userInfo.vendorCompanyName,
      isSidebarShown:true
    })
  }

  render() {
    return (
      <nav className="navbar">
        <ul className="navbar-nav hide-menu">
          <li className="nav-item" onClick={this.handleSidebar}>
            <a className="nav-link" data-widget="pushmenu" role="button" >
              <i className="fas fa-bars"></i>
            </a>
          </li>
        </ul>

        <ul className="navbar-nav">
          <div className="nav-link" onClick={() => this.setState({showDropdown: !this.state.showDropdown})}>
            {/* <i className="far fa-user"></i> */}
            <FaRegUser />
          </div>
          {
            this.state.showDropdown 
              ? <div style={{backgroundColor: 'white', boxShadow: '0 0.5rem 1rem rgba(0, 0, 0, 0.175)', borderRadius: '4px',  position: 'absolute', zIndex: 2, top: '44px', right: 0, width: '288px', maxWidth: 'calc(100vw - 16px)', padding: '16px', paddingBottom: '8px'}}>
                  <div className="card card-primary card-outline" style={{}}>
                      <div className="card-body box-profile">
                        <h3 className="profile-username text-center">
                          { this.props.userName }
                        </h3>
                        <p className="text-muted text-center">{this.state.role}</p>
                          <button className="btn btn-block btn-primary btn-xs" style={{fontSize: '16px', margin: 'auto', borderRadius: '4px', width: 'fit-content', padding: '4px 16px'}} onClick={() => this.logOut()}>Log Out</button>
                      </div>
                    </div>
                </div> 
              : null
          }
          {/* <li className="nav-item">
            <a className="nav-link" data-widget="fullscreen" href="#" role="button">
              <i className="fas fa-expand-arrows-alt"></i>
            </a>
          </li> */}
        </ul>
      </nav>
    );
  }
}

const mapStateToProps = (state) => ({
  userName: state.loginInfo.login.userName,
  UserEmail: state.loginInfo.login.UserEmail,
  profile: state.loginInfo.login.profile,
  profileImg: state.loginInfo.login.profileImg,
  empId: state.loginInfo.login.empId,
  isTmlEmployee:state.loginInfo.login.isTmlEmployee,
  isSPOC:state.loginInfo.login.isSPOC,
  isNonTmlManager:state.loginInfo.login.isNonTmlManager,
  userInfo:state.loginInfo.login,
  isSuperUser:state.loginInfo.login.isSuperUser
});

const mapDispatchToProps = {
  logOut: logOut,
  login:login
};
export default connect(mapStateToProps, mapDispatchToProps)(Header);
