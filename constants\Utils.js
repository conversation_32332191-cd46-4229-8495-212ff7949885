import { BASE_URL, HEADERS, FRONTEND_URL } from "./constants"
import { getToken, getTokenFromRefreshToken , validateOtpUrl , validateUrl , ndaAccept, getTokenV2, getRefreshTokenV2} from "./config"

async function getAPIResponse(endpoint, method, payload) {
    let requestParameter = {
        method: method,
        headers: method === 'GET' ? {} : {'Content-Type': 'application/json'}
    }
    if (endpoint != getTokenV2) {
        const accessToken = localStorage.getItem("accessToken")
        requestParameter.headers.Authorization = `Bearer ${accessToken}`
    } 
    if(endpoint === validateOtpUrl || endpoint.startsWith(validateUrl) || endpoint === ndaAccept){
        delete requestParameter.headers.Authorization
    }
    if (Object.keys(payload).length !== 0) {
        requestParameter.body = JSON.stringify(payload)
    }
    let response = await fetch(`${BASE_URL}/${endpoint}`, requestParameter)
    if (response.status == 401) {
        if(endpoint !== getTokenV2){
            try {
                let response = await fetch(`${BASE_URL}/${getRefreshTokenV2}`, {
                    method: 'POST',
                    body: JSON.stringify({
                        "user_id": localStorage.getItem('user_id'),
                        "refresh_token": localStorage.getItem('refreshToken')
                    }),
                    headers: {
                        "Content-Type": "application/json"
                    }
                })
                if(response.status !== 200){
                    window.location.href = FRONTEND_URL + '/login'
                    return response
                }
                else {
                    let jsonResponse = await response.json()
                    localStorage.setItem('accessToken', jsonResponse.data.access_token)
                    localStorage.setItem('refreshToken', jsonResponse.data.refresh_token)
                    requestParameter.headers.Authorization = `Bearer ${jsonResponse.data.access_token}`
                    response = await fetch(`${BASE_URL}/${endpoint}`, requestParameter)
                    return response
                }
                
            } catch (error) {
                console.log('error while getting access token from refresh token')
                return response
            }
        } else {
            return response
        }
        
    } else {
        return response
    }
}

export { getAPIResponse };