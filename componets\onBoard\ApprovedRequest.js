import React, { Fragment } from "react";
import { Component } from "react";
import { toast } from "react-toastify";
import Router from "next/router";
import BootstrapTable from "react-bootstrap-table-next";
import paginationFactory from "react-bootstrap-table2-paginator";
import ToolkitProvider, { Search } from "react-bootstrap-table2-toolkit";
const { SearchBar } = Search;
import { connect } from "react-redux";
import { setId } from "../../redux/actions/counterActions";
import { getAPIResponse } from '../../constants/Utils'
import * as config from '../../constants/config'
import * as constants from '../../constants/constants'
import moment from "moment";
import { APPROVEDREQUEST, FULLFILLEDREQUEST, INTERNAL_SERVER_ERROR, PAGINATIONS, SEARCH, SOMETHING_WENT_WRONG } from "../../constants/message";
import { successToast, errorToast } from "../../toast"
import { Button } from "antd";

class ApprovedRequest extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      response: "",
      updateObj: "",
      headers: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "id", text: "Request ID", sort: true },
        { dataField: "fullName", text: "Person Name", sort: true },
        { dataField: "tmlManager", text: "Reporting Manager", sort: true },
        { dataField: "createdBy", text: "Created By", sort: true },
        { dataField: "createdAt", text: "Created At", sort: true },
        { dataField: "status", text: "Request Status", sort: true },
        {dataField: "Action",text: "Action",formatter: this.linkFollow,sort: true}
      ],
      dataArray: [],
      isFollow: true,
      isOpen: false,
      isSubmit: "No",
      CompleteName: "",
      ContactNo: "",
      EmailID: "",
      CompanyCodeDesc: "",
      spinner: false,
      totalcomplete: "",
      currentPage: 0,
      currentPageData: [],
      sizePerPage: constants.sizePerPage, 
      totalSize: 0,
      searchText: '',
      isRetryDisable:false,
      isRetryDisableV2:{}
    };

  }
  
  componentDidMount() {
    this.getApprovedRequest()
  }

  componentDidUpdate(prevProps){
    if(prevProps.requestType !== this.props.requestType){
      this.getApprovedRequest()
    }
  }

  getApprovedRequest = () => {
    let queryParams = new URLSearchParams({
      'filter' : APPROVEDREQUEST,
      'requesttype' : this.props.requestType
    })
    let api_url = `${config.getFullfilledRequest}?${queryParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      queryParams = new URLSearchParams({
        'filter' : APPROVEDREQUEST,
        'requesttype' : this.props.requestType,
        "action": constants.admin_approved_request,
        "module": this.props.requestType +' '+ constants.admin_dashboard
      })
      api_url = `${config.getAdminRequestDetails}?${queryParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response)=>{
      if(response.status === 500){
        console.log("getApprovedRequest() in allrequest",response)
        return null
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      if (data !== null && data.status === 1){
        this.setState({
          totalSize: data.data.count,
          currentPage: 1,
          currentPageData: data.data.results.map((value, index) => ({...value, srno: index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')}))
        })
      }
    })
    .catch((error)=>{
      console.log("getApprovedRequest() in ApprovedRequest",error)
    })
  }

  getPaginatedApprovedRequests(page, searchText) {
    let searchParams = new URLSearchParams({
      "filter": APPROVEDREQUEST,
      "page": page,
      "searchtext": searchText,
      'requesttype' : this.props.requestType
    }) 
    let api_url = `${config.getFullfilledRequest}?${searchParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      searchParams = new URLSearchParams({
        "filter": APPROVEDREQUEST,
        "page": page,
        "searchtext": searchText,
        'requesttype' : this.props.requestType,
        "action": constants.admin_approved_request,
        "module": this.props.requestType +' '+ constants.admin_dashboard
      })
      api_url = `${config.getAdminRequestDetails}?${searchParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response) => {
      if (response.status === 500){
        console.log("getPaginatedApprovedRequests (FulfilledRequest.js)", response)
        return null
      } else {
        return response.json()
      }  
    }).then((response) => {
      if (response !== null && response.data === null && this.state.searchText.length > 0) {
        this.setState({
          totalSize: 0,
          currentPage: 0,
          currentPageData: []
        })
      }
      if (response !== null && response.status === 1) {
        this.setState({
          totalSize: response.data.count,
          currentPage: page,
          currentPageData: response.data.results.map((value, index) => ({...value, srno: (page - 1) * this.state.sizePerPage + index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')})),
        })
      }
    }).catch((error) => {
      console.log("getPaginatedApprovedRequests (FulfilledRequest.js)", error)
    })
  }

  triggerDomainCreations(row) {
    this.handleRetryDisableClick(row.id,true)
    let body = {
      "id" : row.id 
    }
    let URL = ""
    if(row.requestType === 'Update'){
      URL = config.triggerUpdateDomainID
    }
    else {
      URL = config.triggerCreateDomainID
    }
    if (localStorage.getItem("role") == 'isSuperUser'){
        body['module'] = this.props.requestType +' '+ constants.admin_dashboard
        if(row.requestType === 'Update'){
          URL = config.triggerAdminUpdateDomainID
          body['action'] = constants.admin_update_domainid
        }
        else {
          URL = config.triggerAdminDomainID
          body['action'] = constants.admin_create_domainid
        }
    }
    getAPIResponse(URL , "POST" , body)
      .then((response)=>{
        if(response.status === 500){
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("triggerDomainCreations() in approvedRequest",response)
          return null
        }
        else{
          return response.json()
        }  
      })
      .then((data)=>{
        if(data !== null){
          if(data.status === 1){
            successToast(data.message)
            this.getApprovedRequest()
          }
          else{
            errorToast(data.message)
            this.handleRetryDisableClick(row.id,false)
          }
        }
        
      })
      .catch((error)=>{
        console.log("triggerDomainCreations() in approvedRequest",error)
        errorToast(SOMETHING_WENT_WRONG)
        this.handleRetryDisableClick(row.id,false)
      })
  }

  onChangeHandler = (event) => {
    event.preventDefault();
    let name = event.target.name;
    let value = event.target.value;
    this.setState({ [name]: value });
  };

  mySubmitHandler = (event) => {
    event.preventDefault();
    this.setState({ isSubmit: "Yes" }, () => this.closeModal());
  };

  openModal = () => this.setState({ isOpen: true, isSubmit: "No" });

  closeModal = () => {
    if (this.state.isSubmit == "No") {
      this.setState({ isOpen: false });
      toast.warn("Operation Canceled...!", {
        position: toast.POSITION.TOP_CENTER,
      });
    } 
    else {
      this.setState({ isOpen: false });
      toast.info("Data Updated...!", { position: toast.POSITION.TOP_CENTER });
    }
  };

  onFollowChanged(row) {
    this.props.setId({ requestId: row.id });
    Router.push({
      pathname: "/RequestManager",
    });
  }

  handleRetryDisableClick=(id,value)=>{
    this.setState(prevState=>({
      isRetryDisableV2: {
        ...prevState.isRetryDisableV2,
        [
          id
        ]: value,
      },
    }));
  };


  linkFollow = (cell, row, rowIndex, formatExtraData) => {
    return (
      <div>
        <a style={{ marginRight: "2px", cursor: "pointer" , padding:'8px 8px'}} title="view details" className="btn-info btn-xs" onClick={() => {this.onFollowChanged(row)}}><i className="far fa-eye"></i></a>
        {constants.ApprovedRequestList.includes(row.status) ? <Button style={{ marginLeft: "5px", cursor: "pointer" , padding:'4px 10px'}} disabled={formatExtraData.disabledButtons[row.id]} onClick={()=>{this.triggerDomainCreations(row)}}>Retry</Button>:null}
      </div>
    );
  };

  render() {
    // isRetryDisable=this.state
    const tableColumns=this.state.headers.map(col=>{
      if(col.dataField==='Action'){
        return{
          ...col,
          formatExtraData: {
            disabledButtons:this.state.isRetryDisableV2
          }
        };
      }
      return col;
    });
    return (
      <Fragment>
          <ToolkitProvider
            keyField="id"
            data={this.state.currentPageData}
            // columns={this.state.headers}
            columns={tableColumns}
            // formatExtraData = {this.state.isRetryDisable}
            search
          >
            {(props) => (
              <div>
                <div className="text-right">
                  <SearchBar
                    {...props.searchProps}
                    className="custome-search-field"
                    placeholder="Search"
                  />
                </div>

                <BootstrapTable
                  pagination={paginationFactory({page: this.state.currentPage, sizePerPage: this.state.sizePerPage, totalSize: this.state.totalSize, hideSizePerPage: true})}
                  wrapperClasses="table-responsive"
                  striped
                  {...props.baseProps}
                  remote
                  columns={tableColumns}
                  onTableChange={(type, { page, searchText }) => {
                    console.log("Props:::",props)
                    if (type === SEARCH) {
                      this.setState({searchText: searchText})
                      this.getPaginatedApprovedRequests(1, searchText)
                    }
                    if (type === PAGINATIONS) {
                      this.getPaginatedApprovedRequests(page, this.state.searchText)
                    }       
                  }}
                />
              </div>
            )}
          </ToolkitProvider>
      </Fragment>
    );
  }
}
const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  requestType : state.counter.requestType
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(ApprovedRequest);
