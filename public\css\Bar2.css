.multi-steps > li.is-active:before, .multi-steps > li.is-active ~ li:before {
    content: counter(stepNum);
    font-family: inherit;
    font-weight: 700;
    
}
.multi-steps > li.is-disable:before, .multi-steps > li.is-disable ~ li:before {
    content: counter(stepNum);
    font-family: inherit;
    font-weight: 700;
    
}
.multi-steps > li.is-active:after, .multi-steps > li.is-active ~ li:after {
    background-color: #d1d1da;
}
.multi-steps > li.is-disable:after, .multi-steps > li.is-disable ~ li:after {
    background-color: #d1d1da;
}
.multi-steps {
    display: table;
    table-layout: fixed;
    width: 100%;
}
.multi-steps > li {
    counter-increment: stepNum;
    text-align: center;
    display: table-cell;
    position: relative;
    color: #3c8dbc;
    font-size: 13px;
}
.multi-steps > li:before {
    content: '\f00c';
    content: '\2713';
    content: '\10003';
    content: '\10004';
    content: '\2713';
    display: block;
    margin: 0 auto 4px;
    background-color: #ededed;
    width: 36px;
    height: 36px;
    line-height: 32px;
    text-align: center;
    font-weight: bold;
    border-width: 1px;
    border-style: solid;
    border-color:#ededed;
    border-radius: 50%;
}

/* .is-disable {
    content: '\f00c';
    content: '\2713';
    content: '\10003';
    content: '\10004';
    content: '\2713';
    display: block;
    margin: 0 auto 4px;
    background-color: #e83636;
    width: 36px;
    height: 36px;
    line-height: 32px;
    text-align: center;
    font-weight: bold;
    border-width: 1px;
    border-style: solid;
    border-color: #ca1a1a;
    border-radius: 50%;
} */
.multi-steps > li:after {
    content: '';
    height: 1px;
    width: 100%;
    background-color: #3c8dbc;
    position: absolute;
    top: 16px;
    left: 50%;
    z-index: -1;
}
.multi-steps > li:last-child:after {
    display: none;
}
.multi-steps > li.is-active:before {
    background-color: #fff;
    border-color: #3c8dbc;
}
.multi-steps > li.is-disable:before {
    background-color: #fff;
    border-color: #f55749;
    color: #f55749;
    content: "X" !important;
}
.multi-steps > li.is-disable > label{
    color: #f57064ba;
    /* content: ""; */
}
.multi-steps > li.is-active ~ li {
    color: #808080;
}
.multi-steps > li.is-disable ~ li {
    color: #808080;
}
.multi-steps > li.is-active ~ li:before {
    background-color: #ededed;
    border-color: #ededed;
}
.multi-steps > li.is-disable ~ li:before {
    background-color: #ededed;
    border-color: #ededed;
}

._53Ji7 {
    box-sizing: border-box;
}
._1Lo2h {
    margin: 0 0 1rem 0;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
}
._2Jtxm {
    list-style: none;
    position: relative;
    text-align: center;
    flex: 1 1 auto;
    position: relative;
}
._2Jtxm::before {
    content: '';
    position: absolute;
    top: 50%;
    height: 3px;
    width: 100%;
    right: 50%;
    transform: translateY(-50%);
    z-index: -1;
    background: linear-gradient(to left, #e7eaf3 50%, #17a2b8 50%);
    background-size: 200% 100%;
    background-position: right bottom;
    transition: background-position 200ms ease-in-out;
}
._2Jtxm:first-child::before {
    content: none;
}
._2Jtxm ._2kL0S, ._2Jtxm ._2JvrO {
    background-color: #e7eaf3;
    color: var(--text-normal);
    border-radius: 50%;
    height: 2.5rem;
    width: 2.5rem;
    line-height: 2.5rem;
    display: inline-flex;
    transform-origin: center;
    align-items: center;
    justify-content: center;
}
._2kL0S, ._2JvrO {
    transition: background-color 250ms ease;
}
._2Jtxm ._1hzhf {
    color: var(--text-normal);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    line-height: normal;
}
._2Jtxm ._1ixJ3 {
    display: block;
}
._2Jtxm._2ZUAI ._2JvrO {
    background-color: #17a2b8; 
    color: white;
    -webkit-animation: _3SDnc 400ms ease-in-out forwards;
    animation: _3SDnc 400ms ease-in-out forwards;
}
._2Jtxm._2ZUAI ._2JvrO:hover {
    background-color: #42997f;
}
._2Jtxm._35Ago ._2kL0S {
    background-color: #00c0ef;
    color: white;
    -webkit-animation: _JAh3L 400ms ease forwards;
    animation: _JAh3L 400ms ease forwards;
}
._2Jtxm._35Ago ._2kL0S:hover {
    background-color: #316ddb;
}
._2Jtxm._2ZUAI::before, ._2Jtxm._35Ago::before, ._2Jtxm._1CcaK::before {
    background-position: left bottom;
}
._2Jtxm._1CcaK ._2JvrO {
    color: white;
    background-color: #bb392d;
    -webkit-animation: _1ujce 350ms ease-in-out forwards;
    animation: _1ujce 350ms ease-in-out forwards;
}
._2_g61 {
    margin-top: 55px;
}
._3uApM {
    margin-left: 15px;
    margin-right: 20px;
    padding-bottom: 25px;
    align-items: center;
    justify-content: space-between;
}
._2pGos {
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 200ms ease-in-out;
}
._3CDiP {
    border: 1px solid #d1d9eb;
}
._3CDiP:hover {
    background-color: #f3f7ff;
    box-shadow: 0px 3px 6px 0 rgba(190, 201, 228, 0.3);
}
._hsN1w {
    background-color: #00c0ef;
    border: 1px solid #397cf6;
    color: white;
}
._hsN1w:hover {
    background-color: #316ddb;
    border: 1px solid #316ddb;
    box-shadow: 0px 3px 6px 0 rgba(57, 124, 246, 0.3);
}
._2pGos._2RWmX {
    pointer-events: none;
    -webkit-filter: opacity(0.7);
    filter: opacity(0.7);
    cursor: default;
}
@-webkit-keyframes _JAh3L {
    0% {
        transform: scale(1);
   }
    10% {
        transform: scale(0.975);
   }
    40% {
        transform: scale(1.4);
   }
    50%, 100% {
        transform: scale(1.2);
   }
}
@keyframes _JAh3L {
    0% {
        transform: scale(1);
   }
    10% {
        transform: scale(0.975);
   }
    40% {
        transform: scale(1.4);
   }
    50%, 100% {
        transform: scale(1.2);
   }
}
@-webkit-keyframes _3SDnc {
    0% {
        transform: scale(1.2);
   }
    35%, 100% {
        transform: scale(1);
   }
}
@keyframes _3SDnc {
    0% {
        transform: scale(1.2);
   }
    35%, 100% {
        transform: scale(1);
   }
}
@-webkit-keyframes _1ujce {
    0% {
        transform: translateX(0);
   }
    15% {
        transform: translateX(0.2rem);
   }
    30%, 60% {
        transform: translateX(-0.4rem);
   }
    45%, 75% {
        transform: translateX(0.4rem);
   }
    90% {
        transform: translateX(-0.2rem);
   }
    100% {
        transform: translateX(0);
   }
}
@keyframes _1ujce {
    0% {
        transform: translateX(0);
   }
    15% {
        transform: translateX(0.2rem);
   }
    30%, 60% {
        transform: translateX(-0.4rem);
   }
    45%, 75% {
        transform: translateX(0.4rem);
   }
    90% {
        transform: translateX(-0.2rem);
   }
    100% {
        transform: translateX(0);
   }
}
@media (min-width: 360px) and (max-width: 640px) {
    ._2Jtxm {
        margin: 2px;
   }
    ._2_g61 {
        margin-top: 56px;
   }
    ._2Jtxm ._1hzhf {
        color: var(--text-normal);
        position: absolute;
        top: 120%;
        left: 25px;
        transform: translateX(-50%);
        font-size: 10px;
   }
}
.mtop {
    margin-top: 36px !important;
}
.setIndex {
    position: relative;
    z-index: 1;
    margin-top: -61px;
    margin-bottom: 20px;
}
