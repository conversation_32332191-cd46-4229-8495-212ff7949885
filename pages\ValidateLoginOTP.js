import { Component } from "react";
import Router from "next/router";
import { Col, Row } from "react-bootstrap";
import { connect } from "react-redux";
import { login, logOut } from "../redux/actions/loginActions";
import {getAPIResponse} from '../constants/Utils'
import * as config from '../constants/config'
import Image from "next/image";
import logo from '../public/img/Tata-Motors-Logo-Vector.svg'
import { clearCounter } from "../redux/actions/counterActions";
import "react-toastify/dist/ReactToastify.css";
import { INVALID_EMAIL_OTP,LOGIN_SUCCESSFULLY, PLEASE_ENTER_PASSWORD, PLEASE_ENTER_VALID_EAMIL_ID } from "../constants/message";
import { successToast, errorToast } from "../toast"
import { routerAndRole } from "../constants/navigations";
import { FaBookOpen } from "react-icons/fa";

class Login extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      Email: "",
      password: "",
      role: "",
      disable:false,
      isPassword:true,
      usernameError : "",
      passwordError : ""
    };
  }

  componentDidMount() {
    // this.props.logOut()
    // this.props.clearCounter()
    // localStorage.clear()
  }

  handleSubmit(event) {
    event.preventDefault();
    if (this.state.password === "" || this.state.password === null){
      this.setState({ passwordError  : PLEASE_ENTER_PASSWORD})
    }
    else if (this.state.usernameError === PLEASE_ENTER_VALID_EAMIL_ID){
      this.setState({ disable:false })
    }
    else {
      this.setState({
        disable:true
      })
      let body = {
        // "email":this.state.Email,
        "user_id":this.props.loginState.userEmail,
        "login_otp":this.state.password,
      }
      getAPIResponse(config.validateOtp, "POST", body)
      .then((response) => {
        if (response.status == 401) {
          this.setState({
            disable:false
          })
          errorToast(INVALID_EMAIL_OTP)
          return null
        } 
        else if (response.status == 404) {
          this.setState({
            disable:false
          })
          errorToast(INVALID_EMAIL_OTP)
          return null
        } 
        else if (response.status == 500) {
          this.setState({
            disable:false
          })
          errorToast(INVALID_EMAIL_OTP)
          return null
        } 
        else {
          return response.json()
        }
      })
      .then((response) => {
        if (response != null) {
          successToast(LOGIN_SUCCESSFULLY)
          let response_data = this.props.loginState
          console.log("response_data:",response_data)
            this.goNext(response_data.isTmlEmployee,response_data.isSPOC,response_data.isNonTmlManager,response_data.isSuperUser)
          // }
          
        }
      })
      .catch((error) => {
        console.log("handleSubmit() in login",error)
        this.setState({
          disable:false
        })
      })
    }  
  }

  handleChange(event , inputValue) {
    let name = event.target.name;
    let value = event.target.value;
    if(this.state.usernameError !== "" && inputValue == 'Email') this.setState({usernameError : ""})
    else if(this.state.passwordError !== "" && inputValue == 'password') this.setState({passwordError : ""})
    this.setState({ 
      [name]: value,
      disable:false
    }); 
    
  }

  emailValidations = (value , error , name) => {
    const regex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/i;
    if(value !== '' && !regex.test(value)){
      this.setState({
        [error] : PLEASE_ENTER_VALID_EAMIL_ID,
      })
    } 
  }

  goNext = (isTmlManager,isSPOC,isNonTmlManager,isSuperUser) => {
    console.log("goNext:",isTmlManager,isSPOC,isNonTmlManager,isSuperUser)

    let role = routerAndRole(isTmlManager,isSPOC,isNonTmlManager,isSuperUser)
    console.log("role:",role)
    Router.push({
      pathname: role.defaultRoute,
    });
  };

  isPasswordShow = () => {
    this.setState({
      isPassword:!this.state.isPassword
    })
  }

  render() {
    console.log("this.props:",this.props)
    return (
      <div className="hold-transition login-page">
        <Row>
          <Col md="12">
            <form onSubmit={(e) => this.handleSubmit(e)} autoComplete="off">
              <div className="login-box">
                <div className="login-logo">
                    
                </div>
                <div className="card">
                  <div className="card-body login-card-body">
                    <div className="login-logo">
                      <Image 
                        src={logo} 
                        alt="logo-images"
                        width={230}
                        height={100}
                      />
                    </div>
                    <div className="login-form-box">
                      <div className="row justify-content-center">
                        <div>
                          <h6 style={ {marginBottom:0}}>Email: {this.props.loginState.userEmail}</h6>
                        </div>
                      </div>
                      <div className="input-group">
                          <div className="username-box">
                          <input
                            type={this.state.isPassword ? "password" : "text"}
                            onChange={(e) => this.handleChange(e , "password")}
                            className="form-control"
                            value={this.state.password}
                            name="password"
                            placeholder="Validate OTP"
                          />
                        {
                          this.state.isPassword ? 
                          <div className="input-group-append">
                            <div className="input-group-text" onClick={this.isPasswordShow}>
                              <span className="fas fa-eye-slash"></span>
                            </div>
                          </div> : 
                          <div className="input-group-append">
                          <div className="input-group-text" onClick={this.isPasswordShow}>
                            <span className="fas fa-eye"></span>
                          </div>
                        </div>
                        }
                          </div>
                            {  this.state.passwordError !== "" ? 
                            <span style={{color:"red" , fontSize:"12px"}}>{this.state.passwordError  }</span> : null
                            } 
                      </div>
                    </div>
                    <div className="row justify-content-center">
                       <div className="login-button">
                        <button type="submit" className="btn btn-primary btn-block" disabled={this.state.disable}>{this.state.disable ? <div className="loader-small"></div> : 'Validate OTP'}</button>
                      </div>
                    </div>
                    <div className="row justify-content-end">
                      <a style={{gap:7}} className="row" href={"/documents/Partner-Onboarding-Help-and-Support.pdf"} target="_blank">
                        <div className="justify-content-center">
                          <FaBookOpen style={{color:"#007bff"}}/>
                        </div>
                        User Manual
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </Col>
        </Row>
      </div>
    );
  }
}
const mapStateToProps = (state) => ({
  isLogin: state.loginInfo.isLogin,
  loginState: state.loginInfo.login,
});

const mapDispatchToProps = {
  login: login,
  logOut: logOut, 
  clearCounter:clearCounter
};
export default connect(mapStateToProps, mapDispatchToProps)(Login);
