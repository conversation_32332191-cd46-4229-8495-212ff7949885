import { fas } from '@fortawesome/free-solid-svg-icons';
import { LOGIN, LOGOUT , REHYDRATE} from '../actions/loginActions';

const initialstate = {
    login: {
        userName: '',
        userEmail: '',
        profile: '',
        profileImg: '',
        empId: '',
        userRole: "",
        isLogin: false,
        personName: '',
        link:"",
        vendorName:"",
        isTmlEmployee:false,
        otp:'',
        spoc:'',
        tmlmanager:'',
        vendorCompanyName:'',
        message:'',
        isSPOC:'',
        isNonTmlManager:'',
        isSuperUser:'',
        isSidebarShown:false
    },
    isLogin: false

}

const loginReducer = (state = initialstate, action) => {
    switch (action.type) {

        case LOGIN:
            return {
                ...state,
                isLogin: true,
                login: {
                    userName: action.payload.userName,
                    userEmail: action.payload.userEmail,
                    profile: action.payload.profile,
                    profileImg: action.payload.profileImg,
                    empId: action.payload.empId,
                    userRole: action.payload.userRole,
                    isLogin: action.payload.isLogin,
                    personName: action.payload.personName,
                    personMobile: action.payload.personMobile,
                    personEmail: action.payload.personEmail,
                    link:action.payload.link,
                    vendorName:action.payload.vendorName,
                    isTmlEmployee:action.payload.isTmlEmployee,
                    otp:action.payload.otp,
                    spoc:action.payload.spoc,
                    tmlmanager:action.payload.tmlmanager,
                    vendorCompanyName:action.payload.vendorCompanyName,
                    message:action.payload.message,
                    isSPOC:action.payload.isSPOC,
                    isNonTmlManager:action.payload.isNonTmlManager,
                    isSuperUser:action.payload.isSuperUser,
                    isSidebarShown:action.payload.isSidebarShown
                }
            };

        case LOGOUT:
            return {
                ...state,
                isLogin: false,
                login: {
                    userName: "",
                    userEmail: "",
                    profile: "",
                    profileImg: "",
                    empId: "",
                    isLogin: false,
                    isSidebarShown:false
                }
            };

        default:
            return { ...state };
    }
};

export default loginReducer;
