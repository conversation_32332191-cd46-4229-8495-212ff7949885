import React, { Fragment } from "react";
import { Component } from "react";
import { toast } from "react-toastify";
import Router from "next/router";
import BootstrapTable from "react-bootstrap-table-next";
import paginationFactory from "react-bootstrap-table2-paginator";
import ToolkitProvider, { Search } from "react-bootstrap-table2-toolkit";
const { SearchBar } = Search;
import { connect } from "react-redux";
import { setId } from "../../redux/actions/counterActions";
import { getAPIResponse } from '../../constants/Utils'
import * as config from '../../constants/config'
import * as constants from '../../constants/constants'
import moment from "moment";
import { FULLFILLEDREQUEST, PAGINATIONS, PERMISSION_DENIED_FOR_DELETE_OPERATION, SEARCH } from "../../constants/message";
import { errorToast } from "../../toast"
import fileDownload from 'js-file-download';
import info from '../../public/img/information.svg'
import Image from "next/image";

class Fullfilled extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      response: "",
      updateObj: "",
      headers: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "id", text: "Request ID", sort: true },
        { dataField: "fullName", text: "Person Name", sort: true },
        { dataField: "tmlManager", text: "Reporting Manager", sort: true },
        { dataField: "createdBy", text: "Created By", sort: true },
        { dataField: "createdAt", text: "Created At", sort: true },
        { dataField: "status", text: "Request Status", sort: true },
        {dataField: "Action",text: "Action",formatter: this.linkFollow,sort: true},
      ],
      dataArray: [],
      isFollow: true,
      isOpen: false,
      isSubmit: "No",
      CompleteName: "",
      ContactNo: "",
      EmailID: "",
      CompanyCodeDesc: "",
      spinner: false,
      totalcomplete: "",
      currentPage: 0,
      currentPageData: [],
      sizePerPage: constants.sizePerPage, 
      totalSize: 0,
      searchText: '',
    };

    this.onFollowChanged.bind(this);
    this.onDeleteRow.bind(this);
    this.mySubmitHandler.bind(this);
    this.onChangeHandler = this.onChangeHandler.bind(this);
  }
  
  componentDidMount() {
    // if( this.props?.activeKey == "fullfilled")
      this.getFullfilledRequest()
  }

  componentDidUpdate(prevProps){
    if(prevProps.requestType !== this.props.requestType){
      this.getFullfilledRequest()
    }
  }

  getFullfilledRequest = () => {
    let queryParams = new URLSearchParams({
      'filter' : FULLFILLEDREQUEST,
      'requesttype' : this.props.requestType
    })
    let api_url = `${config.getFullfilledRequest}?${queryParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      queryParams = new URLSearchParams({
        'filter' : FULLFILLEDREQUEST,
        'requesttype' : this.props.requestType,
        "action": constants.admin_fullfill_request,
        "module":this.props.requestType +' '+ constants.admin_dashboard
      })
      api_url = `${config.getAdminRequestDetails}?${queryParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response)=>{
      if(response.status === 500){
        console.log("getAssignedRequest() in allrequest",response)
        return null
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      if (data !== null && data.status === 1){
        this.setState({
          totalSize: data.data.count,
          currentPage: 1,
          currentPageData: data.data.results.map((value, index) => ({...value, srno: index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')}))
        })
      }
    })
    .catch((error)=>{
      console.log("handleSubmit() in request",error)
    })
  }

  getPaginatedFulfilledRequests(page, searchText) {
    let searchParams = new URLSearchParams({
      "filter": FULLFILLEDREQUEST,
      "page": page,
      "searchtext": searchText,
      'requesttype' : this.props.requestType
    }) 
    let api_url = `${config.getFullfilledRequest}?${searchParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      searchParams = new URLSearchParams({
        "filter": FULLFILLEDREQUEST,
        "page": page,
        "searchtext": searchText,
        'requesttype' : this.props.requestType,
        "action": constants.admin_fullfill_request,
        "module":this.props.requestType +' '+ constants.admin_dashboard
      })
      api_url = `${config.getAdminRequestDetails}?${searchParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response) => {
      if (response.status === 500){
        console.log("getPaginatedFulfilledRequests (FulfilledRequest.js)", response)
        return null
      } else {
        return response.json()
      }  
    }).then((response) => {
      if (response !== null && response.data === null && this.state.searchText.length > 0) {
        this.setState({
          totalSize: 0,
          currentPage: 0,
          currentPageData: []
        })
      }
      if (response !== null && response.status === 1) {
        this.setState({
          totalSize: response.data.count,
          currentPage: page,
          currentPageData: response.data.results.map((value, index) => ({...value, srno: (page - 1) * this.state.sizePerPage + index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')})),
        })
      }
    }).catch((error) => {
      console.log("getPaginatedFulfilledRequests (FulfilledRequest.js)", error)
    })
  }

  onChangeHandler = (event) => {
    event.preventDefault();
    let name = event.target.name;
    let value = event.target.value;
    this.setState({ [name]: value });
  };

  mySubmitHandler = (event) => {
    event.preventDefault();
    this.setState({ isSubmit: "Yes" }, () => this.closeModal());
  };

  openModal = () => this.setState({ isOpen: true, isSubmit: "No" });

  closeModal = () => {
    if (this.state.isSubmit == "No") {
      this.setState({ isOpen: false });
      toast.warn("Operation Canceled...!", {
        position: toast.POSITION.TOP_CENTER,
      });
    } 
    else {
      this.setState({ isOpen: false });
      toast.info("Data Updated...!", { position: toast.POSITION.TOP_CENTER });
    }
  };

  onFollowChanged(row) {
    this.props.setId({ requestId: row.id });
    Router.push({
      pathname: "/RequestManager",
    });
  }

  async downloadNDA(row){
    let requestId = row.id
    let api_url = config.downloadNDA + requestId
    if (localStorage.getItem("role") == 'isSuperUser'){
      let queryParams = new URLSearchParams({
        "action": constants.admin_nda_download,
        "module": constants.admin_create_dashboard
      })
      api_url = `${config.downloadNDA + requestId}?${queryParams.toString()}`
    }
    // const response = await getAPIResponse(config.downloadNDA + requestId, 'GET',{})
    const response = await getAPIResponse(api_url, 'GET',{})
    if(!response.ok){
      throw new Error('Network response was not ok');
    }
    const blob = await response.blob();
    fileDownload(blob, `nda_form_${requestId}.pdf`)
  }

  onDeleteRow(row) {
    errorToast(PERMISSION_DENIED_FOR_DELETE_OPERATION)
  }

  linkFollow = (cell, row, rowIndex, formatExtraData) => {
    return (
      <div style={{display:"flex"}}>
        <a style={{ marginRight: "2px", cursor: "pointer", padding:'8px 8px' }} title="view details" className="btn-info btn-xs" onClick={() => {this.onFollowChanged(row)}}><i className="far fa-eye"></i></a>
        {this.state.currentPageData[rowIndex].isSynced===false && this.props.requestType === "Create"?
          <a style={{ marginRight: "2px", cursor: "pointer", padding:'8px 8px'}} title="Download NDA" className="btn btn-outline-info btn-xs" onClick={() => {this.downloadNDA(row)}}><i className="fa fa-download"></i></a>
          :this.state.currentPageData[rowIndex].isSynced===true && this.props.requestType === "Create"?
          <div className='sync-info'>
              <Image src={info} alt='information' width={20}/>
              <div className='info'>
                  <div className='desc'>
                      <div className='enable-desc'>
                          <p className='p-tb-one'>{"The NDA form cannot be downloaded for the employee because the Domain ID was not created via POP."}</p>
                      </div> 
                  </div>
              </div>
          </div> :
          null}
      </div>
    );
  };

  render() {
    
    return (
      <Fragment>
          <ToolkitProvider
            keyField="id"
            data={this.state.currentPageData}
            columns={this.state.headers}
            search
          >
            {(props) => (
              <div>
                <div className="text-right">
                  <SearchBar
                    {...props.searchProps}
                    className="custome-search-field"
                    placeholder="Search"
                  />
                </div>

                <BootstrapTable
                  pagination={paginationFactory({page: this.state.currentPage, sizePerPage: this.state.sizePerPage, totalSize: this.state.totalSize, hideSizePerPage: true})}
                  wrapperClasses="table-responsive"
                  striped
                  {...props.baseProps}
                  remote
                  onTableChange={(type, { page, searchText }) => {
                    if (type === SEARCH) {
                      this.setState({searchText: searchText})
                      this.getPaginatedFulfilledRequests(1, searchText)
                    }
                    if (type === PAGINATIONS) {
                      this.getPaginatedFulfilledRequests(page, this.state.searchText)
                    }       
                  }}
                />
              </div>
            )}
          </ToolkitProvider>
      </Fragment>
    );
  }
}
const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  requestType : state.counter.requestType
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(Fullfilled);
