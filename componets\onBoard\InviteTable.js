import React, { Fragment } from "react";
import { Component } from "react";
import "react-notifications-component/dist/theme.css";
import { ReactNotifications, Store } from "react-notifications-component";
import Router from "next/router";
import BootstrapTable from "react-bootstrap-table-next";
import paginationFactory from "react-bootstrap-table2-paginator";
import ToolkitProvider, { Search } from "react-bootstrap-table2-toolkit";
const { SearchBar } = Search;
import { Spinner, fieldset, Modal, Col, Card, Container, Row, Form} from "react-bootstrap";
import { connect } from "react-redux";
import { setId, dashboardBoxCount } from "../../redux/actions/counterActions";
import { getAPIResponse } from '../../constants/Utils'
import * as config from '../../constants/config'
import * as constants from '../../constants/constants'
import moment from "moment";
import { INTERNAL_SERVER_ERROR, INVITE, PAGINATIONS, SEARCH, SOMETHING_WENT_WRONG } from "../../constants/message";
import { successToast, errorToast } from "../../toast"
import { MdCancel } from "react-icons/md";
import { Button } from "antd";

class InviteTable extends Component {
  static getInitialProps({ store }) {}

  constructor(props) {
    super(props);
    this.state = {
      response: "",
      updateObj: "",
      headers: [
        { dataField: "srno", text: "Sr   No.", sort: true },
        { dataField: "id", text: "Request ID", sort: true },
        { dataField: "fullName", text: "Person Name", sort: true },
        { dataField: "tmlManager", text: "Reporting Manager", sort: true },
        { dataField: "createdBy", text: "Created By", sort: true },
        { dataField: "createdAt", text: "Created At", sort: true },
        { dataField: "status", text: "Request Status", sort: true },
        {dataField: "Action",text: "Action",formatter: this.linkFollow,sort: true}
      ],
      dataArray: [],
      isFollow: true,
      isOpen: false,
      isSubmit: "No",
      CompleteName: "",
      ContactNo: "",
      EmailID: "",
      CompanyCodeDesc: "",
      spinner: false,
      totalcomplete: "",
      requestId: "",
      rejectPopUp:false,
      requestID:"",
      remarkNote:"",
      isLoading:false,
      sendInvitePopUp:false,
      currentPage: 0,
      currentPageData: [],
      sizePerPage: constants.sizePerPage, 
      totalSize: 0,
      searchText: '',
      isSubmitDisable:true,
      isSendInviteDisable:false,
      isRetryDisableV2:{}
    };
  }

  
  componentDidMount() {
    this.getInviteExpiredList()
  }

  componentDidUpdate(prevProps) {
    if (prevProps.requestType !== this.props.requestType) {
      this.getInviteExpiredList()
    }
  }

  getInviteExpiredList(){
    let queryParams = new URLSearchParams({
      'filter' : INVITE,
      'requesttype' : this.props.requestType
    })
    let api_url = `${config.getInviteDetails}?${queryParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      queryParams = new URLSearchParams({
        'filter' : INVITE,
        'requesttype' : this.props.requestType,
        "action": constants.admin_invite_request,
        "module": this.props.requestType +' '+ constants.admin_dashboard
      })
      api_url = `${config.getAdminRequestDetails}?${queryParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response)=>{
      if(response.status === 500){
        console.log("getInviteExpiredList() in allrequest",response)
        return null
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      if(data !== null && data.status === 1){
        this.setState({
          totalSize: data.data.count,
          currentPage: 1,
          currentPageData: data.data.results.map((value, index) => ({...value, srno: index + 1 , createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')})),
        })
      }
      
    })
    .catch((error)=>{
      console.log("handleSubmit() in request",error)
    })
  }

  getPaginatedInviteList(page, searchText) {
    let searchParams = new URLSearchParams({
      "filter": INVITE,
      "page": page,
      "searchtext": searchText,
      'requesttype' : this.props.requestType
    })
    let api_url = `${config.getInviteDetails}?${searchParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      searchParams = new URLSearchParams({
        "filter": INVITE,
        "page": page,
        "searchtext": searchText,
        'requesttype' : this.props.requestType,
        "action": constants.admin_invite_request,
        "module":this.props.requesttype +' '+ constants.admin_dashboard
      })
      api_url = `${config.getAdminRequestDetails}?${searchParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response) => {
      if (response.status === 500){
        console.log("getPaginatedInviteList (InviteTable.js)", response)
        return null
      } else {
        return response.json()
      }  
    }).then((response) => {
      if (response !== null && response.data === null && this.state.searchText.length > 0) {
        this.setState({
          totalSize: 0,
          currentPage: 0,
          currentPageData: []
        })
      }
      if (response !== null && response.status === 1) {
        this.setState({
          totalSize: response.data.count,
          currentPage: page,
          currentPageData: response.data.results.map((value, index) => ({...value, srno: (page - 1) * this.state.sizePerPage + index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')})),
        })
      }
    }).catch((error) => {
      console.log("getPaginatedInviteList (InviteTable.js)", error)
    })
  }

  onSendInvite(row) {
    this.handleRetryDisableClick(row.id,true)
    let body = {
      "id" : row.id 
    }
    let api_url = config.sendInvite
    if (localStorage.getItem("role") == 'isSuperUser'){
      body['action'] = constants.admin_send_invite
      body['module'] = constants.admin_dashboard
      api_url = config.adminSendInvite
    }
    getAPIResponse(api_url , "POST" , body)
    .then((response)=>{
      if(response.status === 500){
        errorToast(INTERNAL_SERVER_ERROR)
        console.log("getInviteExpiredList() in allrequest",response)
        return null
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      if(data !== null){
        if(data.status === 1){
          successToast(data.message)
          this.getInviteExpiredList()
        }
        else{
          errorToast(data.message)
          this.handleRetryDisableClick(row.id,false)
        }
      }
      
    })
    .catch((error)=>{
      console.log("onSendInvite() in InviteTable",error)
      errorToast(SOMETHING_WENT_WRONG)
      this.handleRetryDisableClick(row.id,false)
    })
    
  }

  onChangeHandler = (event) => {
    event.preventDefault();
    let name = event.target.name;
    let value = event.target.value;
    this.setState({ [name]: value });
  };

  onFollowChanged(row) {
    this.props.setId({ requestId: row.id });
    Router.push({
      pathname: "/RequestManager",
    });
  }

  onDeleteRow(){
    if(this.state.remarkNote !== ""){
      this.setState({
        isLoading:true,
        isSubmitDisable:true
      })
      let body = {
        "id":this.state.requestID,
        "requestStatus":"Cancel",
        "remark": `${"SPOC Reject Note"} - ${this.state.remarkNote.trim()}`
      };
      let api_url = config.cancelRequest
      if (localStorage.getItem("role") == 'isSuperUser'){
        body['action'] = constants.admin_cancel_invite
        body['module'] =this.props.requesttype +' '+ constants.admin_dashboard
        body['remark'] = `${"Admin Reject Note"} - ${this.state.remarkNote.trim()}`
        api_url = config.adminCancelRequest
      }
      getAPIResponse(api_url , "POST" , body)
      .then((response) => {
        if(response.status === 500){
          this.setState({
            isLoading:false,
            rejectPopUp:false,
            isSubmitDisable:false
          })
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("onDeleteRow() in InviteTable",response)
          return null 
        }
        else{
          return response.json()
        }  
      })
      .then((data) => {
        if(data !== null){
          if(data.status === 1) {
            this.setState({
              isLoading:false,
              rejectPopUp:false
            })
            successToast(data.message)
            this.getPaginatedInviteList(this.state.currentPage, this.state.searchText)
          }
          else {
            this.setState({
              isLoading:false,
              isSubmitDisable:false
            })
            console.log("onDeleteRow() in InviteTable",data);
            errorToast(data.message)
          }
        }else {
          console.log("onDeleteRow() in InviteTable",data);
        }
        })
        .catch((error) => {
          this.setState({
            isSubmitDisable:false
          })
          console.log("onDeleteRow() in InviteTable",error)
        });
    }
}

  goNext = () => {
    Router.push({
      pathname: "/Dashboard",
    });
  };

  openRejectpopUp = (row) => {
    this.setState({
      rejectPopUp : true,
      requestID : row.id
    })
  } 

  closeRejectpopup = () => {
    this.setState({rejectPopUp : false})
  }

  rejectReasonInput = (e) => {
    const regex = /[^A-Za-z0-9\s]+$/;
    if(!regex.test(e.target.value)){
        this.setState({
          remarkNote : e.target.value,
          isSubmitDisable:false
        },()=>{this.state.remarkNote})
    }
  }

  handleRetryDisableClick=(id,value)=>{
    this.setState(prevState=>({
      isRetryDisableV2: {
        ...prevState.isRetryDisableV2,
        [
          id
        ]: value,
      },
    }));
  };

  linkFollow = (cell, row, rowIndex, formatExtraData) => {
    if (!row.isExpired && row.status === "OTP Verification Pending with User" || row.status === "NDA Acceptance Pending with User") {
      return (
        <div>
          <a style={{ marginRight: "5px", cursor: "pointer", padding:'8px 8px' }} title="view details" className="btn-info btn-xs" onClick={() => {this.onFollowChanged(row,cell)}}><i className="far fa-eye"></i></a> 
          <a style={{ marginRight: "5px", cursor: "pointer", padding:'8px 8px' }} title ="Delete Record" className="btn-danger btn-xs" onClick={()=>{this.openRejectpopUp(row)}}><i className="fas fa-trash-alt"></i></a>
        </div>
      );
    } 
    else if(row.isExpired && row.status === "OTP Verification Pending with User" || row.status === "NDA Acceptance Pending with User"){
      return (
        <div>
          <a style={{ marginRight: "5px", cursor: "pointer", padding:'8px 8px' }} title="view details" className="btn-info btn-xs" onClick={() => {this.onFollowChanged(row,cell)}}><i className="far fa-eye"></i></a> 
          <a style={{ marginRight: "5px", cursor: "pointer", padding:'8px 8px' }} title ="Delete Record" className="btn-danger btn-xs" onClick={()=> {this.openRejectpopUp(row)}}><i className="fas fa-trash-alt"></i></a>
          <Button style={{ marginLeft: "5px", cursor: "pointer", padding:'4px 10px'}} onClick={()=>{this.onSendInvite(row)}} disabled={formatExtraData.disabledButtons[row.id]}>Send Invite</Button>
        </div>
      )
    }
    else {
      return (
        <div>
          {/* <button style={{ marginRight: "2px", cursor: "pointer" }} disabled={!row.isExpired} title="view details" className="btn-info btn-xs" onClick={() => {this.onSendInvite(row);}}>Send Invite</button> */}
          <a style={{ marginRight: "2px", cursor: "pointer",padding:'8px 8px' }} title="view details" className="btn-info btn-xs" onClick={() => {this.onFollowChanged(row,cell);}}><i className="far fa-eye"></i></a> 
        </div>
      );
    }
  };

  render() {
    const tableColumns=this.state.headers.map(col=>{
      if(col.dataField==='Action'){
        return{
          ...col,
          formatExtraData: {
            disabledButtons:this.state.isRetryDisableV2
          }
        };
      }
      return col;
    });
    return (
    <>
      <Fragment>
        <ReactNotifications />
        <ToolkitProvider
          keyField="id"
          data={this.state.currentPageData}
          columns={tableColumns}
          search
        >
          {(props) => (
            <div>
              <div className="text-right">
                <SearchBar
                  {...props.searchProps}
                  className="custome-search-field"
                  placeholder="Search"
                />
              </div>
              <BootstrapTable
                pagination={paginationFactory({page: this.state.currentPage, sizePerPage: this.state.sizePerPage, totalSize: this.state.totalSize, hideSizePerPage: true})}
                wrapperClasses="table-responsive"
                striped
                {...props.baseProps}
                remote
                columns={tableColumns}
                onTableChange={(type, { page, searchText }) => {        
                  if (type === SEARCH) {
                    this.setState({searchText: searchText})
                    this.getPaginatedInviteList(1, searchText)
                  }
                  if (type === PAGINATIONS) {
                    this.getPaginatedInviteList(page, this.state.searchText)
                  }       
                }}
              />
            </div>
          )}
        </ToolkitProvider>

                <Modal
                 show={this.state.rejectPopUp}
                 onHide={this.closeRejectpopup}
                 aria-labelledby="contained-modal-title-vcenter"
                 centered
                >
                  <Modal.Header>
                    <Modal.Title id="contained-modal-title-vcenter">
                      Remark
                    </Modal.Title>
                    <div style={{cursor:"pointer"}}onClick={this.closeRejectpopup}>
                      <MdCancel />  
                    </div>
                  </Modal.Header>
                  <Modal.Body>
                    <Row className="reject-header">
                      <Form.Group controlId="RequestID">
                        <Form.Label>Note<sup style={{color:'red', fontWeight:'900'}}>*</sup>:</Form.Label>
                      </Form.Group>
                      <Form.Group controlId="RequestID">
                        <Form.Label>Request ID :  </Form.Label>
                        <span>{this.state.requestID}</span>
                      </Form.Group>
                    </Row>
                    <>
                        <Form.Group controlId="remark">
                          <Form.Control 
                          as='textarea' 
                          rows={4}
                          value={this.state.remarkNote}
                          style={{resize:"none"}}
                          onChange={(e)=>{this.rejectReasonInput(e)}}
                          />
                        </Form.Group>
                    </>
                  </Modal.Body>
                  <Modal.Footer>
                    <Button variant="primary" disabled={this.state.isSubmitDisable} onClick={()=>{this.onDeleteRow()}}>Submit</Button>
                    <Button variant="danger" onClick={this.closeRejectpopup}>Cancel</Button>
                  </Modal.Footer>
                </Modal>

      </Fragment>
      {/* {
        this.state.isSendInviteDisable ?
        <div className="loader">
          <div></div>
        </div> : null
      } */}
      </>
    );
  }
}
const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  countData: state.counter.countData,
  requestType: state.counter.requestType
});

const mapDispatchToProps = {
  setId: setId,
  dashboardBoxCount: dashboardBoxCount,
};
export default connect(mapStateToProps, mapDispatchToProps)(InviteTable);
