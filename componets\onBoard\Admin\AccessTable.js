import { Component , Fragment} from "react";
import Toolkit<PERSON>rovider, { Search } from "react-bootstrap-table2-toolkit";
const { SearchBar } = Search;
import AdminTable from "./AdminTable";
import { getAPIResponse } from "../../../constants/Utils";
import { userList, offBoardUser, getVendor<PERSON>ist, onBoardUser, validateUser } from "../../../constants/config";
import { admin_user,admin_user_listing,admin_add_user,admin_update_user,admin_validate_user } from "../../../constants/constants";
import { errorToast, successToast } from "../../../toast";
import * as message from '../../../constants/message' 
import { Select, Switch } from 'antd'
import { AiOutlineClose } from "react-icons/ai";
import { IoMdPersonAdd } from "react-icons/io";
import { Tooltip } from 'antd';
import { FaRegEdit,Fa<PERSON>rrow<PERSON>ef<PERSON>,FaArrowRight } from "react-icons/fa";
import { FaSearch } from "react-icons/fa";
import { FaCheck } from "react-icons/fa";
import { faSalesforce } from "@fortawesome/free-brands-svg-icons";
class AccessTable extends Component{
    static getInitialProps({ store }) { }
  constructor(props) {
    super(props);
    this.state = {
        defaultHeaders : [
            {dataField: "srno", text: "Sr No.", sort: true },
            {dataField:"fullName", text:"Person Name", sort:true},
            {dataField:"vendor", text:"Vendor Name", sort:true},
            {dataField:"email",text:"Email ID", sort:true},
            {dataField:"userRole",text:"User Role", sort:true},
            {dataField:"is_active", text:"Active", sort:true}
        ],
        currentPageData:[],
        vendorOption:[],
        defaultIsActive:true,
        isPopupOpen:false,
        email:"",
        emailErrorMessage:"",
        firstName:"",
        lastName:"",
        vendor:[],
        firstNameErrorMessage:"",
        lastNameErrorMessage:"",
        vendorErrorMessage:"",
        isActive:true,
        totalCount:0,
        currentPage:1,
        searchText:"",
        prev: null,
        next: null,
        pages: 1,
        isValidUser:true,
        isLoader:false,
        isLoading: false, //for page loading
        isBtnDisabled:false,
        roleOptions:[
            {value:"spoc",label:"SPOC"},
            {value:"admin",label:"Admin"}
        ],
        roleErrorMessage:"",
        userSelectedRole:"spoc"
    } 
  }

  componentDidMount() {
    this.getSpocList()
    this.getVendorList()
  }

  getSpocList = () => {
    this.setState({
        isLoading: true
    })
    let req_body ={
        "page": this.state.pages
    }
    if(this.state.searchText.length > 0){
        req_body = {
            "searchText": this.state.searchText,
            "page": this.state.pages
        }
        if(this.state.searchText.length >= 3){
            console.log("inside if:",this.state.searchText.length)
            req_body = {
                "searchText": this.state.searchText,
                "page": this.state.pages
            }
        }
        else if(this.state.searchText.length <= 0){
            req_body = {
                "page": this.state.pages
            }
        }
    }
    req_body['action'] = admin_user_listing
    req_body['module'] = admin_user
    getAPIResponse(userList,'POST',req_body)
    .then((response)=>{
        if(response.status === 500){
            errorToast(message.INTERNAL_SERVER_ERROR)
            console.log(response)
            return null
        }
        else{
            return response.json()
        }
    })
    .then((data)=>{
        if(data !== null && data.status === 1){
            this.setState({
                currentPageData: data.data.results.map((value,index) => ({...value,srno: this.state.pages != 1 ? ((this.state.pages - 1) * 10) + index + 1 : index + 1,is_active: <Switch defaultChecked={value.is_active} onChange={()=>{this.offBoard(value)}}/>})),
                totalCount:data.data.count,
                // prev: data.data.total_page_count < this.state.pages || this.state.pages != 1,
                // next: data.data.total_page_count > this.state.pages || this.state.pages != data.data.total_page_count,
                prev: data.data.previous != null,
                next: data.data.next != null,

            })
        }
        else{
            errorToast(message.SOMETHING_WENT_WRONG)
            console.log(data)
        }
    })
    .catch((error)=>{
        console.log(error)
        errorToast(message.SOMETHING_WENT_WRONG)
    })
    this.setState({
        isLoading: false
    })
  }

  offBoard = (value) => {
    console.log(value)
    let body = {
        email:value.email,
        isActive:!value.is_active,
        "action": admin_update_user,
        "module": admin_user,
        "previousData":{isActive:value.is_active},
        "updateData":{isActive:!value.is_active}
    }
    getAPIResponse(offBoardUser,'POST',body)
    .then((response)=>{
        if(response.status === 500){
            errorToast(message.INTERNAL_SERVER_ERROR)
            console.log(response)
            return null
        }
        else{
            return response.json()
        }
    })
    .then((data)=>{
        if(data !== null){
            if(data.status === 1){
                successToast(data.message)
                this.getSpocList()
            }
            else {
                errorToast(data.message)
            }
        }
        else{
            errorToast(message.SOMETHING_WENT_WRONG)
        }
    })
    .catch((error)=>{
        errorToast(message.SOMETHING_WENT_WRONG)
        console.log(error)
    })
  }

  onBoardUser = () => {
    this.setState({
        isLoading: true,
        isBtnDisabled:true
    })
    if(this.state.email === "" || this.state.email === null){
        this.setState({
            emailErrorMessage:"Please enter a email ID"
        })
    }
    else if(this.state.firstName === "" || this.state.firstName === null){
        this.setState({
            firstNameErrorMessage:"Please enter first name"
        })   
    }
    else if(this.state.lastName === "" || this.state.lastName === null){
        this.setState({
            lastNameErrorMessage:"Please enter last name"
        })
    }
    else if(this.state.vendor === "" || this.state.vendor === null){
        this.setState({
            vendorErrorMessage:"Please select a vendor name"
        })
    }
    else {
        let body = {
            firstName:this.state.firstName,
            lastName:this.state.lastName,
            email:this.state.email,
            vendor:this.state.vendor,
            is_active:this.state.isActive,
            "action": admin_add_user,
            "module": admin_user,
            "role":this.state.userSelectedRole
        }
        getAPIResponse(onBoardUser,"POST",body)
        .then((response)=>{
            if(response.status === 500){
                errorToast(message.INTERNAL_SERVER_ERROR)
                return null
            }
            else {
                return response.json()
            }
        })
        .then((data)=>{
            if(data !== null && data.status === 1){
                successToast(data.message)
                this.setState({
                    isPopupOpen:false
                },()=>{this.onclosePopUp(),this.getSpocList()})
                
            }
            else{
                errorToast(data.message)
            }
        })
        .catch((error)=>{
            errorToast(message.SOMETHING_WENT_WRONG)
            console.log(error)
        })
    }
    this.setState({
        isLoading: false,
        isBtnDisabled:false
    })
  }

  getVendorList = () => {
    getAPIResponse(getVendorList , "GET" , {})
    .then((response) => {
      if(response.status === 500){
        errorToast(message.INTERNAL_SERVER_ERROR)
        console.log("getVendorList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null && data.status === 1){
        let vendorData = data.data.map((data)=>({value:data.vendorCode,label:data.vendorName.toUpperCase()}))
        this.setState({
          vendorOption : vendorData 
        })
      }
      else {
        console.log("getVendorList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getVendorList() in Request",error)
    })

  }

  searchText = (e)=>{
    this.setState({
        searchText: e.target.value,
        pages: 1
    },()=>{
        if(this.state.searchText.length >= 3){
            this.getSpocList()
            this.getVendorList()
        }
        else if(this.state.searchText.length <= 0){
            this.getSpocList()
            this.getVendorList()
    }
    });
  }

  validateSpoc = () => {
    const regex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/i;
    if(this.state.email === "" || this.state.email === null){
        this.setState({
            emailErrorMessage:"Please enter a email ID"
        })
    }
    else if(this.state.email !== '' && !regex.test(this.state.email)){
        this.setState({
          emailErrorMessage:"Please enter a valid email ID",
        })
      }  
    else {
        this.setState({
            isLoader : true
        })
        let body = {
            "mail" : this.state.email,
            "action": admin_validate_user,
            "module": admin_user
        }
        getAPIResponse(validateUser,"POST",body)
        .then((response)=>{
            if(response.status === 500){
                errorToast(message.INTERNAL_SERVER_ERROR)
                console.log("validateSpoc in AccessTable",response)
                return null
            }
            else {
                return response.json()
            }
        })
        .then((data)=>{
            if(data !== null){
                if(data.status === 1) {
                    this.setState({
                        firstName : data.data.first_name,
                        lastName: data.data.last_name,
                        isValidUser:false,
                        isLoader:false
                    })
                }
                else {
                    this.setState({
                        emailErrorMessage : data.message,
                        isLoader:false
                    })
                }
            }
        })
    }
  }
  
  onChangeEmail = () => {
    const regex = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/i;
    console.log(this.state.email !== '' && !regex.test(this.state.email))
    if(this.state.email !== '' && !regex.test(this.state.email)){
      this.setState({
        emailErrorMessage:"Please enter a valid email ID",
      })
    }  
  }

  inputValidation = (value,name) => {
    if(this.state.firstNameErrorMessage){this.setState({firstNameErrorMessage : ""})}
    if(this.state.lastNameErrorMessage)(this.setState({lastNameErrorMessage:""}))
    const regex = /[^A-Za-z0-9\s]+$/;
    if(!regex.test(value)){
        this.setState({
            [name] : value
        })
    }
  }

  onclosePopUp = () => {
    this.setState({
        isPopupOpen:false,
        isActive:false,
        vendor:[],
        lastName:"",
        firstName:"",
        email:"",
        isValidUser:true,
        isLoader:false,
        isActive:false,
        vendorErrorMessage:"",
        lastNameErrorMessage:"",
        firstNameErrorMessage:"",
        emailErrorMessage:"",
    })
  }

  onHandleVendorDropdown = (e) => {
    this.setState({
        vendor:e,
        vendorErrorMessage:""
    })
  }

  onHandleRoleDropdown = (e) => {
    this.setState({
        userSelectedRole:e,
        roleErrorMessage:""
    })
  }

  resetForm = () => {
    this.setState({
        firstName:"",
        emailErrorMessage:"",
        email:"",
        vendorErrorMessage:"",
        lastNameErrorMessage:"",
        firstNameErrorMessage:"",
        vendor:[],
        lastName:"",
        isLoader:false,
        isValidUser:true,
        isActive:false,
        isLoading:false,
        isBtnDisabled:false
    })
  }


  render() {
    console.log("this.state:",this.state)
    return (
        <>
        {this.state.isPopupOpen ?
        <>
            <div className="popup-wrapper"></div>
            <div className="popup-center">
                <div className="popup-container">
                <div style={{width:"100%"}}>
                    <div className="close-popup" onClick={()=>{this.onclosePopUp()}}>
                        <AiOutlineClose/>
                    </div>
                    <h3 style={{textAlign:"center",fontWeight:"bolder"}}>Add User</h3>
                </div>
                <div className="popup-form">
                    <div className="display-flex-column">
                        <div className="popup-input display-flex-1">
                            <label className="popup-lable">Email ID<span style={{color:'red', fontWeight:'900'}}>*</span>
                            </label>
                            <div className="validate-spoc-email">
                                <div className="input-box">
                                    <input placeholder="Please Enter Email ID" onChange={(e) => { this.setState({ email: e.target.value, emailErrorMessage: "" }) }} onBlur={this.onChangeEmail} value={this.state.email} />
                                </div>
                                {this.state.isLoader ?
                                    <button className="clear-button">
                                        <div className="loader-small"></div>
                                    </button> :
                                    !this.state.isValidUser ?
                                        <button className="clear-button" disabled={this.state.validateDomainID}>
                                            <FaCheck className="clear-icon" />
                                        </button> :
                                        <div className="clear-button" onClick={this.validateSpoc}>
                                            <FaSearch className="clear-icon" />
                                        </div>
                                }
                            </div>
                            {this.state.emailErrorMessage ? <span className="error-message">{this.state.emailErrorMessage}</span> : null}
                        </div>
                        <div className="popup-input display-flex-1">
                            <label className="popup-lable">Role<span style={{color:'red', fontWeight:'900'}}>*</span>
                            </label>
                            <div className="input-box">
                                <Select className="input-box-select" value={this.state.userSelectedRole} placeholder="Please Select Role" options={this.state.roleOptions} onChange={(e) => { this.onHandleRoleDropdown(e) }} disabled={this.state.isValidUser}/>
                            </div>
                            {this.state.roleErrorMessage ? <span className="error-message">{this.state.roleErrorMessage}</span> : null}
                        </div>
                    </div>
                    <div className="display-flex-column">
                        <div className="popup-input display-flex-1">
                            <label className="popup-lable">First Name<span style={{color:'red', fontWeight:'900'}}>*</span>
                            </label>
                            <div className="input-box">   
                                <input placeholder="Please Enter First Name" onChange={(e)=>{this.inputValidation(e.target.value,"firstName")}} value={this.state.firstName} disabled={this.state.isValidUser}/>
                            </div>
                            {this.state.firstNameErrorMessage ? <span className="error-message">{this.state.firstNameErrorMessage}</span> : null}
                        </div>
                        <div className="popup-input display-flex-1">
                            <label className="popup-lable">Last Name<span style={{color:'red', fontWeight:'900'}}>*</span>
                            </label>
                            <div className="input-box">   
                                <input placeholder="Please Enter Last Name" onChange={(e)=>{this.inputValidation(e.target.value,"lastName")}} value={this.state.lastName} disabled={this.state.isValidUser}/>
                            </div>
                            {this.state.lastNameErrorMessage ? <span className="error-message">{this.state.lastNameErrorMessage}</span> : null}
                        </div>
                    </div>
                    <div className="display-flex-column">
                        {this.state.userSelectedRole == 'spoc' ? 
                            <div className="popup-input display-flex-1">
                                <label className="popup-lable">Vendor Name<span style={{color:'red', fontWeight:'900'}}>*</span>   
                                </label>
                                <div className="input-box">   
                                    <Select className="input-box-select" value={this.state.vendor} placeholder="Please Select Vendor" options={this.state.vendorOption} onChange={(e)=>{this.onHandleVendorDropdown(e)}} disabled={this.state.isValidUser}/>
                                </div>
                                {this.state.vendorErrorMessage ? <span className="error-message">{this.state.vendorErrorMessage}</span> : null}
                            </div>
                        :null}
                        <div className="popup-input display-flex-1">
                            <label className="popup-lable">Active</label>
                            <div className="input-box">  
                                <Switch value={this.state.isActive} onChange={(e)=>{this.setState({isActive:e})}} disabled={this.state.isValidUser}/>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="save">
                    <button disabled={this.state.isBtnDisabled} className="save-button" onClick={this.onBoardUser}>Save</button>
                    <button disabled={this.state.isBtnDisabled} className="reset-button-popup" onClick={this.resetForm}>Reset</button>
                </div>
                </div>
            </div>
        </> : null
        }
            <div className="border content-wrapper justify-space-between">
                <div className="text-right" style={{gap:0}}>
                        <div className="search-bar">
                            <input
                                type="text"
                                placeholder={"Search by Email ID"}
                                value={this.state.searchText}
                                onChange={(e)=>{this.searchText(e)}}
                            />
                            {this.state.searchText && (
                                <span className="clear-iconV2" onClick={this.clearInput}>x</span>
                            )}
                        </div>
                    {/* </div> */}
                    {/* <div className="search-bar-spoc align-center" onClick={()=>{this.setState({isPopupOpen : true})}}> */}
                    <div className="button-paginate" onClick={()=>{this.setState({isPopupOpen : true})}}>
                        <Tooltip title="Add User" color="#FFF" overlayInnerStyle={{color:"#000000",fontWeight:"normal"}}>
                            <IoMdPersonAdd size={'1.2rem'} />
                        </Tooltip>
                    </div>
                </div>
                
                <AdminTable headers={this.state.defaultHeaders} currentPageData={this.state.currentPageData} />
                <div className='fixed-width'>
                    <div className="paginator-space">
                        <div className='flex-d' >
                            <div className='page-label'>
                            {`Total Record: ${this.state.totalCount}`}
                            </div>
                        </div>
                    <div className='pagination'>
                        <div className={this.state.prev === true  ? 'button-paginate' : 'button-paginate disabled'} onClick={this.state.prev === true ? () => { this.setState({ pages: this.state.pages - 1, isLoading: true }, () => { this.getSpocList() })} : null}>
                            <FaArrowLeft style= {{display:'inline-block', alignItems:'center'}} size={'0.8rem'}/> 
                            {" Previous"}
                        </div>
                        <div className={'page-label'}>{this.state.pages}</div>
                            <div className={this.state.next === true  ? 'button-paginate' : 'button-paginate disabled'} onClick={this.state.next === true ? () => { this.setState({ pages: this.state.pages + 1, isLoading: true }, () => { this.getSpocList() })} : null}>
                            {"Next "}
                            <FaArrowRight style= {{display:'inline-block', alignItems:'center'}} size={'0.8rem'}/> 
                        </div>
                    </div>
                </div>
            </div>
            </div>
            {
                this.state.isLoading ?
                <div className="loader">
                    <div></div>
                </div> : null
            }
        </>
    )
  }

}

export default AccessTable