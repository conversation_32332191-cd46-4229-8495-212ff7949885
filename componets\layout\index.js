import Sidebar from "../Sidebar";
import Header from "../Header";
import Footer from "../Footer";
import { usePathname } from "next/navigation";
import { urlList } from "../../constants/constants";

export default function Layout({ children }) {
    const pathname = usePathname()
    return (
        urlList.includes(pathname) ?
        children :
        <div className="main-flex">
            <div className="sidebar-flex">
                <Sidebar/>
            </div>
            <div className="content-flex">
                <div className="header-flex">
                    <Header/>
                </div>
                
                <div className="main-content-center">
                    {children}
                </div>
            </div>
        </div>
    )
  }