import React, { Component } from "react";
import { connect } from "react-redux";
import { setId} from "../../../redux/actions/counterActions";
import CreateTabs from "./AdminCreateTabs";
import UpdateTabs from "./AdminUpdateTabs";
import DisableTabs from "./AdminDisableTabs";
import Box from "../Box";


class AllTabs extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      activeKeyCreate: "invite",
      activeKeyUpdate:"assigned",
      activeKey:"invite"
    };
  }

  handleShow = (e) => {
    this.setState({
      activeKey: e,
    });

  };

  render() {
    const userRole = this.props.loginData;
    const empId = this.props.loginData.empId;
    return (
        <div>
          <Box baseUrl={this.props.baseUrl} loginData={this.props.loginData}/>
          {this.props.requestType === 'Create' ? <CreateTabs loginData={this.props.loginData}/> :this.props.requestType == 'Update'? <UpdateTabs loginData={this.props.loginData}/> : <DisableTabs loginData={this.props.loginData}/>}
        </div>
      );
    } 
  }

const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
  requestType: state.counter.requestType
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(AllTabs);
