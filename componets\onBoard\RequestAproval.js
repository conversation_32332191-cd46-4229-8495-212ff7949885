import { Component } from "react";
import { connect } from "react-redux";
import { setId } from "../../redux/actions/counterActions";
import React, { useState } from "react";
import { ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form, } from "react-bootstrap";
import { CardBox } from "../common";
import { DateTime } from "luxon";
import SetpUl from "./SetpUl";
import { getAPIResponse } from "../../constants/Utils";
import * as config from "../../constants/config";
import Select from "react-select";
import { ChangeLogFiels, changeLogList,admin_request_details,admin_request_screen } from "../../constants/constants";
import { fas } from "@fortawesome/free-solid-svg-icons";
import { INTERNAL_SERVER_ERROR } from "../../constants/message";
import { errorToast } from "../../toast";
import { <PERSON>aEye, FaEyeSlash } from "react-icons/fa";
import { Tooltip } from 'antd';


class RequestAproval extends Component {
  static getInitialProps({ store }) {}

  constructor(props) {
    super(props);
    this.state = {
      personName: "",
      vendor: "",
      vendorValue: "",
      personMobile: "",
      personEmail: "",
      manager: "",
      acceptance: "",
      checked: true,
      ndalfag: false,
      emailfag: true,
      checkedValue: "Reject",
      isOpen: false,
      PersNo: "",
      titleCheckBtn: "please click checkbox if accept NDA",
      EmailID: "",
      location: "",
      step: "",
      msg: "",
      tmlManagerEmail: "",
      gender: "",
      designation: "",
      employeeId: "",
      projectName: "",
      tmlManagerEmail: "",
      validTill: "",
      vendorManagerEmail: "",
      vendorManagerName: "",
      managerApprovalDate: "",
      city: "",
      department: "",
      yearOfExperience: "",
      skill: "",
      tmlOffice: "",
      swipeLocation:"",
      buCompanyCode:"",
      tmlRegion: "",
      vendorOption: [],
      designationList: [],
      project: [],
      tmlLocationData: [],
      tmlRegionData: [],
      skillData: [],
      response: "",
      skillListData: [],
      isLoading: false,
      tmlOfficeName: "",
      tmlRegionName: "",
      skillsArray: [],
      remark: "",
      requestFinalStatus: "",
      requestID: "",
      requestFinalType: "",
      towerData: [],
      towerName: "",
      towerShownForManager: false,
      domainIdShown: false,
      domainID: "",
      requestType: "",
      detailsView: true,
      changeLogView: false,
      isEmailCheck: false,
      isInternetCheck: false,
      changeLogsArray: [],
      isDomainCheck: true,
      edata: null,
      employeeType:"",
      ssiCode:"",
      edataDepartment:"",
      showMaskedContent:false,
      l2ManagerEmail:"",
      l2ManagerName:""
    };
    // this.handleSubmit.bind(this);
  }

  componentDidMount() {
    this.getDetailsByRequestId();
    this.getTower();
  }

  getTower = () => {
    getAPIResponse(config.getTowerList, "GET", {})
      .then((response) => {
        if (!response.ok) {
          console.log("getSkill() in Request", response);
          return null;
        } else {
          return response.json();
        }
      })
      .then((data) => {
        if (data !== null) {
          data.data.map((obj, index) => {
            this.setState(
              (prevState) => ({
                towerData: [
                  ...prevState.towerData,
                  { value: obj.towerCode, label: obj.towerName },
                ],
              }),
              () => this.getTowerName()
            );
          });
        } else {
          console.log("getTower() in Request", response);
        }
      })
      .catch((error) => {
        console.log("getTower() in Request", error);
      }).finally(() => {
        this.setState({isLoading: false})
      });;
  };

  getTowerName = () => {
    this.state.towerData.map((obj, index) => {
      if (obj.value === this.state.edata?.tower) {
        this.setState({
          towerName: obj,
        });
      }
    });
  };

  getSkillDropdownData = (list) => {
    list.map((value, index) => {
      this.setState((prevState) => ({
        skillsArray: [
          ...prevState.skillsArray,
          { label: value.skillName, value: value.skillName },
        ],
      }));
    });
  };

  getdropdownObject = (value, label, field) => {
    let object = { lable: label, value: value };
    this.setState({
      [field]: object,
    });
    return object;
  };

  getTowerNameFromCode = (towerName) => {
    return this.state.towerData
      .filter((value) => value.value === towerName)
      ?.at(0)?.label;
  };

  changeLogList = (value) => {
    if (value !== "") {
      let result = [];
      let Keys = Object.keys(value);
      Keys.map((key) => {
        if (ChangeLogFiels[key] !== undefined) {
          const object = value[key];

          if (key === "vendorManagerEmail") {
            object.old = !this.state.showMaskedContent ? this.maskEmail(object.old)  : object.old;
            object.new = !this.state.showMaskedContent ? this.maskEmail(object.new) : object.new;
          }
          if (key === "email") {
            object.old = !this.state.showMaskedContent ? this.maskEmail(object.old)  : object.old;
            object.new = !this.state.showMaskedContent ? this.maskEmail(object.new) : object.new;
          }
          if (key === "mobile") {
            object.old = !this.state.showMaskedContent ? this.maskMobileNumber(object.old)  : object.old;
            object.new = !this.state.showMaskedContent ? this.maskMobileNumber(object.new) : object.new;
          }

          if (key === "edata.tower") {
            object.old = this.getTowerNameFromCode(value[key].old);
            object.new = this.getTowerNameFromCode(value[key].new);
          }
          object["Field_name"] = ChangeLogFiels[key];
          result.push(object);
        }
      });
      console.log("changes", this.state.changeLogsArray);
      this.setState(prevState=>({...prevState,changeLogsArray:[...result]}))
      }
    };

  // get details from request id
  getDetailsByRequestId = () => {
    let id = this.props.requestId;
    if (id != undefined) {
      this.setState({
        isLoading: true,
        requestID: this.props.requestId,
      });
      let api_url = `${config.getDetailsByRequestId}/${id}`
      if (localStorage.getItem("role") == 'isSuperUser'){
        let queryParams = new URLSearchParams({
          "action": admin_request_details,
          "module": admin_request_screen
        })
        api_url = `${config.getDetailsByRequestId}/${id}?${queryParams.toString()}`
      }
      getAPIResponse(api_url , "GET" , {})
      // getAPIResponse(`${config.getDetailsByRequestId}/${id}`, "GET", {})
        .then((response) => {
          if (response.status === 500) {
            this.setState({
              isLoading: false,
            });
            errorToast(INTERNAL_SERVER_ERROR);
            console.log("getDetailsByRequestId() in requestApproval", response);
            return null;
          } else {
            return response.json();
          }
        })
        .then((data) => {
          if (data !== null) {
            if (data.status === 1) {
              this.setState(
                {
                  isLoading: false,
                  response: data.data,
                  towerShownForManager:
                    data.data.ndaEmployeeDetails.edata &&
                    data.data.ndaEmployeeDetails.edata !== null &&
                    data.data.ndaEmployeeDetails.edata?.tower,
                  domainIdShown:
                    data.data.ndaEmployeeDetails.domainId &&
                    data.data.ndaEmployeeDetails.domainId !== null,
                  id: data.data.id,
                  personEmail: data.data.ndaEmployeeDetails.email,
                  personMobile: data.data.ndaEmployeeDetails.mobile,
                  personName: `${data.data.ndaEmployeeDetails.firstName} ${
                    data.data.ndaEmployeeDetails.middleName ?? ""
                  } ${data.data.ndaEmployeeDetails.lastName}`,
                  vendor: {
                    label: data.data.ndaEmployeeDetails.vendor?.vendorName,
                    value: data.data.ndaEmployeeDetails.vendor?.vendorCode,
                  },
                  tmlManagerEmail: data.data.tmlManagerEmail,
                  gender: data.data.ndaEmployeeDetails.gender,
                  designation: {
                    label:
                      data.data.ndaEmployeeDetails.designation?.designationName,
                    value:
                      data.data.ndaEmployeeDetails.designation?.designationCode,
                  },
                  employeeId: data.data.ndaEmployeeDetails.employeeId,
                  projectName: {
                    label: data.data.ndaEmployeeDetails.project?.projectName,
                    value: data.data.ndaEmployeeDetails.project?.projectCode,
                  },
                  tmlManagerEmail:
                    data.data.ndaEmployeeDetails.tmlManager.email,
                  validTill: data.data.ndaEmployeeDetails.validTill,
                  vendorManagerEmail:
                    data.data.ndaEmployeeDetails.vendorManagerEmail,
                  vendorManagerName:
                    data.data.ndaEmployeeDetails.vendorManagerName,
                  managerApprovalDate: data.data.managerApprovalDate,
                  city: data.data.city,
                  department: data.data.department,
                  yearOfExperience:
                    data.data.ndaEmployeeDetails.yearsOfExperience,
                  skill: this.getSkillDropdownData(
                    data.data.ndaEmployeeDetails.skills
                  ),
                  tmlOffice: data.data.ndaEmployeeDetails.tmlOffice.officeName,
                  swipeLocation:data.data.ndaEmployeeDetails?.swipeLocation?.locationName || 'NA',
                  buCompanyCode:data.data.ndaEmployeeDetails?.buDetails?.compName,
                  tmlRegion: data.data.ndaEmployeeDetails.tmlRegion.regionName,
                  requestFinalStatus: data.data.requestFinalState.id,
                  requestFinalType: data.data.requestFinalState.requestStatus,
                  remark: data.data.remark,
                  requestType: data.data.requestType,
                  isEmailCheck: data.data.ndaEmployeeDetails.isEmail,
                  isInternetCheck: data.data.ndaEmployeeDetails.isInternet,
                  edata: data.data.ndaEmployeeDetails.edata,
                  employeeType:data.data.ndaEmployeeDetails.employeeType,
                  ssiCode:data.data.ndaEmployeeDetails.edata?.sioCode,
                  edataDepartment:data.data.ndaEmployeeDetails.edata?.department,
                  l2ManagerEmail:data.data.l2Manager.email,
                  l2ManagerName:data.data.l2Manager.name
                },
                () => {
                  console.log("response changelogs",this.state.response.changeLogs)
                  if (this.state.towerShownForManager) this.getTower();
                  if (this.state.response.changeLogs)
                    this.changeLogList(JSON.parse(JSON.stringify(this.state.response.changeLogs)));
                }
              );
            } else {
              this.setState({
                isLoading: false,
              });
              errorToast(data.message);
            }
          }
        })
        .catch((error) => {
          console.log("handleSubmit() in request", error);
        }).finally(() => {
          this.setState({isLoading: false})
        });
    }
  };

  openModal = () => this.setState({ isOpen: true });

  closeModal = () => this.setState({ isOpen: false });

  detailViewTab = () => {
    this.setState({
      changeLogView: false,
      detailsView: true,
    });
  };

  changeLogTab = () => {
    this.setState({
      changeLogView: true,
      detailsView: false,
    });
  };

  maskEmail = (email) => {
    if(!email) return null;
    if (!email || !email.includes("@")) return "****@****.***";
  
    const [name, domain] = email.split("@");
    const maskedName = name[0] + "*".repeat(Math.max(0, name.length - 2)) + name.slice(-1);
    
    return maskedName + "@" + domain;
  };

  maskMobileNumber = (number) => {
    if(!number) return null;
    if (number.length < 4) return "****"; 
    return number.replace(/\d(?=\d{4})/g, "*");
  };

  onMasking = () =>{
    this.setState(prevState=>({...prevState,showMaskedContent:!prevState.showMaskedContent}),()=>{
      if (this.state.response.changeLogs)
        this.changeLogList(JSON.parse(JSON.stringify(this.state.response.changeLogs)));
    })
  }


  render() {
    const id = this.props.requestId;
    const baseUrl = this.props.baseUrl;
    return (
      <>
        <div>
          <div className="row">
            <div className="col-md-12">
              <SetpUl baseUrl={baseUrl} Id={id} />
            </div>
          </div>

          <div className="row">
            <div className="col-12">
              <div className="card card-primary card-outline">
                <div className="detail-form-header">
                  <div>
                    <h2 className="card-title"> Request Details </h2>
                    <span
                      className={
                        this.state.requestFinalStatus === 1 ||
                        this.state.requestFinalStatus === 2
                          ? "is-request-active"
                          : "is-request-disable"
                      }
                    >
                      {this.state.requestFinalType}
                    </span>
                  </div>
                  <div>
                    <h2 className="request-id">
                      {" "}
                      Request ID : {this.state.requestID}{" "}
                    </h2>
                  </div>
                </div>
                <div className="card-body w-100">
                  <div className="d-flex justify-content-between">
                    <div className="change-log d-flex">
                      <div
                        onClick={() => {
                          this.detailViewTab();
                        }}
                        className={this.state.detailsView ? "tab-active" : null}
                      >
                        Details View
                      </div>
                      {changeLogList.includes(this.state.requestType) ? (
                        <div
                          onClick={() => {
                            this.changeLogTab();
                          }}
                          className={
                            this.state.changeLogView ? "tab-active" : null
                          }
                        >
                          Change Logs
                        </div>
                      ) : null}
                      
                       
                       
                    </div>
                    <div className="validate" onClick={this.onMasking}
                     >
                        <Tooltip title={this.state.showMaskedContent ? 'Hide':'Show'} color="#FFF" overlayInnerStyle={{color:"#000000"}}>
                                <div className="clear-button">{this.state.showMaskedContent ? <FaEyeSlash className="clear-icon"/>:<FaEye className="clear-icon"/>}</div> 
                              </Tooltip> 
                        </div>
                    {/* <hr className="seperator" /> */}
                  </div>
                  {!this.state.changeLogView ? (
                    // ----------------------------- personal Details ------------------------------ */}
                    <div className="employee-update-details">
                      <div className="update-header">
                        <div className="update-section">Personal Details</div>
                      </div>
                      <Row className="form-grid">
                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="formGridEmailFristName">
                            <Form.Label> Name of Person</Form.Label>
                            <InputGroup>
                              <Form.Control
                                type="text"
                                name="personName"
                                readOnly
                                value={this.state.personName}
                                onChange={this.onChangeHandler}
                                placeholder=" Enter Person Name"
                              />
                            </InputGroup>
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="Mobile">
                            <Form.Label> Mobile Number</Form.Label>
                              <Form.Control
                                type="text"
                                name="personMobile"
                                readOnly
                                value={this.state.showMaskedContent ? this.state.personMobile : this.maskMobileNumber(this.state.personMobile)}
                                onChange={this.onChangeHandler}
                                placeholder="Enter Mobile"
                              /> 
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                            <Form.Group controlId="Email">
                              <Form.Label>Email ID</Form.Label>
                                <Form.Control
                                  type="text"
                                  name="personEmail"
                                  readOnly
                                  value={this.state.showMaskedContent ? this.state.personEmail : this.maskEmail(this.state.personEmail)}
                                  onChange={this.onChangeHandler}
                                  placeholder="Enter Email ID"
                                />
                            </Form.Group>
                          </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="gender">
                            <Form.Label> Gender</Form.Label>
                            <InputGroup>
                              <Form.Control
                                type="text"
                                name="gender"
                                readOnly
                                value={this.state.gender}
                                onChange={this.onChangeHandler}
                                placeholder=" Enter Gender"
                              />
                            </InputGroup>
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="employeeId">
                            <Form.Label>Employee ID</Form.Label>
                            <Form.Control
                              type="text"
                              name="employeeId"
                              readOnly
                              value={this.state.employeeId}
                              onChange={this.onChangeHandler}
                              placeholder=" Enter Employee ID"
                            />
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="designation">
                            <Form.Label>Designation</Form.Label>
                            <Select
                              instanceId={"designation"}
                              options={this.state.designationList}
                              value={this.state.designation}
                              onChange={(e) => {
                                this.handleDropdownData(e, "designation");
                              }}
                              isSearchable
                              isFocused={false}
                              name="city"
                              placeholder="Select Designation"
                              isDisabled={true}
                              isOptionDisabled={(option) => option.label == "a"}
                              className="myclass"
                              noOptionsMessage={({ inputValue }) =>
                                "No results found"
                              }
                              // required
                            />
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="yearOfExperience">
                            <Form.Label>Years of Experience</Form.Label>
                            <Form.Control
                              type="text"
                              name=""
                              readOnly
                              value={this.state.yearOfExperience}
                              onChange={this.onChangeHandler}
                              placeholder=" Enter Year Of Experience"
                            />
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="skill">
                            <Form.Label>Skill</Form.Label>
                            <Select
                              options={this.state.skillData}
                              value={this.state.skillsArray}
                              // value = {this.getUniqueList()}
                              onChange={(e) => {
                                this.handleDropdownData(e, "skill");
                              }}
                              isSearchable
                              name="skill"
                              placeholder="Select Skill"
                              className="myclass"
                              noOptionsMessage={({ inputValue }) =>
                                "No results found"
                              }
                              isDisabled={true}
                              isMulti
                            />
                          </Form.Group>
                        </Col>

                        <Col md="3">
                          <Form.Group controlId="yearOfExperience">
                            <Form.Label>EmployeeType</Form.Label>
                            <Form.Control
                              type="text"
                              name=""
                              readOnly
                              value={this.state.employeeType}
                              onChange={this.onChangeHandler}
                              placeholder=" Enter Employee Type"
                            />
                          </Form.Group>
                          </Col>
                        <Col md="3">
                          <Form.Group controlId="department">
                            <Form.Label>Department</Form.Label>
                            <Form.Control
                              type="text"
                              name=""
                              readOnly
                              value={this.state.edataDepartment}
                              onChange={this.onChangeHandler}
                              placeholder=" Enter Department"
                            />
                            {/* <Select
                              options={this.state.departmentTempData.length > 0 ? this.state.departmentTempData : this.state.departmentData}
                              value={this.state.edataDepartment}
                              onChange={(e) => { this.handleDropdownData(e, "edataDepartment") }}
                              isSearchable
                              name="department"
                              placeholder="Select Department"
                              className="myclass"
                              noOptionsMessage={({ inputValue }) =>
                                "No results found"
                              }
                              isDisabled={this.state.isDepartmentDisable}
                            // isMulti
                            // components={animatedComponents}
                            // required
                            /> */}
                          </Form.Group>
                        </Col>
                      </Row>

                      {/* ----------------------------------------- Vendor Details --------------------------------------  */}
                      <div className="update-header">
                        <div className="update-section">Vendor Details</div>
                      </div>
                      <Row className="form-grid">
                        <Col md="3">
                          <Form.Group controlId="Vendor">
                            <Form.Label>Vendor / Company Name</Form.Label>
                            <Select
                              instanceId={"Vendor"}
                              options={this.state.vendorOption}
                              value={this.state.vendor}
                              onChange={(e) => {
                                this.handleDropdownData(e, "vendor");
                              }}
                              isSearchable
                              isFocused={false}
                              name="vendor"
                              placeholder="Select Vendor Name"
                              className="myclass"
                              noOptionsMessage={({ inputValue }) =>
                                "No results found"
                              }
                              isDisabled={true}
                              // required
                            />
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="vendorManagerName">
                            <Form.Label> Vendor Manager&apos;s Name</Form.Label>
                            <Form.Control
                              type="text"
                              name=""
                              readOnly
                              value={this.state.vendorManagerName}
                              onChange={this.onChangeHandler}
                              placeholder=" Enter Vendor Manager Name"
                            />
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="vendorManagerEmail">
                            <Form.Label>Vendor Manager&apos;s Email</Form.Label>
                            <Form.Control
                              type="text"
                              name=""
                              readOnly
                              value={this.state.showMaskedContent ? this.state.vendorManagerEmail : this.maskEmail(this.state.vendorManagerEmail)}
                              onChange={this.onChangeHandler}
                              placeholder="Enter Vendor Manager Email"
                              />
                            </Form.Group>
                        </Col>

                        <Col md="3">
                          <Form.Group controlId="projectName">
                            <Form.Label>Project Name</Form.Label>
                            <Select
                              options={this.state.project}
                              value={this.state.projectName}
                              onChange={(e) => {
                                this.handleDropdownData(e, "projectName");
                              }}
                              isSearchable
                              name="projectName"
                              placeholder="Select Project Name"
                              className="myclass"
                              noOptionsMessage={({ inputValue }) =>
                                "No results found"
                              }
                              isDisabled={true}
                            />
                          </Form.Group>
                        </Col>

                        {this.state.towerShownForManager ? (
                          <Col md="3" className="detail-view-flex">
                            <Form.Group controlId="towerName">
                              <Form.Label>Tower Name</Form.Label>
                              <Select
                                options={this.state.towerData}
                                value={this.state.towerName}
                                onChange={(e) => {
                                  this.handleDropdownData(e, "towerName");
                                }}
                                isSearchable
                                name="towerName"
                                placeholder="Select Tower Name"
                                className="myclass"
                                noOptionsMessage={({ inputValue }) =>
                                  "No results found"
                                }
                                isDisabled={true}
                              />
                            </Form.Group>
                          </Col>
                        ) : null}
                      </Row>

                      {/* ------------------------------------------ Tml manager Details -------------------------------------- */}
                      <div className="update-header">
                        <div className="update-section">
                          Reporting Manager Details
                        </div>
                      </div>
                      <Row className="form-grid">
                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="Manager">
                            <Form.Label>
                              Reporting Manager&apos;s Email
                            </Form.Label>
                            <InputGroup className="mb-3">
                              <FormControl
                                type="text"
                                name="manager"
                                placeholder="Enter TML Manager Email"
                                aria-label="Recipient's username"
                                aria-describedby="basic-addon2"
                                required
                                value={this.state.tmlManagerEmail}
                                disabled={true}
                              />
                            </InputGroup>
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="tmlLocation">
                            <Form.Label>TML Location</Form.Label>
                            <Form.Control
                              type="text"
                              name=""
                              readOnly
                              value={this.state.tmlOffice}
                              onChange={this.onChangeHandler}
                              placeholder=" Enter TML Location"
                            />
                          </Form.Group>
                        </Col>

                        <Col md="6">
                          <Form.Group controlId="Contact">
                            <Form.Label> BU Company Code </Form.Label>
                            <FormControl
                              type="text"
                              name="buCompanyCode"
                              aria-label="BU Company Code"
                              aria-describedby="basic-addon2"
                              onChange={this.onChangeHandler}
                              required
                              value={this.state.buCompanyCode}
                              readOnly
                            />
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="swipeLocation">
                            <Form.Label>Swipe Location</Form.Label>
                            <Form.Control
                              type="text"
                              name=""
                              readOnly
                              value={this.state.swipeLocation}
                              onChange={this.onChangeHandler}
                              placeholder=" Enter Swipe Location"
                            />
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="tmlRegion  ">
                            <Form.Label>TML Region</Form.Label>
                            <Form.Control
                              type="text"
                              name=""
                              readOnly
                              value={this.state.tmlRegion}
                              onChange={this.onChangeHandler}
                              placeholder=" Enter TML Region"
                            />
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="ssiCode">
                            <Form.Label>SIO Code / BC Field</Form.Label>
                            <Form.Control
                              type="text"
                              name=""
                              readOnly
                              value={this.state.ssiCode}
                              onChange={this.onChangeHandler}
                              placeholder=" Enter SIO Code / BC Field"
                            />
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="ssiCode">
                            <Form.Label>L2 Manager Email</Form.Label>
                            <Form.Control
                              type="text"
                              name=""
                              readOnly
                              value={this.state.l2ManagerEmail}
                              onChange={this.onChangeHandler}
                              placeholder=" Enter SIO Code / BC Field"
                            />
                          </Form.Group>
                        </Col>

                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="ssiCode">
                            <Form.Label>L2 Manager Name</Form.Label>
                            <Form.Control
                              type="text"
                              name=""
                              readOnly
                              value={this.state.l2ManagerName}
                              onChange={this.onChangeHandler}
                              placeholder=" Enter SIO Code / BC Field"
                            />
                          </Form.Group>
                        </Col>
 

                      </Row>

                      {/* ------------------------------------------ Account Details -------------------------------------- */}
                      <div className="update-header">
                        <div className="update-section">Domain ID Details</div>
                      </div>

                      <Row>
                        <Col md="3" className="detail-view-flex">
                          <Form.Group controlId="validTill">
                            <Form.Label> Valid Till</Form.Label>
                            <Form.Control
                              type="text"
                              name=""
                              readOnly
                              value={this.state.validTill}
                              onChange={this.onChangeHandler}
                              placeholder=" Enter Valid Till"
                            />
                          </Form.Group>
                        </Col>

                        {this.state.domainIdShown ? (
                          <Col md="3" className="detail-view-flex">
                            <Form.Group controlId="domainID">
                              <Form.Label>Domain ID</Form.Label>
                              <Form.Control
                                type="text"
                                name=""
                                readOnly
                                value={
                                  this.state.response.ndaEmployeeDetails
                                    .domainId
                                }
                                onChange={this.onChangeHandler}
                                placeholder=" Enter Domain ID"
                              />
                            </Form.Group>
                          </Col>
                        ) : null}

                        <Col md="6">
                          <Form.Group controlId="accountType">
                            <Form.Label>Account Type</Form.Label>
                            {["checkbox"].map((type) => (
                              <div key={`inline-${type}`} className="">
                                <Form.Check
                                  type={type}
                                  label="Domain ID"
                                  id="Domain"
                                  name="accountType"
                                  defaultChecked={this.state.isDomainCheck}
                                  inline
                                  value="Domain"
                                  onChange={this.handleCheck}
                                  disabled={true}
                                />
                                <Form.Check
                                  inline
                                  type={type}
                                  label="Email Account"
                                  name="accountType"
                                  id="emailAccount"
                                  value="Email"
                                  disabled={true}
                                  checked={this.state.isEmailCheck}
                                />
                                <Form.Check
                                  inline
                                  type={type}
                                  label="Internet"
                                  id="Internet"
                                  name="accountType"
                                  value="Internet"
                                  disabled={true}
                                  checked={this.state.isInternetCheck}
                                />
                              </div>
                            ))}
                          </Form.Group>
                        </Col>
                      </Row>
                      {/* ------------------------------------------ Remark Details -------------------------------------- */}
                      {this.state.requestFinalStatus === 3 ||
                      this.state.requestFinalStatus === 4 ? (
                        <>
                          <div className="update-header">
                            <div className="update-section">Remark</div>
                          </div>
                          <Row>
                            <Col md="3">
                              <Form.Group controlId="remark">
                                <Form.Label>Remark</Form.Label>
                                <Form.Control
                                  as="textarea"
                                  rows={3}
                                  value={this.state.remark}
                                  disabled={true}
                                  readOnly
                                  style={{ resize: "none" }}
                                />
                              </Form.Group>
                            </Col>
                          </Row>
                        </>
                      ) : null}
                    </div>
                  ) : (
                    <div className="employee-update-details scroll-behaviour">
                      {this.state.changeLogsArray.length !== 0
                        ? this.state.changeLogsArray.map((object, index) => {
                            return (
                              <div
                                className="card-container-row"
                                key={`changelog${index}`}
                              >
                                <div className="box-card">
                                  <div className="card-heading-text">
                                    Field Name
                                  </div>
                                  <div className="card-body-text">
                                    {object.Field_name}
                                  </div>
                                </div>
                                <div className="box-card">
                                  <div className="card-heading-text">
                                    Old Data
                                  </div>
                                  <div className="card-body-text">
                                    {object.old === true
                                      ? "Yes"
                                      : object.old === false
                                      ? "No"
                                      : object.old}
                                  </div>
                                </div>
                                <div className="box-card">
                                  <div className="card-heading-text">
                                    New Data
                                  </div>
                                  <div className="card-body-text">
                                    {object.new === true
                                      ? "Yes"
                                      : object.new === false
                                      ? "No"
                                      : object.new}
                                  </div>
                                </div>
                              </div>
                            );
                          })
                        : null}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {this.state.checked2 && (
            <Row>
              <Col md="12"></Col>
            </Row>
          )}

          <Modal
            border="primary"
            show={this.state.isOpen}
            onHide={this.closeModal}
            backdrop="static"
            keyboard={false}
            size="lg"
          >
            {/* <Modal.Header closeButton> */}
            <Form onSubmit={this.handleSubmit}>
              <Modal.Header>
                <Modal.Title className="">NDA Form</Modal.Title>
              </Modal.Header>
              <Modal.Body style={{ height: "400px", overflowY: "scroll" }}>
                <div className="">
                  <Row>
                    <Col lg="12">
                      <div className="text-center">
                        <table className="table1">
                          <tbody>
                            <tr>
                              <td rowSpan="3" className="td1">
                                <p className="p1">
                                  <img
                                    width="59"
                                    src="data:image/jpeg;base34,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"
                                    alt="image"
                                  ></img>
                                  <span className="span1"> &nbsp; </span>&nbsp;
                                </p>
                              </td>
                              <td colSpan="2" className="td2">
                                <p className="p2">
                                  <span className="span2">
                                    TATA MOTORS INFORMATION SECURITY &nbsp;
                                    &nbsp;&nbsp;
                                  </span>
                                  &nbsp;
                                </p>
                              </td>

                              <td rowSpan="3" className="td3">
                                <p className="p3">
                                  <span className="span3">
                                    Document Ref&nbsp;
                                  </span>
                                  <span className="span33">ACC-D002</span>
                                </p>
                              </td>
                            </tr>

                            <tr>
                              <td className="td4">
                                <p className="p4">
                                  <span className="span4">Document&nbsp;</span>
                                  &nbsp;
                                </p>
                              </td>
                              <td className="td5">
                                <p className="p5">
                                  <span className="span5">
                                    Third Party Access - NDA&nbsp;
                                  </span>
                                  &nbsp;
                                </p>
                              </td>
                            </tr>
                            <tr>
                              <td className="td6">
                                <p className="p6">
                                  <span className="span6">&nbsp;</span>&nbsp;
                                </p>
                              </td>
                              <td className="td7">
                                <p className="p7">
                                  <span className="span7">&nbsp;</span>&nbsp;
                                </p>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </Col>
                  </Row>
                  <Row>
                    <Col md="12">
                      <div className="text-center" style={{ marginTop: "5px" }}>
                        {" "}
                        <h5>THIRD PARTY NON DISCLOSURE AGREEMENT </h5>
                      </div>
                      <p>
                        {/* THIS AGREEMENT is entered into this <b><u>Saturday day of 3, 2022,</u></b> between */}
                        THIS AGREEMENT is entered into this{" "}
                        <b>
                          <u>{DateTime.now().toFormat("MMMM dd, yyyy")}</u>
                        </b>{" "}
                        between Tata Motors Ltd., hereinafter &quot;TATA
                        MOTORS&quot;, and Mr./Ms.{" "}
                        <b>
                          <u>{this.state.personName}</u>
                        </b>{" "}
                        representing
                        <b>
                          <u> {this.state.vendor}</u>
                        </b>{" "}
                        &nbsp;(the Vendor / Company), hereinafter
                        &quot;Recipient&quot;. The persons executing this
                        Agreement hereby warrant that they are authorized to do
                        so for and on behalf of the above-named companies.
                      </p>

                      <h5>RECITALS: </h5>

                      <ol type="A">
                        <li>
                          TATA MOTORS is engaged in research, manufacture and
                          marketing of commercial vehicles and passenger cars
                          and{" "}
                        </li>
                        <li>
                          TATA MOTORS and the Vendor / Company have entered into
                          an agreement to carry out business transactions ; and{" "}
                        </li>
                        <li>
                          In connection with such agreement, TATA MOTORS will
                          disclose to Recipient certain information concerning
                          its business and technologies, a portion of which
                          information is regarded by TATA MOTORS as confidential
                          or proprietary, and the parties desire to provide for
                          a means of determining which information is
                          confidential or proprietary information and for the
                          respective right and duties of the parties with
                          respect thereto;{" "}
                        </li>
                        <li style={{ listStyle: "none", marginTop: "5px" }}>
                          NOW THEREFORE, the parties hereto agree as follows:{" "}
                        </li>
                      </ol>
                      <ol type="1">
                        <li>
                          {" "}
                          Definitions. As used herein: <br></br>
                          <ol type="a">
                            <li>
                              {" "}
                              The term &quot;Information&quot; shall mean all
                              information relating to TATA MOTORS business,
                              technologies or prospective business which is
                              furnished to the recipient by TATA MOTORS.{" "}
                            </li>
                            <li>
                              The term &quot;Confidential Information&quot;
                              shall mean all information which TATA MOTORS
                              protects against unrestricted disclosure to others
                              and which: if in written or other tangible form,
                              is clearly designated and labeled as
                              &quot;Confidential&quot;; and if disclosed orally,
                              is reduced to a writing designating such
                              information as &quot;Confidential&quot; which is
                              delivered to the Recipient promptly following such
                              oral disclosure. By way of illustration, but not
                              limitation, such Confidential Information may
                              include inventions, concepts, designs, formulas,
                              techniques, processes, software, or market data.
                            </li>
                          </ol>
                        </li>
                        <li>
                          {" "}
                          Protection of Confidential Information. The Recipient
                          agrees, with respect to any Confidential Information
                          received by him or her and their represented
                          companies: <br></br>
                          <ol type="a">
                            <li>
                              To use such Confidential Information only for the
                              purpose previously stated (i.e., for the purpose
                              of effecting the proposed transaction between the
                              parties).{" "}
                            </li>
                            <li>
                              To use the same methods and degree of care to
                              prevent disclosure of such Confidential
                              Information as it uses to prevent disclosure of
                              its own proprietary and Confidential Information;
                              and to return such Confidential Information
                              received in any tangible form to TATA MOTORS at
                              the request of TATA MOTORS and to retain no copies
                              or reproductions thereof.
                            </li>
                          </ol>
                        </li>
                        <li>
                          <p>
                            Limitations. The Recipient shall not be obligated to
                            treat information as Confidential Information if
                            such information:{" "}
                          </p>
                          <br></br>
                          <ol type="a">
                            <li>
                              <p>
                                {" "}
                                was rightfully in Recipient&apos;s possession or
                                was rightfully known to Recipient prior to
                                receipt from TATA MOTORS, or is or becomes
                                public knowledge without the fault of Recipient;
                                or{" "}
                              </p>
                              <br></br>
                              <p>
                                {" "}
                                is or becomes rightfully available to Recipient
                                without confidential restriction from a source
                                not under TATA MOTORS control; or{" "}
                              </p>
                              <br></br>
                              <p>
                                {" "}
                                is independently developed by Recipient without
                                use of the Confidential Information disclosed
                                hereunder; provided, however, that the burden of
                                proof of such independent development shall be
                                upon Recipient; or{" "}
                              </p>
                              <br></br>
                              <p>
                                {" "}
                                is disclosed pursuant to court or government
                                action provided, however, that recipient gives
                                TATA MOTORS reasonable prior notice of
                                disclosure pursuant to such court or government
                                action.{" "}
                              </p>
                            </li>
                            <li style={{ listStyle: "none" }}>
                              <p>
                                Each party agrees that its obligations hereunder
                                are essential in order to protect the other, and
                                its business, and expressly acknowledges and
                                agrees that monetary damages would be inadequate
                                compensation for any breach of any covenant or
                                agreement set forth herein. Accordingly, each
                                party agrees and acknowledges that any such
                                violation or threatened violation will cause
                                irreparable injury to the other and that, in
                                addition to and without waiving any other
                                remedies (Including damages) that may be
                                available, in law, in equity or otherwise, the
                                aggrieved party shall be entitled to obtain
                                temporary, preliminary and/or permanent
                                injunctive relief against the threatened breach
                                of this Agreement or the continuation of any
                                such breach, without the necessity of proving
                                damage.{" "}
                              </p>
                            </li>
                          </ol>
                        </li>
                        <li style={{ listStyle: "none" }}>
                          This agreement is acknowledged and signed by an
                          authorized representative of the respective companies:
                        </li>
                        <ul style={{ listStyle: "none" }}>
                          <li>
                            <Row>
                              <Col md="6">
                                <ul
                                  style={{ listStyle: "none", margin: "5px" }}
                                >
                                  {" "}
                                  <b>Tata Motors</b>
                                  {/* <li>
                                                                <p>Signature:_____________</p>
                                                            </li> */}
                                  <li>
                                    Printed Name:
                                    <b>
                                      <u>{this.state.personName}</u>
                                    </b>
                                  </li>
                                  <li>Title: NDA</li>
                                  <li>
                                    Date:
                                    <b>
                                      <u>
                                        {DateTime.now().toFormat("MM-dd-yyyy")}
                                      </u>
                                    </b>
                                  </li>
                                </ul>
                              </Col>
                              <Col md="6">
                                <ul
                                  style={{ listStyle: "none", margin: "5px" }}
                                >
                                  <b>Recipient</b>
                                  <li>
                                    Printed Name:
                                    <b>
                                      <u>{this.state.manager}</u>
                                    </b>
                                  </li>
                                  <li>Title: NDA</li>
                                  <li>
                                    Date:
                                    <b>
                                      <u>
                                        {DateTime.now().toFormat("MM-dd-yyyy")}
                                      </u>
                                    </b>
                                  </li>
                                </ul>
                              </Col>
                            </Row>
                          </li>
                        </ul>
                      </ol>
                    </Col>
                  </Row>
                </div>
              </Modal.Body>
              <Modal.Footer style={{ display: "block" }}>
                <Row>
                  {" "}
                  <Col md="4">
                    <Form.Group>
                      {/* <Form.Control as="textarea" onChange={this.onChangeHandler} name="acceptance" placeholder="NDA Acceptance Description" /> */}
                      <Form.Check
                        size="md"
                        type="checkbox"
                        label="I have accpted NDA"
                        id="nda"
                        defaultChecked={this.state.checked}
                        onChange={this.handleSelectcheck}
                        value={this.state.checkedValue}
                        name="acceptance"
                        inline
                        title={this.state.titleCheckBtn}
                      />
                    </Form.Group>
                  </Col>
                  <Col md="6">
                    <Form.Group className="text-center" controlId="Submitbtn">
                      <Button
                        disabled={!this.state.checked}
                        style={{ marginRight: "10px" }}
                        variant="info"
                        size="sm"
                        type="submit"
                      >
                        Submit
                      </Button>
                    </Form.Group>
                  </Col>
                </Row>
              </Modal.Footer>
            </Form>
          </Modal>
        </div>

        {this.state.isLoading ? (
          <div className="loader">
            <div></div>
          </div>
        ) : null}
      </>
    );
  }
}
const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
  vendor: state.loginInfo.login.vendorName,
  isTmlEmployee: state.loginInfo.login.isTmlEmployee,
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(RequestAproval);
