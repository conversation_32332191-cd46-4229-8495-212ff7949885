//  Api endpoint
export const getLogin                   = 'login'
export const getToken                   = 'api/token/'
export const getTokenFromRefreshToken   = 'api/token/refresh/'
export const createRequest              = 'api/nda/create-nda-request/'
export const getAllRequest              = 'api/nda/nda-requests/'
export const getInviteDetails           = 'api/nda/get-nda-requests-list'
export const cancelRequest              = 'api/nda/cancel-nda-request/'
export const getAssignedRequest         = 'api/nda/get-nda-requests-list'
export const getFullfilledRequest       = 'api/nda/get-nda-requests-list'
export const getCancelledRequest        = 'api/nda/get-nda-requests-list'
export const getRejectedRequest         = 'api/nda/get-nda-requests-list'
export const getIdDetails               = 'api/nda/get-nda-request-details'
export const validateUrl                = 'api/nda/validate-url/'
export const validateOtpUrl             = 'api/nda/nda-login/'
export const getDetailsByRequestId      = 'api/nda/get-nda-request-details'
export const ndaAccept                  = 'api/nda/user-nda-accept-reject/'
export const managerNdaAccept           = 'api/nda/manager-nda-accept-reject/'
export const managerAllrequest          = 'api/nda/get-approval-list/'
export const getManagerDashboardCount   = 'api/nda/manager-dashboard-count'
export const getSpocDashboardCount      = 'api/nda/dashboard-count'
export const getVendorList              = 'api/nda/vendors/'
export const getDesignationList         = 'api/nda/designations/'
export const getProjectList             = 'api/nda/projects/'
export const getSkillList               = 'api/nda/skills/'
export const getTowerList               = 'api/nda/towers/'
export const getTmlOffice               = 'api/nda/tml-offices/'
export const getTmlRegion               = 'api/nda/tml-regions/'
export const getDepartmentList          = 'api/nda/department/'
export const getManagerDetails          = 'api/nda/get-sap-details'
export const getManagerDetails_v2       = 'api/nda/get-sap-details-v2'
export const getVendorEmployeeDeatils   = 'api/nda/search-employee-details'
export const sendInvite                 = 'api/nda/resend-invite/'
export const logout                     = 'api/logout/'
export const updateRequest              = 'api/nda/create-nda-update-request/'
export const managerAcceptUpdate        = 'api/nda/nda-update-manager-accept-reject/'
export const managerRejectUpdate        = 'api/nda/nda-update-manager-accept-reject/'
export const triggerUpdateDomainID      = 'api/nda/retry-update-domain-id'
export const triggerCreateDomainID      = 'api/nda/retry-create-domain-id'
export const disableRequest             = 'api/nda/create-nda-disable-request/'
export const getDisableRequest          = 'api/nda/get-nda-requests-list'
export const validateDomainID           = 'api/sync/validate-domain-id/'
export const validateManagerDetails     = 'api/sync/validate-manager-details/'
export const syncEmployeeDetails        = 'api/sync/sync-nda-employee/'
export const deleteRequest              = 'api/nda/create-nda-delete-request/'
export const downloadNDA                = 'api/nda/generate-nda-form/'
export const getNDAEmployeesByDomainIds = 'api/nda/get-employee-details-by-domain-ids'
export const bulkExtensionUpdate        = "api/nda/bulk-extension-update"
export const getSwipeLocationList       = 'api/nda/swipe-location'
export const getBuCompanyCodeList       = 'api/nda/bu-details-list'

//Admin API
export const adminMasterList                             = 'api/admin/master-list'
export const userList                                    = 'api/admin/user-list'
export const offBoardUser                                = 'api/admin/offboard-user'
export const addUpdateVendor                             = 'api/admin/add-or-update-vendor-master'
export const addUpdateDesignation                        = 'api/admin/add-or-update-designations-master'
export const addUpdateSkills                             = 'api/admin/add-or-update-skill-master'
export const addUpdateTower                              = 'api/admin/add-or-update-tower-master'
export const addUpdateProject                            = 'api/admin/add-or-update-project-master'
export const addUpdatetmlOffice                          = 'api/admin/add-or-update-tmlOffice-master'
export const addUpdatetmlRegion                          = 'api/admin/add-or-update-tmlRegion-master'
export const addUpdatedepartment                         = 'api/admin/add-or-update-department-master'
export const onBoardUser                                 = 'api/admin/onboard-user'
export const validateUser                                = 'api/admin/validate-user'
export const adminSpocEmpDetails                         = 'api/admin/get-spoc-emp-details'
export const getAdminDashboardCount                      = 'api/admin/dashboard-count'
export const getAdminRequestDetails                      = 'api/admin/get-nda-requests-list'
export const adminCancelRequest                          = 'api/admin/cancel-nda-request/'
export const adminSendInvite                             = 'api/admin/resend-invite/'
export const triggerAdminUpdateDomainID                  = 'api/admin/retry-update-domain-id'
export const triggerAdminDomainID                        = 'api/admin/retry-create-domain-id'
export const getAdminApprovalDashboardCount              = 'api/admin/manager-dashboard-count'
export const adminApprovalrequest                        = 'api/admin/get-approval-list/'
export const adminNdaAccept                              = 'api/admin/manager-nda-accept-reject/'
export const adminAcceptRejectUpdate                     = 'api/admin/nda-update-manager-accept-reject/'
export const adminBulkEmployeeValidityAndStatusExtension = 'api/admin/bulk-employee-validity-and-status-extension/'
export const adminBulkEmployeeValidityAndStatusEnable    = 'api/admin/bulk-employee-validity-and-status-enable/'
export const adminBulkEmployeeValidityAndStatusUpdate    = 'api/admin/bulk-employee-update/'


// SPOC Report API
export const spocEmpDetails             = 'api/nda/get-spoc-emp-details'
export const sendSpocEmpDetails         = 'api/nda/send-spoc-emp-details'
export const doaminIDExtensionEmpDetails= 'api/nda/get-nda-employee-domain-id-extension'
export const extendDomainIDs            = 'api/nda/bulk-domain-validity-extension'

// Bulk Extension
export const managerBulkExtension       = 'api/nda/manager-bulk-extension-pending-request-approval'

// Project Check
export const checkProjectDetails        = 'api/nda/check-porject-details'

// Central Auth API
export const getTokenV2                 = 'api/nda/get-token'
export const getRefreshTokenV2          = 'api/nda/get-refresh-token'
export const userLogout                 = 'api/nda/logout'
export const validateOtp                = 'api/nda/validate-otp'
export const searchPO                   = 'api/nda/po-details'
export const submitMaster               = 'api/nda/update-po-details'
export const PoMasterList               = 'api/nda/po-list-details'
