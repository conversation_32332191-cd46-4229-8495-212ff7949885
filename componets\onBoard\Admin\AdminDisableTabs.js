import React, { Component } from "react";
import {Tabs,Tab,ListGroup,Badge,Table,Alert,FormControl,InputGroup,fieldset,Modal,Col,Card,Container,Row,Button,Form,} from "react-bootstrap";
import { connect } from "react-redux";
import { setId} from "../../../redux/actions/counterActions";
import AllRequest from "../AllRequest";

class DisableTabs extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      activeKey:"allRequest"
    };
  }

  handleShow = (e) => {
    this.setState({
      activeKey: e,
    });

  };

  render() {
    return (
        <div>
          <div className="table-container">
            <Row>
              <Col md="12">
                <Tabs
                  id="controlled-tab-example"
                  defaultActiveKey={"allRequest"}
                  onSelect={this.handleShow}
                  className="mb-3"
                > 
                  <Tab
                    className="tab-color"
                    eventKey="allRequest"
                    title="All Request"
                    tabClassName={this.state.activeKey == "Indents"? "tabActive" : "tabInactive"}  
                  >
                    <AllRequest baseUrl={this.props.baseUrl} loginData={this.props.loginData}/>
                  </Tab> 
                </Tabs>
              </Col>
            </Row> 
          </div>
        </div>
      );
    } 
  }

const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
  requestType: state.counter.requestType
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(DisableTabs);
