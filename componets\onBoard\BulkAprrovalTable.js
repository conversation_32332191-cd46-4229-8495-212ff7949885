import React, { Fragment } from "react";
import { Component } from "react";
import BootstrapTable from "react-bootstrap-table-next";
import paginationFactory from "react-bootstrap-table2-paginator";
import ToolkitProvider, { Search } from "react-bootstrap-table2-toolkit";
const { SearchBar } = Search;
import Router from "next/router";
import { connect } from "react-redux";
import {setId,rejectedList,managerBoxCount} from "../../redux/actions/counterActions";
import { InputGroup, Modal, Col, Row, Button, Form, } from "react-bootstrap";
import "react-notifications-component/dist/theme.css";
import { ReactNotifications, Store } from "react-notifications-component";
import { DateTime } from "luxon";
import moment from "moment";
import { getAPIResponse } from "../../constants/Utils";
import * as config from '../../constants/config'
import Select from "react-select";
import { RequestType, admin_approval_dashboard, admin_approval_dashboard_count, admin_bulk_approved_request, admin_bulk_extension, admin_download_extension_request, sizePerPage } from "../../constants/constants";
import { INTERNAL_SERVER_ERROR, NDA_FORM_REJECTED_SUCCESSFULLY, NDA_NOT_SUBMITTED, PENDING, REJECTEDREQUEST, SOMETHING_WENT_WRONG } from "../../constants/message";
import { successToast, errorToast } from "../../toast"
import { MdCancel } from "react-icons/md";
import fileDownload from 'js-file-download';

class BulkAprrovalTable extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      response: "",
      updateObj: "",
      headers: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "bulk_id", text: "Request ID", sort: true },
        // { dataField: "fullName", text: "Person Name", sort: true },
        { dataField: "managerName", text: "Reporting Manager", sort: true },
        { dataField: "createdBy", text: "Created By", sort: true },
        { dataField: "createdAt", text: "Created At", sort: true },
        { dataField: "status", text: "Request Status", sort: true },
        { dataField: "requestType", text: "Request Type", sort: true },
        {dataField: "Action",text: "Action",formatter: this.linkFollow,sort: true}
      ],
      dataArray1: [],
      requestId: "",
      isOpen: false,
      isOpen1: false,
      isSubmit: "",
      isSubmit1: "",
      note: "",
      requestId: "",
      row: "",
      personName: "",
      ndaAcceptanceDateEmployee: "",
      manager: "",
      checked: false,
      acceptance: "",
      checked: false,
      checked3: false,
      checked2: true,
      ndalfag: false,
      emailfag: true,
      checkedValue: "Reject",
      createdDate: "",
      currentPage: 0,
      currentPageData: [],
      sizePerPage: sizePerPage, 
      totalSize: 0,
      searchText: '',
      selectOptions : RequestType,
      requestType:"All",
      selectedValue:{ label: 'All', value: 'All' },
      count:"",
      bulkDownloadDisable: false
    };
    this.onChangeHandler = this.onChangeHandler.bind(this);
    this.handleSubmit.bind(this);
  }

  
  componentDidMount() {
    this.getApprovalList()
  }

  getApprovalList = () => {
    let queryParams = new URLSearchParams({})
    let api_url = `${config.managerBulkExtension}`
    console.log("this.props?.loginData?:",this.props?.loginData)

    if (localStorage.getItem("role") == 'isSuperUser'){
      let queryParams = new URLSearchParams({
        // "filter":PENDING,
        // "requesttype":requestType,
        "action": admin_bulk_approved_request,
        "module": admin_bulk_extension
      })
      api_url = `${config.managerBulkExtension}?${queryParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
      .then((response)=>{
        if(response.status === 500){
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("getApprovalList() in tmlapprovaltable",response)
          return null
        }
        else{
          return response.json()
        }  
      })
      .then((data)=>{
        if(data !== null && data.status === 1){
          this.setState({
            totalSize: data.data.count,
            currentPage: 1,
            count:data.data.count,
            currentPageData: data.data.results.map((value, index) => ({...value, srno: index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')}))
          })
        }
      })
      .catch((error)=>{
        console.log("getApprovalList() in tmlapprovaltable",error)
      })
  }

  getPaginatedPendingRequests(page, searchText) {
    // let searchParams = new URLSearchParams({
    //   "filter": PENDING,
    //   "page": page,
    //   "searchtext": searchText,
    //   "requesttype":this.state.requestType
    // }) 
    // let api_url = `${config.managerAllrequest}?${searchParams.toString()}`
    // if (localStorage.getItem("role") == 'isSuperUser'){
    //   searchParams = new URLSearchParams({
    //     "filter": PENDING,
    //     "page": page,
    //     "searchtext": searchText,
    //     "requesttype":this.state.requestType,
    //     "action": admin_approval_dashboard_count,
    //     "module": this.state.requestType +' '+ admin_approval_dashboard
    //   })
    //   api_url = `${config.adminApprovalrequest}?${searchParams.toString()}`
    // }
    let api_url = `${config.managerBulkExtension}`
    console.log("this.props?.loginData?:",this.props?.loginData)

    if (localStorage.getItem("role") == 'isSuperUser'){
      let queryParams = new URLSearchParams({
        // "filter":PENDING,
        // "requesttype":requestType,
        "action": admin_bulk_approved_request,
        "module": admin_bulk_extension
      })
      api_url = `${config.managerBulkExtension}?${queryParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response) => {
      if (response.status === 500){
        console.log("getPaginatedPendingRequests (TMLApproval.js)", response)
        return null
      } else {
        return response.json()
      }  
    }).then((response) => {
      if (response !== null && response.data === null && this.state.searchText.length > 0) {
        this.setState({
          totalSize: 0,
          currentPage: 0,
          currentPageData: []
        })
      }
      if (response !== null && response.status === 1) {
        this.setState({
          totalSize: response.data.count,
          currentPage: page,
          currentPageData: response.data.results.map((value, index) => ({...value, srno: (page - 1) * this.state.sizePerPage + index + 1 , createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')})),
        })
      }
    }).catch((error) => {
      console.log("getPaginatedPendingRequests (TMLApproval.js)", error)
    })
  }

  handleSubmit = (event) => {
    let body = {
      'id':this.state.requestId,
      "status":REJECTEDREQUEST,
    }
    getAPIResponse(config.managerNdaAccept,"POST",body)
    .then((response)=>{
      if(!response.ok){
        console.log("handleSubmit() in tmlapprovaltable",response)
        return null
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      if(data !== null){
        if(data.message === "success"){
          successToast(NDA_FORM_REJECTED_SUCCESSFULLY)
          this.closeModal();
          this.refreshpage();
        }
        else{
          console.error(data);
          errorToast(NDA_NOT_SUBMITTED)
        }
      }
      else{
        console.error(data);
        errorToast(SOMETHING_WENT_WRONG)
      }
    })
    .catch((error) => {
      console.error("handleSubmit() in tmlapprovaltable",error);
      errorToast(SOMETHING_WENT_WRONG)
    });
    //  https://stackoverflow.com/questions/43842793/basic-authentication-with-fetch
  };

  refreshpage = () => {
    this.managerCount();
    let queryParams = new URLSearchParams({
      "filter": REJECTEDREQUEST
    })
    getAPIResponse(`${config.managerAllrequest}?${queryParams.toString()}`, "GET" , {})
    .then((response)=>{
      if(!response.ok){
        console.log("refreshpage() in tmlapprovaltable",response)
        return {
          data : []
        }
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      this.setState({
        rejectArray: data.data.map((obj, index) => ({
          ...obj,
          srno:index+1,
          PersonName:`${obj.firstName} ${obj.lastName}`
        }))
      });
      this.props.rejectedList({ dataArray: data.data });
    })
    .catch((error)=>{
      console.log("refreshpage() in tmlapprovaltable",error)
    })
  };

  handleSelectcheck = (e) => {
    var titlecheck = "please click checkbox if accept NDA";
    if (e.target.checked == false) {
      titlecheck = "please click checkbox if accept NDA";
      this.setState({
        checked: e.target.checked,
        checkedValue: "Reject",
        acceptance: "",
        checked2: true,
        titleCheckBtn: titlecheck,
      });
    } else {
      titlecheck = "NDA accepted";
      this.setState({
        checked: e.target.checked,
        checkedValue: "Yes Accepted",
        acceptance: "Yes Accepted",
        checked2: false,
        titleCheckBtn: titlecheck,
      });
    }
  };

  managerCount = () => {
    let api_url = config.getManagerDashboardCount
    if (localStorage.getItem("role") == 'isSuperUser'){
      let queryParams = new URLSearchParams({
        "action": constants.admin_approval_dashboard_count,
        "module": constants.admin_approval_dashboard
      })
      api_url = `${config.getAdminApprovalDashboardCount}?${queryParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response)=>{
      if(!response.ok){
        console.log("getManagerDashboardCount() in tmlApprovalTable",response)
        return null
      }
      else {
        return response.json()
      }
    })
    .then((data)=>{
      if(data !== null){
        if(data.message === "success"){
          this.props.managerBoxCount({
            countData: data.data,
          });
        }
        else {
          console.log("managerCount() in tmlApprovalTable",data)
        }
      }
      else {
        console.log("managerCount() in tmlApprovalTable",data)
      }
    })
    .catch((error)=>{
      console.error("managerCount() in tmlApprovalTable",error)
    })
  };


  // common functions
  openModal = () => this.setState({ isOpen: true, isSubmit: "No" });

  closeModal = () => {this.setState({ isOpen: false });};

  openNdaform = () => this.setState({ isOpen1: true, isSubmit1: "No" });

  closeNdaForm = () => {this.setState({ isOpen1: false })};

  onChangeHandler = (event) => {
    event.preventDefault();
    let name = event.target.name;
    let value = event.target.value;
    this.setState({ [name]: value });
  };

  onAcceptHandle(row) {
    var dateNDA = moment(row.ndaAcceptanceDateEmployee, "YYYY-MM-DD");
    var dateCR = moment(row.createdDate, "YYYY-MM-DD");
    let ndaAcceptanceDateEmployee = dateNDA.format("DD-MM-YYYY");
    let createdDate = dateCR.format("DD-MM-YYYY");
    this.setState({
      requestId: row.id,
      row,
      ndaAcceptanceDateEmployee,
      createdDate,
      manager: row.tmlManager,
      personName: row.personName,
    });
    this.openNdaform();
  }

  onRjectHandle(row) {
    this.setState({ requestId: row.id });
    this.openModal();
  }

  linkFollow = (cell, row, rowIndex, formatExtraData) => {
    return (
      <div>
        <a onClick={() => {this.goNext(row);}} style={{ cursor: "pointer", marginRight: "2px",padding:'8px 8px' }} className="btn-info btn-xs" target="_blank"><i className="fa fa-eye"> Accept</i>
        </a>
        <a onClick={() => {this.goNext(row);}} style={{ cursor: "pointer", marginRight: "2px",padding:'8px 8px' }} className="btn-info btn-xs" target="_blank"><i className="fa fa-times"> Reject</i>
        </a>
        <a onClick={() => {this.downloadBulkRequest(row);}} style={{ cursor: "pointer", marginRight: "2px",padding:'8px 8px' }} className={this.state.bulkDownloadDisable ? "bulkDisabled btn-success btn-xs" :"btn-success btn-xs"} target="_blank"><i className="fa fa-arrow-down"></i>
        </a>
      </div>
    )
    }

  goNext(row) {
    this.props.setId({ requestId: row.id });
    Router.push({
      pathname: "/UpdateIdPage",
    });
  }

  downloadBulkRequest(row) {
    this.props.setId({ requestId: row.id });
    this.setState({bulkDownloadDisable: true})
    let searchParams = new URLSearchParams({
      "requesttype":this.state.requestType,
      "requestId": row.id
    }) 
    let api_url = `${config.managerAllrequest}?${searchParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      searchParams = new URLSearchParams({
        "requesttype":this.state.requestType,
        "requestId": row.id,
        "action": admin_download_extension_request,
        "module": this.state.requestType +' '+ admin_approval_dashboard
      })
      api_url = `${config.adminApprovalrequest}?${searchParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response) => {
      if (response.status === 500){
        console.log("downloadBulkRequest (BulkApprovaltable.js)", response)
        return null
      } else {
        return response.json()
      }  
    }).then((response) => {
      if (response !== null && response.data === null && this.state.searchText.length > 0) {
        errorToast(SOMETHING_WENT_WRONG)
      }
      if (response !== null && response.status === 1) {
        const csvContent = 'Sr No,Request ID,Person Name,Reporting Manager,Request Type,created By,created At,Status,\n';
        fileDownload(csvContent, `BulkExtensionTemplate.csv`, 'text/csv');
      }
    }).catch((error) => {
      console.log("downloadBulkRequest (BulkApprovaltable.js)", error)
    })
    this.setState({bulkDownloadDisable: false})
  }


  render() {
    return (
      <Fragment>
        <ReactNotifications />
        <ToolkitProvider
          keyField="id"
          data={this.state.currentPageData}
          columns={this.state.headers}
          search
        >
          {(props) => (
            <div>
              <div className="text-right">
                <div className="table-dropdown ">
                  <Select
                  options={this.state.selectOptions}
                  value={this.state.selectedValue}
                  onChange={(e)=>{this.setState({requestType : e.value, selectedValue:e},()=>{this.getPaginatedPendingRequests(this.state.currentPage , this.state.searchText)})}}
                  isSearchable
                  name="requestType"
                  placeholder="Select Request Type"
                  className="myclass"
                  noOptionsMessage={({ inputValue }) =>
                    "No results found"
                  }
                  // required
                  />
                </div>
                <SearchBar
                  {...props.searchProps}
                  className="custome-search-field"
                  placeholder="Search"
                />
              </div>

              <BootstrapTable
                pagination={paginationFactory({page: this.state.currentPage, sizePerPage: this.state.sizePerPage, totalSize: this.state.totalSize, hideSizePerPage: true, count:this.state.count})}
                wrapperClasses="table-responsive"
                striped
                noDataIndication={"No data to display"}
                {...props.baseProps}
                remote
                onTableChange={(type, { page, searchText }) => {
                  // console.log(type, page, searchText)
                  if (type === 'search') {
                    this.setState({searchText: searchText})
                    this.getPaginatedPendingRequests(1, searchText)
                  }
                  if (type === 'pagination') {
                    this.getPaginatedPendingRequests(page, this.state.searchText)
                  }
                }}       
              />
            </div>
          )}
        </ToolkitProvider>
        <Modal
          border="primary"
          show={this.state.isOpen}
          onHide={this.closeModal}
          backdrop="static"
          keyboard={false}
          size="md"
        >
        <Modal.Header>
          <Modal.Title>Reject Request</Modal.Title>
          <div style={{cursor:"pointer"}}onClick={this.closeRejectpopup}>
            <MdCancel />  
          </div>
        </Modal.Header>
          <Modal.Body>
            <Form onSubmit={this.handleSubmit}>
              <Row>
                <Col md="12">
                  <Form.Group controlId="RecallDescription">
                    <Form.Label>Reject Note*</Form.Label>
                    <InputGroup>
                      <Form.Control
                        required
                        as="textarea"
                        onChange={this.onChangeHandler}
                        name="note"
                        placeholder="Enter  Description"
                      />
                    </InputGroup>
                  </Form.Group>
                </Col>
              </Row>
              <Button variant="info" size="sm" type="submit">Reject</Button>
            </Form>
          </Modal.Body>
        </Modal>
      </Fragment>
    );
  }
}
const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
});

const mapDispatchToProps = {
  setId: setId,
  rejectedList: rejectedList,
  managerBoxCount: managerBoxCount,
};
export default connect(mapStateToProps, mapDispatchToProps)(BulkAprrovalTable);
