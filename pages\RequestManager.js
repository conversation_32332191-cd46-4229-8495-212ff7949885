import { Component } from "react";
import Sidebar from "../componets/Sidebar";
import Footer from "../componets/Footer";
import Header from "../componets/Header";
import RequestAproval from "../componets/onBoard/RequestAproval";
import Link from "next/link";
import { encode } from "base-64";
import Router from "next/router";
import { connect } from "react-redux";
import { setId } from "../redux/actions/counterActions";
//import { decrementCounter, incrementCounter, step1 } from '../redux/actions/counterActions';
import { Tabs, Tab, ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form, } from "react-bootstrap";
import { login, logOut } from "../redux/actions/loginActions";
import Login from './login';
import * as constants from '../constants/constants'

class RequestManager extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      logindata:""
    }
  }

  componentDidMount(){
    
  }

  render() {
    const isLoggedIn = this.props.isLogin;
      if(!isLoggedIn){
        window.location.replace(constants.FRONTEND_URL + '/login');
        return null 
      }
      else {
        return (
          <RequestAproval baseUrl={this.props.baseUrl} loginData={this.props.loginData}/>
        )
      }
  }
}

const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.login.isLogin,
  // loginData: state.loginInfo.login.isLogin
  loginData: state.loginInfo.login,
});

const mapDispatchToProps = {
  setId: setId,
  login:login
};
export default connect(mapStateToProps, mapDispatchToProps)(RequestManager);
