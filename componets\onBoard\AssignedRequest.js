import React, { Fragment } from "react";
import { Component } from "react";
import { toast } from "react-toastify";
import Router from "next/router";
import BootstrapTable from "react-bootstrap-table-next";
import paginationFactory from "react-bootstrap-table2-paginator";
import ToolkitProvider, { Search } from "react-bootstrap-table2-toolkit";
const { SearchBar } = Search;
import { Spinner, fieldset, Modal, Col, Card, Container, Row, Button, Form, } from "react-bootstrap";
import { connect } from "react-redux";
import { setId } from "../../redux/actions/counterActions";
import { getAPIResponse } from '../../constants/Utils'
import * as config from '../../constants/config'
import { ADMIN_REJECT_NOTE, ASSIGNEDREQUEST, CANCELREQUEST, INTERNAL_SERVER_ERROR, PAGINATIONS, SEARCH, SPOC_REJECT_NOTE } from "../../constants/message";
import moment from "moment";
import { admin_assign_request, admin_cancel_assign, admin_cancel_invite, sizePerPage,admin_dashboard } from "../../constants/constants";
import { successToast, errorToast } from "../../toast";
import { MdCancel } from "react-icons/md";

class AssignedRequest extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      response: "",
      updateObj: "",
      headers: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "id", text: "Request ID", sort: true },
        { dataField: "fullName", text: "Person Name", sort: true },
        { dataField: "tmlManager", text: "Reporting Manager", sort: true },
        { dataField: "createdBy", text: "Created By", sort: true },
        { dataField: "createdAt", text: "Created At", sort: true },
        { dataField: "status", text: "Request Status", sort: true },
        {dataField: "Action",text: "Action",formatter: this.linkFollow,sort: true}
      ],
      dataArray: [],
      isFollow: true,
      isOpen: false,
      isSubmit: "No",
      CompleteName: "",
      ContactNo: "",
      EmailID: "",
      CompanyCodeDesc: "",
      spinner: false,
      totalcomplete: "",
      currentPage: 0,
      currentPageData: [],
      sizePerPage: sizePerPage, 
      totalSize: 0,
      searchText: '',
      rejectPopUp:false,
      requestID:'',
      remarkNote:''
    };

    this.onFollowChanged.bind(this);
    this.mySubmitHandler.bind(this);
    this.onChangeHandler = this.onChangeHandler.bind(this);
  }
  
  componentDidMount() {
    this.getAssignedRequest()
  }

  componentDidUpdate(prevProps){
    if(prevProps.requestType !== this.props.requestType){
      this.getAssignedRequest()
    }
  }

  getAssignedRequest = () => {
    let queryParams = new URLSearchParams({
      'filter' : ASSIGNEDREQUEST,
      'requesttype' : this.props.requestType
    })
    let api_url = `${config.getAssignedRequest}?${queryParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      queryParams = new URLSearchParams({
        'filter' : ASSIGNEDREQUEST,
        'requesttype' : this.props.requestType,
        "action": admin_assign_request,
        "module": this.props.requestType +' '+ admin_dashboard
      })
      api_url = `${config.getAdminRequestDetails}?${queryParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response)=>{
      if(response.status === 500){
        console.log("getAssignedRequest() in allrequest",response)
        return null
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      if(data !== null && data.status === 1){
        this.setState({
          totalSize: data.data.count,
          currentPage: 1,
          currentPageData: data.data.results.map((value, index) => ({...value, srno: index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')})),
        })
      }
    })
    .catch((error)=>{
      console.log("handleSubmit() in request",error)
    })
  }

  getPaginatedAssignedRequests(page, searchText) {
    let searchParams = new URLSearchParams({
      "filter": ASSIGNEDREQUEST,
      "page": page,
      "searchtext": searchText,
      'requesttype' : this.props.requestType
    }) 
    let api_url = `${config.getAssignedRequest}?${searchParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      searchParams = new URLSearchParams({
        "filter": ASSIGNEDREQUEST,
        "page": page,
        "searchtext": searchText,
        'requesttype' : this.props.requestType,
        "action": admin_assign_request,
        "module": this.props.requesttype +' '+ admin_dashboard
      })
      api_url = `${config.getAdminRequestDetails}?${searchParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response) => {
      if (response.status === 500){
        console.log("getPaginatedInviteList (AssignedRequest.js)", response)
        return null
      } else {
        return response.json()
      }  
    }).then((response) => {
      if (response !== null && response.data === null && this.state.searchText.length > 0) {
        this.setState({
          totalSize: 0,
          currentPage: 0,
          currentPageData: []
        })
      }
      if (response !== null && response.status === 1) {
        this.setState({
          totalSize: response.data.count,
          currentPage: page,
          currentPageData: response.data.results.map((value, index) => ({...value, srno: (page - 1) * this.state.sizePerPage + index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')})),
        })
      }
    }).catch((error) => {
      console.log("getPaginatedInviteList (AssignedRequest.js)", error)
    })
  }

  onChangeHandler = (event) => {
    event.preventDefault();
    let name = event.target.name;
    let value = event.target.value;
    this.setState({ [name]: value });
  };

  mySubmitHandler = (event) => {
    event.preventDefault();
    this.setState({ isSubmit: "Yes" }, () => this.closeModal());
  };

  openModal = () => this.setState({ isOpen: true, isSubmit: "No" });

  closeModal = () => {
    if (this.state.isSubmit == "No") {
      this.setState({ isOpen: false });
      toast.warn("Operation Canceled...!", {
        position: toast.POSITION.TOP_CENTER,autoClose:1500
      });
    } 
    else {
      this.setState({ isOpen: false });
      toast.info("Data Updated...!", { position: toast.POSITION.TOP_CENTER ,autoClose:1500});
    }
  };

  onFollowChanged(row) {
    this.props.setId({ requestId: row.id });
    Router.push({
      pathname: "/RequestManager",
    });
  }

  onRejectRow = (row) => {
    this.setState({
      requestID:row.id,
      rejectPopUp:true
    })  
  }

  rejectReasonInput = (e) => {
    const regex = /[^A-Za-z0-9\s]+$/;
    if(!regex.test(e.target.value) ){
      this.setState({
        remarkNote : e.target.value
      },()=>{this.state.remarkNote})
    }
  }

  onDeleteRow = ()  => {
    if(this.state.remarkNote !== ''){
      let body = {
        "id":this.state.requestID,
        "requestStatus":CANCELREQUEST,
        "remark": `${SPOC_REJECT_NOTE} - ${this.state.remarkNote.trim()}`
      };
      let api_url = config.cancelRequest
      if (localStorage.getItem("role") == 'isSuperUser'){
        body['action'] = admin_cancel_assign
        body['module'] = this.props.requesttype +' '+ admin_dashboard
        body['remark'] = `${ADMIN_REJECT_NOTE} - ${this.state.remarkNote.trim()}`
        api_url = config.adminCancelRequest
      }
      getAPIResponse(api_url , "POST" , body)
      .then((response) => {
        if(response.status === 500){
          this.setState({
            rejectPopUp:false
          })
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("onDeleteRow() in AssignedRequest",response)
          return null 
        }
        else{
          return response.json()
        }  
      })
      .then((data) => {
        if(data !== null){
          if(data.status === 1) {
            this.setState({
              rejectPopUp:false
            })
            successToast(data.message)
            this.getPaginatedAssignedRequests(this.state.currentPage, this.state.searchText)
            window.location.reload()
          }
          else {
            console.log("onDeleteRow() in AssignedRequest",data);
            errorToast(data.message)
          }
        }else {
          console.log("onDeleteRow() in AssignedRequest",data);
        }
        })
        .catch((error) => {
          console.log("onDeleteRow() in AssignedRequest",error)
        });
    }
  }

  closeRejectpopup = () => {
    this.setState({rejectPopUp : false})
  }

  linkFollow = (cell, row, rowIndex, formatExtraData) => {
    return (
      <div>
        <a style={{ marginRight: "2px", cursor: "pointer", padding:'8px 8px'}} title="view details" className="btn-info btn-xs" onClick={() => {this.onFollowChanged(row)}}><i className="far fa-eye"></i></a>
        <a style={{ marginRight: "5px", cursor: "pointer", padding:'8px 8px' }} title ="Delete Record" className="btn-danger btn-xs" onClick={()=>{this.onRejectRow(row)}}><i className="fas fa-trash-alt"></i></a>
      </div>
    );
  };

  render() {

    return (
      <Fragment>
          <ToolkitProvider
            keyField="id"
            data={this.state.currentPageData}
            columns={this.state.headers}
            search
          >
            {(props) => (
              <div>
                <div className="text-right">
                  <SearchBar
                    {...props.searchProps}
                    className="custome-search-field"
                    placeholder="Search"
                  />
                </div>

                <BootstrapTable
                  pagination={paginationFactory({page: this.state.currentPage, sizePerPage: this.state.sizePerPage, totalSize: this.state.totalSize, hideSizePerPage: true})}
                  wrapperClasses="table-responsive"
                  striped
                  {...props.baseProps}
                  remote
                  onTableChange={(type, { page, searchText }) => {
                    if (type === SEARCH) {
                      this.setState({...this.state, searchText: searchText})
                      this.getPaginatedAssignedRequests(1, searchText)
                    }
                    if (type === PAGINATIONS) {
                      this.getPaginatedAssignedRequests(page, this.state.searchText)
                    }       
                  }}
                />
              </div>
            )}
          </ToolkitProvider>

          <Modal
                 show={this.state.rejectPopUp}
                 onHide={this.closeRejectpopup}
                 aria-labelledby="contained-modal-title-vcenter"
                 centered
                >
                  <Modal.Header>
                    <Modal.Title id="contained-modal-title-vcenter">
                      Remark
                    </Modal.Title>
                    <div style={{cursor:"pointer"}} onClick={this.closeRejectpopup}>
                      <MdCancel />  
                    </div>
                  </Modal.Header>
                  <Modal.Body>
                    <Row className="reject-header">
                      <Form.Group controlId="RequestID">
                        <Form.Label>Note<sup style={{color:'red', fontWeight:'900'}}>*</sup>:</Form.Label>
                      </Form.Group>
                      <Form.Group controlId="RequestID">
                        <Form.Label>Request ID :  </Form.Label>
                        <span>{this.state.requestID}</span>
                      </Form.Group>
                    </Row>
                    <>
                        <Form.Group controlId="remark">
                          <Form.Control 
                          as='textarea' 
                          rows={4}
                          value={this.state.remarkNote}
                          style={{resize:"none"}}
                          onChange={(e)=>{this.rejectReasonInput(e)}}
                          />
                        </Form.Group>
                    </>
                  </Modal.Body>
                  <Modal.Footer>
                    <Button variant="primary" disabled={this.state.remarkNote ? false : true} onClick={()=>{this.onDeleteRow()}}>Submit</Button>
                    <Button variant="danger" onClick={this.closeRejectpopup}>Cancel</Button>
                  </Modal.Footer>
                </Modal>

      </Fragment>
    );
  }
}
const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  requestType : state.counter.requestType
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(AssignedRequest);
