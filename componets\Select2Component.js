import React, { Component } from 'react';
import SelectSearch from 'react-select-search';
//import SelectSearch from 'react-select-search/dist/cjs';
import fuzzySearch from './FuzzySearch';



class Select2Component extends Component {  
  constructor(props) {
    super(props)
    this.state = {
      startDate: ""
    }; 
  } 
  render() {
    return (
      <SelectSearch
        options={[
          { value: 'pratik1', name: 'pratik' },
          { value: '<PERSON><PERSON>', name: '<PERSON><PERSON>' },
          { value: 'Tarade1', name: '<PERSON><PERSON>' },
          { value: 'ok1', name: 'ok' },
          { value: 'mane', name: 'mane' },    
        ]}
        search
        filterOptions={fuzzySearch}
        emptyMessage="Not found"
        placeholder={this.props.placeholderText}
        name={this.props.name}
      /> 
    );
  }

}
export default Select2Component;