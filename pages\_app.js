import App from 'next/app';
import {Provider} from 'react-redux';
import React from 'react';
import withRedux from "next-redux-wrapper";
import { PersistGate } from 'redux-persist/integration/react';
import { ToastContainer, toast } from "react-toastify";
import { configStore , persistor} from '../redux/store';
import "react-toastify/dist/ReactToastify.css";
import Router from 'next/router';
import Layout from '../componets/layout';
import "../public/css/adminlte.min.css"

class MyApp extends App {
    
    static async getInitialProps({Component, ctx}) {
        const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};
        //Anything returned here can be accessed by the client
        return {pageProps: pageProps};
    }

    render() {
        //Page props that were returned  from 'getInitialProps' are stored in the props i.e. pageprops
        const {Component, pageProps, store} = this.props;
        // const { loading } = this.state;
        return (
            <Provider store={configStore}>
                <PersistGate persistor={persistor}>
                    <ToastContainer limit={5}/>
                    <Layout>
                        <Component {...pageProps}/>
                    </Layout>
                </PersistGate>          
            </Provider>
        );
    }
}

//makeStore function that returns a new store for every request
const makeStore = () => configStore;

//withRedux wrapper that passes the store to the App Component
export default withRedux(makeStore)(MyApp);
// export default MyApp

