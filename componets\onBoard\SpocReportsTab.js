import React, { Component } from "react";
import { connect } from "react-redux";
import { setId} from "../../redux/actions/counterActions";
import CreateTabs from "./CreateTabs";
import UpdateTabs from "./UpdateTabs";
import DisableTabs from "./DisableTabs";
import Box from "./Box";
import SpocReportTable from "./SpocReportTable";


class SpocReportsTab extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      activeKeyCreate: "invite",
      activeKeyUpdate:"assigned",
      activeKey:"invite"
    };
  }

  handleShow = (e) => {
    this.setState({
      activeKey: e,
    });

  };

  render() {
    const userRole = this.props.loginData.userRole;
    const empId = this.props.loginData.empId;
    return (
        <div>
          <SpocReportTable/>
          {/* <Box baseUrl={this.props.baseUrl} />
          {this.props.requestType === 'Create' ? <CreateTabs/> :this.props.requestType == 'Update'? <UpdateTabs/> : <DisableTabs/>} */}
        </div>
      );
    } 
  }

const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
  baseUrl: state.counter.baseUrl,
  isLogin: state.loginInfo.isLogin,
  loginData: state.loginInfo.login,
  requestType: state.counter.requestType
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(SpocReportsTab);
