import React, { Fragment } from "react";
import { Component } from "react";
import { connect } from "react-redux";
import { setId } from "../../redux/actions/counterActions";
import BootstrapTable from "react-bootstrap-table-next";
import paginationFactory from "react-bootstrap-table2-paginator";
import Toolk<PERSON><PERSON>rovider, { Search } from "react-bootstrap-table2-toolkit";
const { SearchBar } = Search;
import Router from "next/router";
import { getAPIResponse } from "../../constants/Utils";
import * as config from '../../constants/config';
import moment from "moment";
import Select from "react-select";
import { RequestType, admin_approval_dashboard, sizePerPage,admin_approved_request } from "../../constants/constants";
import { APPROVEDREQUEST, INTERNAL_SERVER_ERROR } from "../../constants/message";
import { errorToast } from "../../toast"

class IdTable extends Component {
  static getInitialProps({ store }) { }

  constructor(props) {
    super(props);
    this.state = {
      response: "",
      updateObj: "",
      headers: [
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "id", text: "Request ID", sort: true },
        { dataField: "fullName", text: "Person Name", sort: true },
        { dataField: "tmlManager", text: "Reporting Manager", sort: true },
        { dataField: "createdBy", text: "Created By", sort: true },
        { dataField: "createdAt", text: "Created At", sort: true },
        { dataField: "status", text: "Request Status", sort: true },
        { dataField: "requestType", text: "Request Type", sort: true },
        { dataField: "approvedBy", text: "Approved By", sort: true },
        {dataField: "Action",text: "Action",formatter: this.linkFollow,sort: true}
      ],
      dataArray: [],
      requestId: "",
      currentPage: 0,
      currentPageData: [],
      sizePerPage: sizePerPage, 
      totalSize: 0,
      searchText: '',
      selectOptions : RequestType,
      requestType:"All",
      selectedValue:{ label: 'All', value: 'All' },
    };
    this.onChangeHandler = this.onChangeHandler.bind(this);
  }

  componentDidMount() {
    this.getManagerAcceptId(this.state.requestType)
  }

  getManagerAcceptId = (requestType) => {
    let queryParams = new URLSearchParams({
      "filter": APPROVEDREQUEST,
      "requesttype":requestType
    })
    let api_url = `${config.managerAllrequest}?${queryParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      let queryParams = new URLSearchParams({
        "filter": APPROVEDREQUEST,
        "requesttype":requestType,
        "action": admin_approved_request,
        "module": requestType +' '+ admin_approval_dashboard
      })
      api_url = `${config.adminApprovalrequest}?${queryParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
      .then((response) => {
        if (response.status === 500) {
          errorToast(INTERNAL_SERVER_ERROR)
          console.log("getManagerAcceptId() in IdTable", response)
          return null
        }
        return response.json()
      })
      .then((data) => {
        if (data !== null && data.status === 1){
          this.setState({
            totalSize: data.data.count,
            currentPage: 1,
            currentPageData: data.data.results.map((value, index) => ({...value, srno: index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')}))
          })
        }
       
      })
      .catch((error) => {
        console.log("getManagerAcceptId() in IdTable", error)
      })
  }

  getPaginatedApprovedRequests(page, searchText) {
    let searchParams = new URLSearchParams({
      "filter": APPROVEDREQUEST,
      "page": page,
      "searchtext": searchText,
      "requesttype":this.state.requestType
    }) 
    let api_url = `${config.managerAllrequest}?${searchParams.toString()}`
    if (localStorage.getItem("role") == 'isSuperUser'){
      searchParams = new URLSearchParams({
        "filter": APPROVEDREQUEST,
        "page": page,
        "searchtext": searchText,
        "requesttype":this.state.requestType,
        "action": admin_approved_request,
        "module": this.state.requestType +' '+ admin_approval_dashboard
      })
      api_url = `${config.adminApprovalrequest}?${searchParams.toString()}`
    }
    getAPIResponse(api_url , "GET" , {})
    .then((response) => {
      if (response.status === 500){
        console.log("getPaginatedApprovedRequests (IdTable.js)", response)
        return null
      } else {
        return response.json()
      }  
    }).then((response) => {
      if (response !== null && response.data === null && this.state.searchText.length > 0) {
        this.setState({
          totalSize: 0,
          currentPage: 0,
          currentPageData: []
        })
      }
      if (response !== null && response.status === 1) {
        this.setState({
          totalSize: response.data.count,
          currentPage: page,
          currentPageData: response.data.results.map((value, index) => ({...value, srno: (page - 1) * this.state.sizePerPage + index + 1,createdAt:moment(value.createdAt).format('DD-MM-YYYY HH:mm')})),
        })
      }
    }).catch((error) => {
      console.log("getPaginatedApprovedRequests (IdTable.js)", error)
    })
  }

  //  common function

  onChangeHandler = (event) => {
    event.preventDefault();
    let name = event.target.name;
    let value = event.target.value;
    this.setState({ [name]: value });
  };


  linkFollow = (cell, row, rowIndex, formatExtraData) => {
    return (
      <div>
        <a onClick={() => { this.goNext(row); }} style={{ cursor: "pointer",padding:'8px 8px' }} className="btn-info btn-xs" target="_blank"> <i className="far fa-eye"></i> </a>
      </div>
    );
  };

  goNext(row) {
    this.props.setId({ requestId: row.id });
    Router.push({
      pathname: "/UpdateIdPage",
    });
  }

  render() {
  
    return (
      <div>
        <ToolkitProvider
          keyField="id"
          data={this.state.currentPageData}
          columns={this.state.headers}
          search
        >
          {(props) => (
            <div>
                <div>
                  <div className="text-right">
                    <div className="table-dropdown ">
                      <Select
                      options={this.state.selectOptions}
                      value={this.state.selectedValue}
                      onChange={(e)=>{this.setState({requestType : e.value, selectedValue:e},()=>{this.getPaginatedApprovedRequests(this.state.currentPage , this.state.searchText)})}}
                      isSearchable
                      name="requestType"
                      placeholder="Select Request Type"
                      className="myclass"
                      noOptionsMessage={({ inputValue }) =>
                        "No results found"
                      }
                      // required
                      />
                    </div>
                    <SearchBar
                      {...props.searchProps}
                      className="custome-search-field"
                      placeholder="Search"
                    />
                  </div>
                </div>

              <BootstrapTable
                pagination={paginationFactory({page: this.state.currentPage, sizePerPage: this.state.sizePerPage, totalSize: this.state.totalSize, hideSizePerPage: true})}
                wrapperClasses="table-responsive"
                striped
                noDataIndication={"No data to display"}
                {...props.baseProps}
                remote
                onTableChange={(type, { page, searchText }) => {
                  if (type === 'search') {
                    this.setState({searchText: searchText})
                    this.getPaginatedApprovedRequests(1, searchText)
                  }
                  if (type === 'pagination') {
                    this.getPaginatedApprovedRequests(page, this.state.searchText)
                  }
                }}       
              />
            </div>
          )}
        </ToolkitProvider>
      </div>
    );
  }
}
const mapStateToProps = (state) => ({
  requestId: state.counter.requestId,
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(IdTable);
