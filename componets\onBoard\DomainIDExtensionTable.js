import React, { Fragment } from "react";
import { Component } from "react";
import { toast } from "react-toastify";
import Router from "next/router";
import BootstrapTable from "react-bootstrap-table-next";
import paginationFactory from "react-bootstrap-table2-paginator";
import ToolkitProvider, { Search } from "react-bootstrap-table2-toolkit";
const { SearchBar } = Search;
import { connect } from "react-redux";
import { setId } from "../../redux/actions/counterActions";
import { getAPIResponse } from '../../constants/Utils'
import { Spinner, fieldset, Modal, Col, Card, Container, Row, Form, } from "react-bootstrap";
import { MdCancel } from "react-icons/md";
import * as config from '../../constants/config'
// import * as constants from '../../constants/constants'
import {sizePerPage,admin_spoc_report,admin_spoc_report_list, emptyDataMessage, admin_spoc_report_mail} from '../../constants/constants'
import moment from "moment";
import { PAGINATIONS, SEARCH, SOMETHING_WENT_WRONG,PLEASE_ENTER_REQUIRED_FIELD,MINDEFAULTVALUE,INTERNAL_SERVER_ERROR } from "../../constants/message";
import { successToast, errorToast } from "../../toast"
import { Button, DatePicker, Select, Switch } from "antd";
import { AiOutlineClose } from "react-icons/ai";
import dayjs from "dayjs";
import { FaEnvelope,FaEye, FaEyeSlash } from "react-icons/fa";
import { Tooltip } from 'antd';

const { RangePicker } = DatePicker

class DomainIDExtensionTable extends Component {
  static getInitialProps({ store }) {}
  constructor(props) {
    super(props);
    this.state = {
      response: "",
      updateObj: "",
      selectedRows:new Set(),
      allSelected:false,
      isExtend:false,
      dataArray: [],
      totalcomplete: "",
      currentPage: 0,
      currentPageData: [],
      sizePerPage: sizePerPage, 
      totalSize: 0,
      searchText: '',
      isSendDisable:false,
      showModal:false,
      spocEmail:this.props.loginData.userEmail,
      dataRange:3,
      fromDate:dayjs(),
      toDate:dayjs().add(3, 'month'),
      showDefaultDateRange: true,
      minDate:dayjs().subtract(3,'month'),
      maxDate:dayjs().add(6,'month'),
      userRole: localStorage.getItem("role"),
      vendorOption:[{"value": "all","label": "All Vendors"}],
      projectOption:[{"value": "all","label": "All Projects"}],
      ActiveOption:[
        {"value": "all","label": "All"},
        { "value": true, "label": "Active" },
        { "value": false, "label": "In-active " }
      ],
      activeStatus:'all',
      vendor:'all',
      vendorErrorMessage:"",
      project:'all',
      projectErrorMessage:"",
      isLoading:false,
      totalRecords:0,
      extendPopUp:false,
      extendDate : new Date(),
      successDomain:0,
      failedDomin:0,
      isDataProcessing:false,
      apiHit:false,
      idExtended:false,
      message : "Processing the Domain IDs ...",
      allEmployeeData : [],
      extendValidityTo: "",
      disableProject: true,
      isDataMasked:true
    };

  }
  closeExtendpopup = () => {
    console.log("close extend popup");
    let currentDate = new Date();
    let newDate = new Date();
    newDate.setMonth(currentDate.getMonth() + 6);
    this.setState({ extendPopUp: false, extendDate: newDate.toISOString().split("T")[0]});
  };
  componentDidMount() {
    this.getDomainIDExtensionTable()
    this.getDomainIDExtension()
    let currentDate = new Date();
    let newDate = new Date();
    newDate.setMonth(currentDate.getMonth() + 6);
    this.setState({ extendDate: newDate.toISOString().split("T")[0] });
    console.log("this.props.loginData.userRole",this.props,localStorage.getItem("role"))
    if((this.state.userRole === 'isSuperUser')||(this.state.userRole === 'isTmlManager')||(this.state.userRole === 'isNonTmlManager'))
      this.getVendorList()
    if((this.state.userRole === 'isSpoc'))
      this.setState({ disableProject:false})
      this.getProjectList(this.props.loginData.vendorName)
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevProps.requestType !== this.props.requestType) {
      this.getDomainIDExtensionTable();
      this.getDomainIDExtension()
    }
  
    // Compare Sets correctly
    const prevSelectedArray = Array.from(prevState.selectedRows);
    const currentSelectedArray = Array.from(this.state.selectedRows);
  
    if (JSON.stringify(prevSelectedArray) !== JSON.stringify(currentSelectedArray)) {
      this.getPaginatedDomainIDExtensionTables(this.state.currentPage, this.state.searchText);
    }
  }
  
handleRowSelect = (rowId) => {
  this.setState((prevState) => {
      const newSelectedRows = new Set(prevState.selectedRows);
      if (newSelectedRows.has(rowId)) {
          newSelectedRows.delete(rowId); // Deselect
      } else {
          newSelectedRows.add(rowId); // Select
      }
      let idExtended = false
      if (newSelectedRows.size > 0){
        idExtended = true
      }
      return { 
          isExtend : idExtended,
          selectedRows: new Set(newSelectedRows),  // Creating a new instance ensures reactivity
          allSelected: newSelectedRows.size === prevState.allEmployeeData.length 
      };
  });
};

handleSelectAll = () => {
  this.setState((prevState) => {
    const { allSelected, allEmployeeData } = prevState;
    let newSelectedRows = new Set();

    if (!allSelected) {
      allEmployeeData.forEach((row) => newSelectedRows.add(row.domain));
    }

    return { 
      selectedRows: new Set(newSelectedRows), 
      allSelected: !allSelected,
      isExtend: !allSelected
    };
  });
};

  getDomainIDExtension = () => {
    this.setState({
      isLoading : true
    })
    let body = {
      "active": true,
      "vendor": this.state.vendor.toLowerCase() != 'all' ? this.state.vendor :'',
      "project": this.state.project.toLowerCase() != 'all' ? this.state.project :''
    }
    let api_url = config.doaminIDExtensionEmpDetails
    if((this.state.userRole === 'isSuperUser')||(this.state.userRole === 'isTmlManager')||(this.state.userRole === 'isNonTmlManager')){
      if (this.state.vendor !== 'all') body['vendor'] = this.state.vendor
      if (this.state.userRole === 'isSuperUser'){
        body['action'] = admin_spoc_report_list
        body['module'] = admin_spoc_report
        api_url = config.adminSpocEmpDetails
      }
    }
    else{
      body['vendor'] = this.props.loginData.vendorName.toUpperCase()
    }

    getAPIResponse(api_url, "POST", body)
    .then((response)=>{
      if(response.status === 500){
        console.log("getDomainIDExtension() in doaminIDExtensionEmpDetails",response)
        return null
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      if (data !== null && data.status === 1){
        data.data.map((value, index) => ({...value, isActive:<Switch disabled checked={value.isActive}/>}))
        this.setState({
          allEmployeeData: data.data.map((value, index) => ({...value, srno: index + 1,validFrom:moment(value.validFrom).format('DD-MM-YYYY'),validTill:moment(value.validTill).format('DD-MM-YYYY'), Active:<Switch disabled checked={value.isActive}/>}))
        })
      }
    })
    .catch((error)=>{
      errorToast(SOMETHING_WENT_WRONG)
      console.log("getDomainIDExtension() in doaminIDExtensionEmpDetails",error)
    })
    this.setState({
      isLoading : false
    })
  }


  getDomainIDExtensionTable = () => {
    this.setState({
      isLoading : true
    })
    let body = {
      "pagination":true,
      "page_number":1,
      "active": true,
      "vendor": this.state.vendor.toLowerCase() != 'all' ? this.state.vendor :'',
      "project": this.state.project.toLowerCase() != 'all' ? this.state.project :''
    }
    let api_url = config.doaminIDExtensionEmpDetails
    if((this.state.userRole === 'isSuperUser')||(this.state.userRole === 'isTmlManager')||(this.state.userRole === 'isNonTmlManager')){
      if (this.state.vendor !== 'all') body['vendor'] = this.state.vendor
      if (this.state.userRole === 'isSuperUser'){
        body['action'] = admin_spoc_report_list
        body['module'] = admin_spoc_report
        api_url = config.adminSpocEmpDetails
      }
    }
    else{
      body['vendor'] = this.props.loginData.vendorName.toUpperCase()
    }

    getAPIResponse(api_url, "POST", body)
    .then((response)=>{
      if(response.status === 500){
        console.log("getDomainIDExtensionTable() in doaminIDExtensionEmpDetails",response)
        return null
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      if (data !== null && data.status === 1){
        data.data.results.map((value, index) => ({...value, isActive:<Switch disabled checked={value.isActive}/>}))
        this.setState({
          totalSize: data.data.count,
          currentPage: 1,
          currentPageData: data.data.results.map((value, index) => ({...value, srno: index + 1,
            maskedEmail:value.email.replace(/^(.)(.*)(.@.*)$/, (_, first, middle, last) => 
              first + "*".repeat(middle.length) + last
            ),
            unmaskedEmail:value.email,
            email:value.email.replace(/^(.)(.*)(.@.*)$/, (_, first, middle, last) => 
              first + "*".repeat(middle.length) + last
            ),
            validFrom:moment(value.validFrom).format('DD-MM-YYYY'),
            validTill:moment(value.validTill).format('DD-MM-YYYY'), 
            Active:<Switch disabled checked={value.isActive}/>,
          select: (
            <input
              type="checkbox"
              checked={this.state.selectedRows.has(value.domain)} 
              onChange={() => this.handleRowSelect(value.domain)}
            />
          )      
          }))
          // currentPageData: data.data.results.map((value, index) => (console.log("value",value)))
        })
      }
    })
    .catch((error)=>{
      errorToast(SOMETHING_WENT_WRONG)
      console.log("getDomainIDExtensionTable() in doaminIDExtensionEmpDetails",error)
    })
    this.setState({
      isLoading : false
    })
  }

  getPaginatedDomainIDExtensionTables(page, searchText) {
    this.setState({
      isLoading : true
    })
    let body = {
      "pagination":true,
      "page_number":page,
      "search_text":searchText,
      "active": true,
      "vendor": this.state.vendor.toLowerCase() != 'all' ? this.state.vendor :'',
      "project": this.state.project.toLowerCase() != 'all' ? this.state.project :''
    }
    let api_url = config.doaminIDExtensionEmpDetails
    if((this.state.userRole === 'isSuperUser')||(this.state.userRole === 'isTmlManager')||(this.state.userRole === 'isNonTmlManager')){
      if (this.state.vendor !== 'all') body['vendor'] = this.state.vendor
      if (this.state.userRole === 'isSuperUser'){
        body['action'] = admin_spoc_report_list
        body['module'] = admin_spoc_report
        api_url = config.adminSpocEmpDetails
      }
    }
    else{
      body['vendor'] = this.props.loginData.vendorName.toUpperCase()
    }
    getAPIResponse(api_url, "POST", body).then((response)=>{
      if (response.status === 500){
        console.log("getPaginatedDomainIDExtensionTables (FulfilledRequest.js)", response)
        return null
      } else {
        return response.json()
      }  
    }).then((response) => {
      if (response !== null && response.data === null && this.state.searchText.length > 0) {
        this.setState({
          totalSize: 0,
          currentPage: 0,
          currentPageData: []
        })
      }
      if (response !== null && response.status === 1) {
        response.data.results.map((value, index) => (console.log("values pages",value.isActive)))
        this.setState({
          totalSize: response.data.count,
          currentPage: page,
          currentPageData: response.data.results.map((value, index) => ({
            ...value,
            srno: (page - 1) * this.state.sizePerPage + index + 1,
            validFrom: moment(value.validFrom).format('DD-MM-YYYY'),
            validTill: moment(value.validTill).format('DD-MM-YYYY'),
            Active: <Switch disabled checked={value.isActive} />,
            select: (
              <input
                type="checkbox"
                checked={this.state.selectedRows.has(value.domain)} 
                onChange={() => this.handleRowSelect(value.domain)}
              />
            )        
        }))
        })
      }
    }).catch((error) => {
      errorToast(SOMETHING_WENT_WRONG)
      console.log("getPaginatedDomainIDExtensionTables (FulfilledRequest.js)", error)
    })
    this.setState({
      isLoading : false
    })
  }
  handleExtension=()=>{
    console.log("extend ---------")
    this.setState({extendPopUp : true})
  }

  extendDomainIds =() =>{
    this.setState({isDataProcessing : true, apiHit:true})
    console.log(this.state.selectedRows, "selected rows");
    
    this.setState({
      isLoading : true
    })
    if (this.state.extendDate == null || this.state.extendDate == undefined){
      toast.error("Invalid Date Format",{position:toast.POSITION.TOP_CENTER , autoClose:1500})
    }
    else if (this.state.extendDate == ""){
      toast.error("Date is Empty",{position:toast.POSITION.TOP_CENTER , autoClose:1500})
    }
    const selectedRowsArray = Array.from(this.state.selectedRows);
    let body = {
      "domain_ids": selectedRowsArray,
      "extended_date":this.state.extendDate
    }
    let api_url = config.extendDomainIDs
    
    getAPIResponse(api_url, "POST", body)
    .then((response)=>{
      if(response.status === 500){
        console.log("extendDomainIds() in doaminIDExtensionEmpDetails",response)
        return null
      }
      else{
        return response.json()
      }  
    })
    .then((data)=>{
      if(data !== null){
        if(data.status === 1){
          this.setState({ extendPopUp : false, isDataProcessing: false, isExtend: false, isLoading : false, selectedRows:new Set(), allSelected:false, apiHit:false})
          successToast(data.message)
          this.getDomainIDExtensionTable();
          this.getDomainIDExtension()
        }
        else {
          this.setState({ extendPopUp : false, isDataProcessing: false, isExtend: false, isLoading : false, selectedRows:new Set(), allSelected:false, apiHit:false})
          this.setState({
            isLoading:false
          })
          errorToast(data.message)
        }
      }
    })
    .catch((error)=>{
      this.setState({ isDataProcessing: false})
      errorToast(SOMETHING_WENT_WRONG)
      console.log("extendDomainIds()",error)
    })
    this.setState({
      isLoading : false
    })
    let currentDate = new Date();
    let newDate = new Date();
    newDate.setMonth(currentDate.getMonth() + 6);
    this.setState({ extendDate: newDate.toISOString().split("T")[0]});
  }
  
  getVendorList = () => {
    getAPIResponse(config.getVendorList , "GET" , {})
    .then((response) => {
      if(response.status === 500){
        errorToast(INTERNAL_SERVER_ERROR)
        console.log("getVendorList() in Request",response)
        return null
      }
      else{
        return response.json()
      }
    }).then((data) => {
      if(data !== null && data.status === 1){
        let vendorData = data.data.map((data)=>({value:data.vendorCode,label:data.vendorName.toUpperCase()}))
        vendorData.splice(0, 0, {"value": "all","label": "All Vendors"});

        this.setState({
          vendorOption : vendorData
        })
      }
      else {
        console.log("getVendorList() in Request",response)
      }
    })
    .catch((error) => {
      console.log("getVendorList() in Request",error)
    })
  }

  getProjectList = (vendor) => {
    if (vendor !== null) {
      let queryParams = new URLSearchParams({
        'vendor': vendor
      })
      getAPIResponse(`${config.getProjectList}?${queryParams.toString()}`, "GET", {})
        .then((response) => {
          if (!response.ok) {
            return null
          }
          else {
            return response.json()
          }
        }).then((data) => {
          if (data !== null) {
            data.data.map((obj, index) => {
              let projectData = data.data.map((data)=>({value:data.projectCode,label:data.projectName.toUpperCase()}))
              projectData.splice(0, 0, {"value": "all","label": "All Projects"});

              this.setState({
                projectOption : projectData 
              })
            })
          }
          else {
            console.log("getProjectList() in Request", response)
          }
        })
        .catch((error) => {
          console.log("getProjectList() in Request", error)
        })
    }
  }
  onHandleVendorDropdown = (e) => {
    this.setState({
        vendor:e,
        vendorErrorMessage:"",
        disableProject:!(e != 'all'),
        project:'all',
        selectedRows:new Set(),
        allSelected:false,
        isExtend:false
    },()=>
      {
        if (e != 'all') {
          this.getProjectList(e)
        }
        this.getDomainIDExtensionTable()
        this.getDomainIDExtension()
      })
  }

  onHandleProjectDropdown = (e) => {
    console.log("Project:",e)
    this.setState({
        project : e,
        projectErrorMessage:"",
        selectedRows:new Set(),
        allSelected:false,
        isExtend:false
    },()=>{
      this.getDomainIDExtensionTable()
      this.getDomainIDExtension()
    })
  }

  onHandleActiveDropdown = (e) => {
    this.setState({
        activeStatus:e,
        vendorErrorMessage:""
    },()=>{
      this.getDomainIDExtensionTable()
      this.getDomainIDExtension()
    })
  }

  getHeaders = () => {
    let headers = [
        { dataField: "select", text: "Select" },
        { dataField: "srno", text: "Sr No.", sort: true },
        { dataField: "employeeId", text: "Employee ID", sort: true },
        // { dataField: "domain", text: "Domain ID", sort: true },
        { dataField: "fullName", text: "Person Name", sort: true },
        { dataField: "email", text: "Person Email", sort: true },
        { dataField: "projectName", text: "Project", sort: true },
        { dataField: "tmlManagerEmail", text: "TML/TTL Manager", sort: true },
        { dataField: "validFrom", text: "Account Start", sort: true },
        { dataField: "validTill", text: "Account Expiry", sort: true },
        { dataField: "daysRemainig", text: "Active Days remaining", sort: true },
        { dataField: "Active", text: "Active status", sort: true }
      ]
    return headers
  }
  // customHeaderRenderer = () => {
  //     return <span>Select</span>; // Displays "Select" instead of a checkbox
  // };
  handleDate = (value) => {
    console.log(value);
    let currentDate = new Date();
    const date = ![null, ""].includes(value) ? dayjs(value).format("YYYY-MM-DD") : dayjs(new Date(currentDate.setMonth(currentDate.getMonth() + 6))).format("YYYY-MM-DD");
    console.log(date, "date")
    this.setState({
      extendDate: date
    })
  }

  handleMasking = ()=>{
    this.setState(prevState=>({...prevState,isDataMasked:!prevState.isDataMasked,currentPageData:prevState.currentPageData.map(obj=>({...obj,email:!prevState.isDataMasked ? obj.maskedEmail:obj.unmaskedEmail }))}))
  }

  render() {
    
    const tableColumns=this.getHeaders()
    const selectRow = {
      mode: 'checkbox', // Enables row selection
      clickToSelect: true, // Allows clicking the row to select
      hideSelectAll: true, // Hides the "Select All" checkbox in the header
      selectColumnPosition: 'left', // Ensures checkbox stays in the first column
      selectionHeaderRenderer: () => <span>Select</span>, // Adds "Select" in the header
      onSelect: (row, isSelect, rowIndex, e) => {
          console.log("Selected Row:", row);
      },
      onSelectAll: (isSelect, rows) => {
          console.log("All Selected Rows:", rows);
      }
  };
  
  
    return (
      <Fragment>
          <ToolkitProvider
            keyField="id"
            data={this.state.currentPageData}
            columns={tableColumns}
            search
          >
            {(props) => (
              <div>
                
                <div className="validate-spoc-email">
                <div className="align-center"> 
                    <div className={!this.state.currentPageData.length==0 ? 'button-select-all btn-select':'button-select-all'} onClick={!this.state.currentPageData.length==0 ?this.handleSelectAll: null} >
                        {this.state.allSelected ? "Deselect All" : "Select All"}
                    </div>
                    {/* <div className={!this.state.currentPageData.length==0 ? 'button-select-all btn-select':'button-select-all'} onClick={this.handleMasking}>
                        {this.state.isDataMasked ? <FaEye/> : <FaEyeSlash/>}
                    </div> */}
                    <div className={this.state.currentPageData.length !== 0? 'button-select-all btn-select': 'button-select-all'}onClick={this.handleMasking}>
                        <Tooltip title={this.state.isDataMasked ? 'Show' : 'Hide'} color="#FFF" overlayInnerStyle={{ color: '#000000' }}>
                          <div>
                            {this.state.isDataMasked ? <FaEye /> : <FaEyeSlash />}
                          </div>
                        </Tooltip>
                      </div>
                  </div>
                  <div className="text-right" >
                  { (this.state.userRole === 'isSuperUser')||(this.state.userRole === 'isTmlManager') ?
                    <Select className="input-box-select" value={this.state.vendor} placeholder="Please Select Vendor" options={this.state.vendorOption} onChange={(e) => { this.onHandleVendorDropdown(e) }} disabled={this.state.isValidUser} />
                    : null
                  }
                  {
                    (this.state.userRole === 'isSuperUser') || (this.state.userRole === 'isTmlManager') || (this.state.userRole === 'isSpoc') ?
                      <Select className="input-box-select" value={this.state.project} placeholder="Please Select Project" options={this.state.projectOption} onChange={(e) => { this.onHandleProjectDropdown(e) }} disabled={this.state.disableProject} />
                      : null
                  }
                    <SearchBar
                      {...props.searchProps}
                      className="custome-search-field"
                      placeholder="Search"
                    />
                    <div className="align-center"> 
                    <div className={ this.state.isExtend ? 'button-extend extend' : 'button-extend'} onClick={()=>{this.state.isExtend ? this.handleExtension() : null}}>
                        {"Extend"}
                    </div>
                  </div>
                  </div>
                </div>
              <BootstrapTable
                keyField="domain"
                pagination={paginationFactory({
                  page: this.state.currentPage,
                  sizePerPage: this.state.sizePerPage,
                  totalSize: this.state.totalSize,
                  hideSizePerPage: true,
                })}
                wrapperClasses="table-responsive"
                striped
                {...props.baseProps}
                remote
                columns={tableColumns}
                // selectRow={selectRow} 
                onTableChange={(type, { page, searchText }) => {
                  console.log("Props:::", props?.baseProps ?? "No baseProps available");

                  if (type === SEARCH) {
                    this.setState({ searchText });
                    this.getPaginatedDomainIDExtensionTables(1, searchText);
                  }

                  if (type === PAGINATIONS) {
                    this.getPaginatedDomainIDExtensionTables(page, this.state.searchText);
                  }
                }}
                noDataIndication={emptyDataMessage()}
              />
              </div>
            )}
          </ToolkitProvider>
          {
            this.state.isLoading ?
              <div className="loader">
                <div></div>
              </div> : null
          }
          <Modal
                 show={this.state.extendPopUp}
                 onHide={() => this.closeExtendpopup()}
                 aria-labelledby="contained-modal-title-vcenter"
                 centered
                >
                  <Modal.Header>
                    <Modal.Title id="contained-modal-title-vcenter">
                      Domain ID Extension
                    </Modal.Title>
                    <div style={{cursor:"pointer"}} onClick={this.state.isDataProcessing ? null : () => this.closeExtendpopup()}>
                      <MdCancel />  
                    </div>
                  </Modal.Header>
                  <Modal.Body>
                    

                    {this.state.apiHit?
                    <>
                    <Row>
                      <Col>
                      <div className="process-domin">
                      {this.state.message }
                      </div>
                      </Col>
                      
                    </Row> </>: <Row className="reject-header">
                      <Form.Group controlId="RequestID">
                      <Form.Label><p className={'my-1'}>{"Select Extension date"}</p></Form.Label>
                      <DatePicker
                        style={{ fontFamily: 'sans-serif' }}
                        className="form-control"
                        name="bulkExtensionDate"
                        placeholderText="Extend Valid To"
                        minDate={dayjs().add(1, 'day')}
                        maxDate={dayjs().add(6, 'month').add(1, 'day')}
                        dateFormat="yyyy-MM-dd"
                        value={dayjs(this.state.extendDate)}
                        onChange={(e) => { this.handleDate(e) }}
                      />
                        <Form.Label className={'mt-3'}><b style={{color:'red'}}>DISCLAIMER: </b>{"If No Date selection is done then by default, the extension date is set to "}<b>{" six months from today"}</b></Form.Label>
                        <Form.Label>Selected <b>{Array.from(this.state.selectedRows).length}</b> Domain IDs will be extended till <b>{this.state.extendDate}</b></Form.Label>
                      </Form.Group>
                    </Row>
                    }
                  </Modal.Body>
                  <Modal.Footer>
                    <div className={ this.state.isDataProcessing ? 'button-extend':'button-extend extend'} onClick={()=>{this.extendDomainIds()}}>
                          {"Extend"}
                      </div>
                    <div className={this.state.isDataProcessing ? 'button-extend':'button-extend extend'} onClick={()=>{this.closeExtendpopup()}}>
                          {"Close"}
                      </div>
                  </Modal.Footer>
                </Modal>
      </Fragment>
    );
  }
}
const mapStateToProps = (state) => ({
  loginData: state.loginInfo.login
});

const mapDispatchToProps = {
  setId: setId,
};
export default connect(mapStateToProps, mapDispatchToProps)(DomainIDExtensionTable);
