import { Component } from 'react';
import Sidebar from '../componets/Sidebar';
import Footer from '../componets/Footer';
import Header from '../componets/Header';
import EmployeeRequest from '../componets/onBoard/EmployeeRequest';
import { connect } from 'react-redux';
import { setId } from '../redux/actions/counterActions';
import { login } from '../redux/actions/loginActions';
import Image from 'next/image';
import logo from '../public/img/logo.png'
import { Tabs, Tab, ListGroup, Badge, Table, Alert, FormControl, InputGroup, fieldset, Modal, Col, Card, Container, Row, Button, Form } from 'react-bootstrap';

class Employee extends Component {

    static getInitialProps({ store }) { }

    constructor(props) {
        super(props);
        this.state = {
            request : ""
        }
    }
    componentDidMount(){

    }

    render() {
        return (
            <div className="wrapper">
                <div className="" >
                    <div className="employee-header">
                        <div className="row">
                            <div className="col-md-12 employee-container">
                                <ol className="breadcrumb align-center">
                                    <div>
                                        <Image
                                        src={logo}
                                        alt='logo'
                                        width={35}
                                        height={30}
                                        />
                                    </div>
                                    <div className='app-name'>
                                        <div>Partner Onboarding</div>
                                    </div>

                                </ol>
                            </div>
                        </div>
                    </div>

                    <div className="content">
                        <div className="container-fluid">
                            <EmployeeRequest baseUrl={this.props.baseUrl} request={this.state.request}/>
                        </div>
                    </div>
                </div>
                <div className='footer-section'>
                    <Footer/>
                </div>
            </div>
        )
    }
}

const mapStateToProps = state => ({
    requestId: state.counter.requestId,
    baseUrl: state.counter.baseUrl,
});

const mapDispatchToProps = {
    setId: setId,
    login:login
};
export default connect(mapStateToProps, mapDispatchToProps)(Employee);
