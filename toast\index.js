import { toast, Slide } from "react-toastify";

const toastOptions = {
    position: "top-center",
    autoClose: 2000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    theme: "light",
    transition: Slide,
}

function successToast(toastContent) {
    console.log('successToast', toastContent)
    toast.success(toastContent, toastOptions)
}

function errorToast(toastContent) {
    console.log('errorToast', toastContent)
    toast.error(toastContent, toastOptions)
}

export { successToast, errorToast } 
