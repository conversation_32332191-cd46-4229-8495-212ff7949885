version: "3"

services:
  nda-frontend:
    image: nda-frontend
    container_name: nda-frontend
    command: sh -c "npm run build && npm start"
    volumes:
       - ./componets:/app/componets
       - ./constants:/app/constants
       - ./pages:/app/pages
       - ./public:/app/public
       - ./redux:/app/redux
       - ./styles:/app/styles
       - ./toast:/app/toast
    ports:
      - "3001:3000"
    restart: always
